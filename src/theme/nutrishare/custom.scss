.theme-nutrishare {

  .home-page {
    .user-msg-welcome {
      color: var(--ion-color-skin-dark) !important;

      span {
        color: var(--ion-color-skin-main-light) !important;
      }
    }
  }
  
  #login-form {
    #forgot-password,
    #click-here {
      text-decoration: underline;
      color: var(--ion-color-skin-main-light) !important;
    }
  }

  .event-popup{
    .ion-color-primary{
      --ion-color-base: var(--ion-color-skin-main) !important;
    }
    .ion-color-success{
      --ion-color-success: var(--ion-color-skin-main) !important;
    }
    
  }

  .button-group {
    #btn-save-as-draft {
        --background:  var(--ion-color-skin-main) !important;
    }
  }

  .common-swipe-buttons .flag {
    --background: var(--ion-color-skin-main);
  }

  .common-swipe-buttons .forward {
    --background: var(--ion-color-skin-main-light);
  }

  .common-swipe-buttons .archive,
  .cancel {
    --background: var(--ion-color-skin-main);
  }

  .common-swipe-buttons .resend {
    background: var(--ion-color-skin-main-light);
  }

  ion-list .item.unread,
  .message-display.unread {
    border-left: 5px solid var(--ion-color-skin-main) !important;

    .set-heading-color,
    .inbox-per-name,
    .data-list-title,
    .list-item-docname,
    label.list-item-docname {
      color: var(--ion-color-skin-main) !important;
    }
  }
  .message-display.unread {
    --background: var(--ion-color-white) !important;

  }

  ion-segment ion-segment-button {
    --color: var(--ion-color-skin-main-lighter) !important;
    --background-checked: var(--ion-color-skin-main-lighter) !important;
    --background-hover: var(--ion-color-skin-main-lighter) !important;
    --ion-background-color: var(--ion-color-skin-main-lighter) !important;
  }

  .common-header {
    background-color: var(--ion-color-skin-main) !important;
}

  .common-header .header-menu {
    --ion-toolbar-color: var(--ion-color-white);
  }

  ion-header ion-toolbar ion-buttons ion-button {
    color: var(--ion-color-white);
  }

  .common-back-btn-icon {
    color: var(--ion-color-white);
  }

  .common-footer {
    --ion-toolbar-background: #808285 !important;
    ion-icon,
    label {
      color: var(--ion-color-white) !important;
    }
  }

  .msg-info-icon {
    ion-icon {
      background: var(--ion-color-skin-main-lighter) !important;
    }
  }

  .read-check {
    color: var(--ion-color-white) !important;
  }

  .chat-send-button {
    background: var(--ion-color-skin-secondary-bright);
  }

  .chat-controls-container .choose-attachments-button {
    background: var(--ion-color-skin-main) !important;
  }

  .common-page-sections {

    .common-btn1,
    .common-btn3,
    .common-btn5 {
      background: var(--ion-color-skin-main-lighter) !important;
    }

    .common-btn2,
    .common-btn4 {
      background: var(--ion-color-skin-main) !important;
    }
  }
  .common-load-more-small button{
    background: var(--ion-color-skin-secondary-bright) !important;
  }

  .bgimg {
    background: url("../../assets/organizations/nutrishare/images/select-list-arrow.svg") no-repeat top right !important;
  }

  .home-page .home-page-sections .row #edu-center img {
    content: url('../../assets/icon/home/<USER>');
  }

  .common-choose-recipients .display-text-wrap {
    background: #fff url("/assets/organizations/nutrishare/images/select-plus-icon.png") no-repeat right center !important;
  }

  .create-visit .uploadbtn {
    background: url(/assets/images/clip.png) no-repeat scroll left center var(--ion-color-skin-main) !important;
    background-position: 5px !important;
  }

  .send-forms-page .badge-patient {
    background-color: var(--ion-color-skin-main-light) !important;
  }

  .calendarFilters {
    #go-to-visit {
      --background: var(--ion-color-skin-main) !important;
    }
  }


  .cancel {
    background: var(--ion-color-skin-main);
  }

  .invite-patient .invite-patient-msg {
    color: var(--ion-color-skin-dark) !important;
  }

  .inbox-per-description {
    color: #58595b !important;
  }

  .common-search-box .action-buttons ion-button {
    --background: var(--ion-color-small-button) !important;
  }

  .tag-display .tag-form .action-buttons #tags-search {
    color: var(--ion-color-de-york) !important;
    --background: transparent !important;
  }

  .create-visit .view-availability-btn {
    --background: var(--ion-color-skin-secondary-bright);
  }

  .common-file-browse {
    background: var(--ion-color-skin-main-lighter) !important;
  }

  .set-button-color {
    --ion-toolbar-color: var(--ion-color-skin-secondary-bright);
  }

  .small-dev-title{
    @media (max-width: 320px) {  
      .common-header .header-ion-title{
        width: calc(100% - 84px) !important;
      }
      .common-header .header-title{
        text-overflow: unset !important;
      }
    }

  }
  .forgot-pass-form {
    --ion-color-de-york: var(--ion-color-skin-main-lighter) !important;
  }
  #login-form {
    --ion-color-de-york: var(--ion-color-skin-main) !important;
  }

  .load-otp{
    color: var(--ion-color-skin-main) !important;
  }
  
  .common-swipe-buttons .restore {
    --background: var(--ion-color-skin-main-lighter);
  }
  .highlight {
    --background: var(--ion-color-skin-secondary-bright) !important;
    --color: #fafafa !important;
  }
  .no-message img {
    content: url(/assets/organizations/nutrishare/images/no-message.png);
  }
  .no-message .head-text {
    color: var(--ion-color-skin-main) !important;
  }
  .empty-list-items .no-items-icon {
    img:not([alt="no-documents"]) {
      content: url(/assets/organizations/nutrishare/images/no-items.png);
    }
    img[alt="no-documents"] {
      content: url(/assets/organizations/nutrishare/images/no-documents.png);
    }
  };
  .empty-list-items .empty-text{
    color: var(--ion-color-skin-secondary-bright) !important;
  }
  #cancel{
    --color: var(--ion-color-white) !important;
  }
  .menu-theme {
    --ion-color-skin-main-light: var(--ion-color-skin-main-lighter) !important;
    ion-icon {
      color:#fff !important;
    }
    ion-item {
      --color:#fff !important;
    }
  }
  .common-active {
    color: var(--ion-color-skin-main) !important;
  }
  #btn-cancel {
    --color: var(--ion-color-white) !important;
    --background: var(--ion-color-skin-secondary-bright) !important;
  }
  #patient-button-group .ion-color-danger {
    --ion-color-danger: var(--ion-color-skin-main) !important;
  }
  .patient-list-page .patient-view-details {
    color: var(--ion-color-skin-secondary-bright) !important;
  }
  .demographic-profile-tabs{
    border: 1px solid var(--ion-color-skin-main) !important;
  }
  .contact-us {
    color: var(--ion-color-skin-dark) !important;
    a{
      color: var(--ion-color-skin-main) !important;
    }
  }
  .site-header-padding {
    padding-top: 16px;
  }
  .chat-sub-header {
    padding: 26px 5px 10px !important;
  }
  .button-send,
  .button-download {
    color: var(--ion-color-white) !important;
  }
  .select-document-page .choose-document-box {

    .file-browse  {
      background: var(--ion-color-skin-main) !important;
    }
    .file-upload-name {
      color: var(--ion-color-skin-main) !important;
    }
  }
  .message-section.message-received-chat a {
      color: var(--ion-color-skin-main) !important;
  }
  .grid-icon .grid-icon-background {
    filter: brightness(0) saturate(100%) invert(52%) sepia(97%) saturate(334%) hue-rotate(128deg) brightness(96%) contrast(83%);
  }
}