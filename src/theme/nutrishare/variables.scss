.theme-nutrishare {
  --ion-color-skin-main: #21549E;
  --ion-color-skin-main-light: #65BC46;
  --ion-color-skin-main-lighter: #4D76B1;
  --ion-color-skin-secondary-bright: #808285;
  --ion-color-skin-dark: var(--ion-color-black);

  --ion-color-hpbtn1: var(--ion-color-skin-main);
  --ion-color-hpbtn2: var(--ion-color-skin-main-lighter);
  --ion-color-hpbtn3: var(--ion-color-skin-main-lighter);
  --ion-color-hpbtn4: var(--ion-color-skin-main);

  --ion-color-link: var(--ion-color-skin-main-light);
  --ion-color-fountain-blue: var(--ion-color-skin-main-light);
  --ion-color-chat-bg-sent: var(--ion-color-skin-main);
  --ion-color-chat-bg: var(--ion-color-skin-main-lighter);
  
  --ion-color-footer-btn: var(--ion-color-skin-main-light);
  --ion-color-socket-bg: var(--ion-color-skin-main-light);

  --ion-color-small-button: var(--ion-color-skin-secondary-bright);

  --ion-color-de-york: var(--ion-color-skin-main-light);
  --ion-color-blumine: var(--ion-color-skin-main);
  --ion-color-success: var(--ion-color-skin-main-light);

  --ion-color-site-selected: linear-gradient(var(--ion-color-skin-main), var(--ion-color-skin-main-lighter));

  --ion-toolbar-color: var(--ion-color-white);
  --ion-color-count-bright-bg: var(--ion-color-white);
  --ion-color-count-bright-color: var(--ion-color-skin-main);
  --ion-color-count-dark-bg: var(--ion-color-skin-main-light);
  --ion-color-count-dark-color: var(--ion-color-white);

  --ion-color-elvio-header: var(--ion-color-skin-main);
  --ion-color-statusbar-background: var(--ion-color-skin-main);
  --ion-color-scrollbar-thumb: var(--ion-color-skin-secondary-bright);

  --ion-color-sign-in: var(--ion-color-skin-main);
  --ion-color-text-light: var(--ion-color-skin-secondary-bright);
  --ion-color-link-dark: var(--ion-color-skin-main);
  --ion-color-head-title: var(--ion-color-skin-secondary-bright);

  --ion-color-button: var(--ion-color-skin-secondary-bright);
  --ion-color-primary: var(--ion-color-skin-main-light);
  --ion-color-secondary: var(--ion-color-skin-secondary-bright);
  --ion-color-active: #2d96ad;
  --ion-color-button-cancel: var(--ion-color-skin-main-lighter);

  --ion-color-form-header: var(--ion-color-skin-main);
  --ion-color-form-submit: var(--ion-color-skin-secondary-bright);
  --ion-color-form-draft: var(--ion-color-skin-main-light);
  --ion-color-form-back-button: var(--ion-color-white);
  --ion-color-document-submit: var(--ion-color-skin-secondary-bright);

  --ion-color-action-sheet-button: var(--ion-color-skin-main-light);
  --highlight-background: var(--ion-color-skin-secondary-bright);
  --ion-color-checked: var(--ion-color-skin-main-lighter);
}