.calendar {
    background: #ffffff;
    border: none;
    border-bottom: 1px solid #e6e6e6;
}

.row {
    padding: 5px 5px;
    text-align: center;
    overflow-y: auto;
}

.calendarFilters {
    padding-top: 35px;
    width: 96%;
    margin-right: 2%;
    margin-left: 2%;
}

.filterbutton {
    font-size: 0.9em;
    margin-top: 2px;
    margin-left: 5px;
}

.segment-top {
    padding-top: 35px;
}

ion-segment {
    --background: #ffffff;
    border: 1px solid var(--ion-color-skin-secondary-bright);

    ion-segment-button {
        --background: #ffffff;
        --color: var(--ion-color-skin-secondary-bright);
        --background-checked: var(--ion-color-skin-secondary-bright);
        --color-checked: #ffffff;
        --background-hover: var(--ion-color-skin-secondary-bright);
        --ion-background-color: var(--ion-color-skin-secondary-bright);
        margin-left: 2px;
        margin-right: 2px;
    }
}

.time-zone-footer {
    padding: 10px 15px;
    margin-top: 15px;

    .dropdown-wrapper {
        margin-bottom: 20px;

        &:last-child {
            margin-bottom: 0;
        }

        .dropdown-label {
            display: block;
            text-align: left;
            margin-bottom: 5px;
        }

        ion-select {
            width: auto;
        }
    }
}

/* Desktop view */
@media (min-width: 768px) {
    .time-zone-footer {
        display: flex;
        justify-content: space-between;
    }

    .dropdown-wrapper {
        margin-bottom: 0;

        &:last-child {
            text-align: right;
        }
    }
}

#color-coding-select {
    color: black !important;
}

::ng-deep {
    .fc-col-header {
        width: 100% !important;
    }

    .fc .fc-scrollgrid table {
        width: 100% !important;
    }

    .fc .fc-daygrid-body {
        width: 100% !important;
    }
}