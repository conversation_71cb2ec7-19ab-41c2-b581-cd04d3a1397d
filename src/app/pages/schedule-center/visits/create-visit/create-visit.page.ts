import { Component, OnInit, <PERSON><PERSON>hild, On<PERSON><PERSON>roy } from '@angular/core';
import { AbstractControl, FormControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, NavigationExtras, Router } from '@angular/router';
import { InAppBrowser } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { AlertController, IonAccordionGroup, IonModal, ModalController, NavController } from '@ionic/angular';
import * as moment from 'moment-timezone';
import { forkJoin, Subscription } from 'rxjs';
import { AdvancedViewerComponent } from 'src/app/components/advanced-viewer/advanced-viewer.component';
import { Config } from 'src/app/constants/config';
import { AssetSource, Constants, DateFormat } from 'src/app/constants/constants';
import { PageRoutes } from 'src/app/constants/page-routes';
import { Permissions } from 'src/app/constants/permissions';
import { VisitScheduleConstants, VisitType } from 'src/app/constants/visit-schedule-constants';
import { Country, DateSelectKeyValue } from 'src/app/interfaces/common-interface';
import { 
  ApiResponse,
  ArrayWithCount,
  AttachmentData,
  FileAttachments,
  IAvailabilityParam,
  ICheckVisitAvailability,
  IDateTime,
  IModifyVistFormData,
  IStaffData,
  IStaffSearchParam,
  IVisitDataController,
  IVisitDelete,
  IVisitStatus,
  TimeZones,
  UserList,
  VisitSubmitData
} from 'src/app/interfaces/schedule-center';
import { SearchStaffPage } from 'src/app/pages/schedule-center/visits/create-visit/search-staff/search-staff.page';
import { ViewAvailabilityCalendarPage } from 'src/app/pages/schedule-center/visits/create-visit/view-availability-calendar/view-availability-calendar.page';
import { ViewHistoryPage } from 'src/app/pages/schedule-center/visits/create-visit/view-history/view-history.page';
import { PatientFilterPage } from 'src/app/pages/schedule-center/visits/visit-filter/patient-filter/patient-filter.page';
import { CommonService } from 'src/app/services/common-service/common.service';
import { PermissionService } from 'src/app/services/permission-service/permission.service';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { VisitScheduleService } from 'src/app/services/visit-schedule-service/visit-schedule.service';
import {
  convertDateTimeToCustomFormat,
  convertDateTimeToIsoFormat,
  convertFromTzToTz,
  convertToMomentInUTC,
  formatDate,
  formatTime,
  isBlank,
  isPresent
} from 'src/app/utils/utils';
import { environment } from 'src/environments/environment';
import { getValueFromLocalStorage } from 'src/app/utils/storage-utils';
import { AsyncAdvanceSelectComponent } from 'src/app/components/async-advance-select/async-advance-select.component';
import { HttpClient } from '@angular/common/http';
import { MapViewComponent } from 'src/app/components/map-view/map-view.component';

@Component({
  selector: 'app-create-visit',
  templateUrl: './create-visit.page.html',
  styleUrls: ['./create-visit.page.scss']
})
export class CreateVisitPage implements OnInit, OnDestroy {
  private static readonly ACCORDION_EXPAND_TIMEOUT = 100;
  private static readonly SCROLL_TO_ERROR_TIMEOUT = 500;

  @ViewChild('deleteSeriesModal') deleteSeriesModal: IonModal;
  createVisitForm: UntypedFormGroup;
  visitationTypes: any;
  countryDetails: Country;
  countryId: string;
  userDetails: any;
  defaultCountryId = Constants.defaultCountryId;
  patientVisitStatusData = Constants.patientVisitStatus;
  staffPartnerVisitStatusData = Constants.staffVisitStatus;
  timeZones: TimeZones;
  repeatSchedules = Constants.repeatSchedules;
  repeatIntervals = Constants.repeatIntervals;
  weeklySchedule = Constants.weeklySchedule;
  scheduleTab = false;
  monthlySchedule = Constants.monthlySchedule;
  staffAvailability = [];
  staffIds = [];
  h1ma = Constants.dateFormat.h1ma;
  hhmm = Constants.dateFormat.hhmm;
  dateTimeValidator = false;
  therapyTypes: any[] = [];
  dosageAppointments: any[] = [];
  selectedTherapyType: any = {};
  selectedDosageAppointment: any = {};
  enableTherapyFields = false;
  endOnDateTimeValidator = false;
  fileError = false;
  patientID: any;
  visitInfo: IVisitDataController;
  isDraft = false;
  isDelete = false;
  timePlaceholder = Constants.dateFormat.hhMM;
  viewHistoryData;
  timeZone;
  dateFormat = Constants.dateFormat;
  mySites = [];
  visitFiles = [];
  driveTimeHours: string[] = [];
  driveTimeMinutes: string[] = [];

  presentingElement = null;
  deleteSeriesModel = 'today';
  deleteSeriesFrom = '';
  fileSize;
  fileSizeError = false;
  fileSizeErrorMessage = this.commonService.getTranslateDataWithParam('ERROR_MESSAGES.MAXIMUM_FILE_SIZE', {
    maxFileSize: Constants.fileSize
  });
  initialStartDate = moment(new Date()).format(Constants.dateFormat.ymd);
  dailyUntilDateShow = true;
  endOnMinDate: string;
  @ViewChild('accordionGroup', { static: true }) accordionGroup: IonAccordionGroup;
  @ViewChild('accordionGroupMain', { static: true }) accordionGroupMain: IonAccordionGroup;
  viewHistory = false;
  visitKey: string;
  staffStatus = Constants.staffVisitStatus;
  editType: number;
  checkRecurrenceVisit: boolean;
  previousEndAt: string;
  previousStartAt: string;
  navigationExtras: NavigationExtras;
  calendarEventData: any;
  endAt: string;
  startAt: string;
  siteTimeZone: string;
  maxYear: string;
  checkDraftVisit = false;
  savedFiles: [FileAttachments];
  todayDateTime: IDateTime;
  selectedDateTime: IDateTime;
  attachmentKeysToRemove = [];
  privilegeManageVisitSchedule: boolean;
  privilegeScheduleForThemselves: boolean;
  privilegeAllowRolesToSchedule: boolean;
  privilegeAllowEditCompletedVisits: boolean;
  partnerOrgName: string;
  partnerSelected: boolean;
  fetchStartDateTime: string;
  fetchEndDateTime: string;
  fetchActualDateTimeIn: string;
  fetchActualDateTimeOut: string;
  visitScheduleConstants = VisitScheduleConstants;
  VisitType = VisitType;
  patientVisitStatusId: number;
  staffVisitStatusId: number;
  isCompleted = false;
  isReviewCompleted = false;
  patientAddress: string;
  btnReset: string;
  viewRecurrenceTab = true;
  organization: string;
  selectedUserID: string;
  pageLoaderStaffStatus = true;
  pageLoaderPatientStatus = true;
  selectedStaffData: UserList;
  visitData: any = {};
  isBlank = isBlank;
  constants = Constants;
  formatDate = formatDate;
  endOnSeriesMinDate: string;
  revisedStartDateMinValue: string;
  revisedEndDateMinValue: string;
  fetchRevisedStartDateTime: string;
  fetchRevisedEndDateTime: string;
  isVisitInPastEvent = false;
  isVisitInPastSeries = false;
  isVisitInProgressSeries = false;
  countryIsoCode: string;
  isUserPatient;
  formValueChangeHandle;
  isRevisedDatesActive = false;
  selectedVisitLocation: any = {};
  selectedVisitChair: any = {};
  dynamicRows = 1;
  showStartButton = false;
  enableReviewComplete = false;
  enableTotalMileageField = false;
  enableDriveTimeField = false;
  clearLocation = {
    timeInLatLongClear: 0,
    timeOutLatLongClear: 0
  };
  durationMin = 30;
  formSubmitted = false;
  private accordionExpandTimeoutId: number | null = null;
  private scrollToErrorTimeoutId: number | null = null;
  private subscriptions = new Subscription();
  constructor(
    private readonly formBuilder: UntypedFormBuilder,
    public sharedService: SharedService,
    private modalController: ModalController,
    public alertController: AlertController,
    public commonService: CommonService,
    private router: Router,
    private route: ActivatedRoute,
    private browser: InAppBrowser,
    private visitScheduleService: VisitScheduleService,
    private permissionService: PermissionService,
    private navController: NavController,
    private readonly http: HttpClient
  ) {
    this.userDetails = this.sharedService.userData;
    this.countryId = this.userDetails.countryCode ? this.userDetails.countryCode : this.defaultCountryId;
    this.sharedService.getAllTimeZones().subscribe((timezones: TimeZones) => {
      if (timezones && timezones.length) {
        this.timeZones = timezones;
        this.sharedService.timeZones = timezones;
      }
    });
    if (this.sharedService.platform.is('ipad') || this.sharedService.platform.is('tablet')) {
      this.dynamicRows = 3;
    } else if (
      (this.sharedService.platform.is('capacitor') && this.sharedService.platform.is('mobile')) ||
      this.sharedService.platform.is('mobileweb')
    ) {
      this.dynamicRows = 4;
    }
    this.setCountryIsoCode();
    this.getCountyDetails();
    this.route.paramMap.subscribe((paramMap) => {
      this.visitKey = paramMap.get('visitKey');
      if (this.visitKey) {
        const { state } = this.router.getCurrentNavigation().extras;
        if (state) {
          this.calendarEventData = state.data;
          this.editType = state.editType;
          if (
            this.calendarEventData.patientVisitStatus !== Constants.staffVisitStatus[4].value ||
            this.calendarEventData.staffVisitStatus !== Constants.staffVisitStatus[4].value
          ) {
            this.showStartButton = false;
          }
        } else {
          this.navController.navigateBack(PageRoutes.scheduleCenterVisits);
        }
      }
    });
    this.siteTimeZone = !isBlank(this.userDetails.mySiteinfo)
      ? this.sharedService.getSiteConfigValue(Config.timezoneName)
      : this.sharedService.getConfigValue(Config.tenantTimezoneName);
    this.maxYear = this.sharedService.setCalendarPickerMaxYear();
    this.todayDateTime = {
      date: formatDate(new Date(), Constants.dateFormat.ymd),
      time: formatDate(new Date(), this.dateFormat.hhmm0)
    };

    // Check if selectedDateTime exists in navigation state
    const navState = this.router.getCurrentNavigation()?.extras?.state;
    if (navState?.selectedDateTime && typeof navState.selectedDateTime === 'object') {
      const dateParam = navState.selectedDateTime;
      this.selectedDateTime = {
        date: dateParam.date || formatDate(new Date(), Constants.dateFormat.ymd),
        time: dateParam.time || formatDate(new Date(), this.dateFormat.hhmm0)
      };
    } else {
      // Default to current date and time if no selection
      this.selectedDateTime = {
        date: formatDate(new Date(), Constants.dateFormat.ymd),
        time: formatDate(new Date(), this.dateFormat.hhmm0)
      };
    }
    this.btnReset = this.commonService.getTranslateData('BUTTONS.RESET');
  }

  ngOnInit() {
    this.initializeFormControls();
    this.checkConfigValues();
    this.sharedService.configValuesUpdated.subscribe(() => {
      this.checkConfigValues();
    });
    if (this.enableTherapyFields) {
      this.createVisitForm.addControl('therapyType', new FormControl('', [Validators.required]));
      this.createVisitForm.addControl('dosageAppointment', new FormControl('', [Validators.required]));
    }
    this.getVisitLocationType();
    this.mySites = this.userDetails.mySites;

    // Initialize drive time hours and minutes arrays
    this.initializeDriveTimeArrays();

    const localSelectedSites = getValueFromLocalStorage(Constants.storageKeys.selectedSites);
    this.createVisitForm.patchValue({
      siteNames: isPresent(localSelectedSites) ? JSON.parse(localSelectedSites) : this.mySites.map((site) => site.id)
    });
    if (this.mySites.length === 1) {
      this.createVisitForm.patchValue({
        siteName: this.mySites[0].id
      });
      this.getCountyDetails();
    }
    this.presentingElement = document.querySelector('.ion-page');
    this.createVisitForm.get('showRevisedTimeUpdate').valueChanges.subscribe((visitData) => {
      this.onRevisedChange();
    });

    this.subscriptions.add(
      this.createVisitForm.controls.staffPartnerVisitStatus.valueChanges.subscribe(() => {
        if (this.createVisitForm.get('staffPartnerVisitStatus')?.value === Constants.staffVisitStatus[2].value 
        && this.createVisitForm.get('requestPatientConfirmation')?.value) {
          this.createVisitForm.patchValue({
            confirmationBeforeStaffConfirm: 0
          }, { emitEvent: false });
        }
      })
    );
  }
  ionViewWillEnter() {
    if (this.visitKey && this.calendarEventData !== undefined) {
      this.getVisitDetails();
    }
  }

  /** initialize all form controls */
  initializeFormControls() {
    this.createVisitForm = this.formBuilder.group({
      patientName: ['', [Validators.required]],
      countryCode: [''],
      phoneNumber: ['', [Validators.pattern(Constants.validationPattern.phoneNumberPattern)]],
      visitAddress: [''],
      visitTitle: ['', [Validators.required]],
      billable: [true],
      staffPartnerVisitStatus: [''],
      staffReasonForCancel: [''],
      patientVisitStatus: [''],
      subcontracted: [false],
      organization: [''],
      visitAssignedTo: ['', [Validators.required]],
      timeZone: [this.siteTimeZone],
      startDate: [''],
      startTime: [''],
      endDate: [''],
      endTime: [''],
      scheduleCadence: [Constants.schedulesCadence[0].value],
      repeatEvery: ['1'],
      weekly: [''],
      monthly: [''],
      fileUpload: [''],
      repeatSchedule: [''],
      actualDate: [''],
      actualTimeIn: [''],
      actualTimeOut: [''],
      siteNames: ['', [Validators.required, Validators.min(1)]],
      siteName: ['', [Validators.required, Validators.min(1)]],
      visitDetails: [''],
      recurrence: [false],
      endOn: [''],
      visitationType: [null, [Validators.required]],
      visitationTypeLocation: [''],
      visitChair: [''],
      requestPatientConfirmation: [false],
      sendPatientNotification: [false],
      setPatientReminder: [false],
      confirmationBeforeStaffConfirm: ['0'],
      additionalDetails: [''],
      patientReasonForCancel: [''],
      seriesStartDate: [''],
      seriesEndDate: [''],
      revisedStartDate: [''],
      revisedEndDate: [''],
      revisedStartTime: [''],
      revisedEndTime: [''],
      showRevisedTimeUpdate: [false],
      showMultipleDatesSelection: [false],
      multipleDates: [''],
      multipleDatesFormat: [''],
      startVisitLatLong: [''],
      stopVisitLatLong: [''],
      totalMileage: [''],
      driveTimeHours: ['00'],
      driveTimeMinutes: ['00'],
      totalDriveTime: ['']
    });
    this.setDefaultValue();
    this.selectedVisitLocation = {};
    this.selectedVisitChair = {};
  }

  /** setDefault values */
  setDefaultValue(): void {
    const defaultTime = this.timeCalculator(`${this.selectedDateTime.date} ${this.selectedDateTime.time}`);
    // Check if the date/time was passed via navigation
    const navState = this.router.getCurrentNavigation()?.extras?.state;
    const isFromNavigation = navState && navState.selectedDateTime;
    this.createVisitForm.patchValue({
      startDate: formatDate(`${this.selectedDateTime.date} ${this.selectedDateTime.time}`, this.dateFormat.mdy),
      endDate: formatDate(`${this.selectedDateTime.date} ${this.selectedDateTime.time}`, this.dateFormat.mdy),
      startTime: moment(defaultTime.setMinutes(defaultTime.getMinutes() + (isFromNavigation ? 0 : 30))).format(Constants.dateFormat.hhmma),
      endTime: moment(defaultTime.setMinutes(defaultTime.getMinutes() + this.durationMin)).format(Constants.dateFormat.hhmma),
      staffPartnerVisitStatus: Constants.staffVisitStatus[0].value,
      patientVisitStatus: Constants.patientVisitStatus[0].value
    });
    this.checkUserStatus();
    if (this.createVisitForm.value.startTime === VisitScheduleConstants.starttimeCheck2) {
      this.createVisitForm.patchValue({
        startDate: moment(this.createVisitForm.value.startDate).add(1, 'd').format(Constants.dateFormat.mdy),
        endDate: moment(this.createVisitForm.value.startDate).add(1, 'd').format(Constants.dateFormat.mdy)
      });
      this.initialStartDate = formatDate(this.createVisitForm.value.startDate, Constants.dateFormat.ymd);
    }
    if (this.createVisitForm.value.startTime === VisitScheduleConstants.starttimeCheck1) {
      this.createVisitForm.patchValue({
        endDate: moment(this.createVisitForm.value.startDate).add(1, 'd').format(Constants.dateFormat.mdy)
      });
    }

    this.setTimePicker();
    this.isUserPatient = Number(this.sharedService.userData?.group) === Constants.patientGroupId;
    this.endOnSeriesMinDate = this.endOnMinDate = moment(this.createVisitForm.value.endDate).add(1, 'd').format(Constants.dateFormat.ymd);
  }
  /** select all country details */
  getCountyDetails(): void {
    this.countryDetails = this.sharedService.getCountryDetails(this.countryId, this.countryIsoCode);
    this.countryId = this.countryDetails?.dialCode || Constants.defaultCountryId;
  }

  presentCountryPopover(ev: any): void {
    this.countryDetails = this.sharedService.selectedCountry;
    this.sharedService.presentCountryPopover(
      ev,
      (callback: Country) => {
        this.countryDetails = callback;
        this.countryId = this.countryDetails.dialCode;
        this.countryIsoCode = this.countryDetails.code;
      },
      VisitScheduleConstants.createVisitPage
    );
  }

  /** get visitation Type */
  getVisitLocationType(): void {
    this.visitScheduleService.getLocationTypes().subscribe((resp) => {
      if (resp && resp.data) {
        this.visitationTypes = resp.data;
      }
    });
  }

  /** return form controls */
  get formControl() {
    return this.createVisitForm.controls;
  }

  /** schedule cadence selection */
  scheduleCadenceChange(): void {
    this.dailyUntilDateShow = true;
    if (this.createVisitForm.value.scheduleCadence === Constants.daily) {
      this.createVisitForm.controls['monthly'].reset();
      this.createVisitForm.controls['weekly'].reset();
    } else if (this.createVisitForm.value.scheduleCadence === Constants.weekly) {
      this.createVisitForm.controls['monthly'].reset();
      this.initWeeklySelection();
      this.scheduleTab = true;
    } else if (this.createVisitForm.value.scheduleCadence === Constants.monthly) {
      this.createVisitForm.controls['weekly'].reset();
      this.scheduleTab = true;
      this.createVisitForm.controls['monthly'].setValue(Constants.dayOfMonth);
    } else {
      this.scheduleTab = false;
      this.createVisitForm.controls['monthly'].reset();
      this.createVisitForm.controls['weekly'].reset();
    }
  }

  /* initialize week day */
  initWeeklySelection() {
    let date = this.createVisitForm.value.startDate;
    if (this.isEditRecursiveRevised()) {
      date = this.createVisitForm.value.revisedStartDate;
    } else if (!(this.isRevisedDatesActive && this.isVisitInPastEvent && this.isVisitInPastSeries) && this.isEditSeriesMode()) {
      date = this.createVisitForm.value.seriesStartDate;
    }
    this.weeklySchedule = Constants.weeklySchedule.map((data) => {
      if (data.value === formatDate(date, Constants.dateFormat.dd).toUpperCase()) {
        data.selected = true;
        const weeks = isBlank(this.createVisitForm.value.weekly) ? [] : this.createVisitForm.value.weekly
        this.createVisitForm.controls['weekly'].patchValue([...new Set([...weeks, data.value])]);
        data.disabled = true;
      } else {
        data.disabled = false;
      }
      return data;
    });
  }

  /** start date change function */
  startDateChange(date): void {
    if (!this.visitKey) {
      this.createVisitForm.patchValue({
        startDate: formatDate(date, Constants.dateFormat.mdy),
        endDate: formatDate(date, Constants.dateFormat.mdy),
        endOn: this.createVisitForm.value.recurrence ? moment(date).add(1, 'd').format(Constants.dateFormat.mdy) : ''
      });
    } else {
      this.createVisitForm.patchValue({
        startDate: formatDate(date, Constants.dateFormat.mdy),
        endDate: formatDate(date, Constants.dateFormat.mdy)
      });
    }

    this.dateTimeChecker();
    if (this.createVisitForm.value.scheduleCadence === Constants.weekly) {
      this.initWeeklySelection();
    }
  }

  /** end date change function */
  endDateChange(date): void {
    if (!this.visitKey) {
      this.createVisitForm.patchValue({
        endDate: formatDate(date, Constants.dateFormat.mdy),
        endOn: this.createVisitForm.value.recurrence ? moment(date).add(1, 'd').format(Constants.dateFormat.mdy) : ''
      });
    } else {
      this.createVisitForm.patchValue({
        endDate: formatDate(date, Constants.dateFormat.mdy)
      });
    }
    this.dateTimeChecker();
    this.checkStartAndEndDate();

    if (this.createVisitForm.value.recurrence) {
      this.endOnMinDate = moment(this.createVisitForm.value.endDate).add(1, 'd').format(Constants.dateFormat.ymd);
    }
  }

  /** end time change function */
  endTimeChange(data): void {
    const createVisitData = this.createVisitForm.getRawValue();
    if (data) {
      this.fetchEndDateTime = formatTime(createVisitData.endTime, Constants.dateFormat.h2ma, Constants.dateFormat.hhmm0);
      const end = formatDate([createVisitData.endDate, data].join(' '), this.dateFormat.h2ma);
      this.createVisitForm.patchValue({
        endTime: end
      });
    }
    this.dateTimeChecker();
  }

  /** start time change function */
  startTimeChange(data): void {
    if (data) {
      const time = [this.createVisitForm.getRawValue().startDate, data].join(' ');
      const startTime = formatDate(time, this.dateFormat.h2ma);
      const end = moment(time).add(this.durationMin, 'minute');
      const endDate = formatDate(end, this.dateFormat.mdy);
      const endTime = formatDate(end, this.dateFormat.h2ma);
      this.createVisitForm.patchValue({
        startTime,
        endTime,
        endDate
      });
      this.fetchStartDateTime = formatTime(this.createVisitForm.getRawValue().startTime, Constants.dateFormat.h2ma, Constants.dateFormat.hhmm0);
      this.fetchEndDateTime = formatTime(this.createVisitForm.getRawValue().endTime, Constants.dateFormat.h2ma, Constants.dateFormat.hhmm0);
    }

    this.dateTimeChecker();
  }

  /** until date change function */
  endOnChange(date): void {
    this.createVisitForm.patchValue({
      endOn: moment(date).format(Constants.dateFormat.mdy),
      seriesEndDate: moment(date).format(Constants.dateFormat.mdy)
    });
    if (moment(this.createVisitForm.value.endDate).isSameOrAfter(this.createVisitForm.value.endOn)) {
      this.endOnDateTimeValidator = true;
    } else {
      this.endOnDateTimeValidator = false;
    }
  }

  /** time minutes setting by checking input datetime */
  timeCalculator(time) {
    const selectMinute = moment(time).minute();
    const selectHour = moment(time).hour();
    let dateTime;
    if (selectMinute >= 30) {
      dateTime = new Date(`${moment(time).format(Constants.dateFormat.mdy)} ${selectHour}:30`);
    } else {
      dateTime = new Date(`${moment(time).format(Constants.dateFormat.mdy)} ${selectHour}:00`);
    }
    return dateTime;
  }

  /** assigned to selection */
  async selectStaff(event?: Event): Promise<void> {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    const maxSelection = Number(this.sharedService.getConfigValue('visit_max_staff_assigned_limit')) || 1;
    const modal = await this.modalController.create({
      component: SearchStaffPage,
      cssClass: 'common-advanced-select',
      componentProps: {
        staffIds: this.staffIds,
        param: {
          type: Constants.staffPartner,
          siteId: this.createVisitForm.value.siteName
        },
        maxSelection,
        singleSelect: this.isSingleSelect
      }
    });
    modal.onDidDismiss().then((modelData) => {
      if (!isBlank(modelData.data) && !isBlank(modelData.data.staffData)) {
        // Extract the staffData from the returned object
        this.visitAssignToStaff(modelData.data.staffData);
      }
      else if (modelData.data && modelData.data.staffData && !modelData.data.staffData.length) {
        this.resetVisitAssignedToData();
      }
    });
    return modal.present();
  }

  setPartnerOrganization(staffObject: UserList): void {
    if (!isBlank(staffObject)) {
      if (staffObject.user_type.toLowerCase() === Constants.partner.toLowerCase() && !isBlank(staffObject.organization)) {
        this.partnerSelected = true;
        this.partnerOrgName = staffObject.organization;
      } else {
        this.partnerSelected = false;
        this.partnerOrgName = '';
      }
      this.subcontractChange();
    }
  }
  /** date time validate function */
  dateTimeChecker(): void {
    if (
      moment(`${this.createVisitForm.controls.startDate.value} ${this.createVisitForm.controls.startTime.value}`).isAfter(
        `${this.createVisitForm.controls.endDate.value} ${this.createVisitForm.controls.endTime.value}`
      )
    ) {
      this.dateTimeValidator = true;
    } else {
      this.dateTimeValidator = false;
    }
    this.checkUserStatus();
  }

  subcontractChange(): void {
    if (this.partnerOrgName && this.partnerSelected && this.createVisitForm.value.subcontracted) {
      this.createVisitForm.patchValue({ organization: this.partnerOrgName });
    } else if (this.organization && this.selectedUserID && this.staffIds?.[0] === this.selectedUserID && this.createVisitForm.value.subcontracted) {
      this.createVisitForm.patchValue({ organization: this.organization });
    } else {
      this.createVisitForm.patchValue({ organization: '' });
    }
    this.updateOrganizationValidators();

    if (
      !this.createVisitForm.value.subcontracted &&
      !this.createVisitForm.value.organization &&
      this.staffIds?.length &&
      this.createVisitForm.value.staffPartnerVisitStatus === Constants.staffVisitStatus[0].value
    ) {
      this.createVisitForm.patchValue({
        staffPartnerVisitStatus: Constants.staffVisitStatus[1].value
      });
      this.checkUserStatus();
    }
    if (!this.staffIds?.length && !this.createVisitForm.value.subcontracted && !this.createVisitForm.value.organization) {
      this.createVisitForm.patchValue({
        staffPartnerVisitStatus: Constants.staffVisitStatus[0].value
      });
      this.checkUserStatus();
    }
    this.updateClinicianFieldRequirement();
  }

  /** select a file */
  selectFile(data): void {
    let isFileExist: boolean;
    this.fileSize = 0;
    const allowedImageFileTypes = Constants.visitScheduleAllowedFileFormats;
    for (let i = 0; i < data.target.files.length; i++) {
      const selectedFile: File = data.target.files[i];
      const renamedFile = new File([selectedFile], selectedFile.name.replace(Constants.specialChar, '_'));
      const fileExt = data.target.files[i].name.split('.').pop().toLowerCase();
      const mathExtension = allowedImageFileTypes.indexOf(fileExt);
      if (mathExtension < 0) {
        this.fileError = true;
      } else {
        isFileExist = !isBlank(this.visitFiles.find((x) => x.name === data.target.files[i].name));
        if (!isFileExist && this.visitKey && !isBlank(this.savedFiles)) {
          isFileExist = !isBlank(this.savedFiles.find((x) => x.formattedFileName === data.target.files[i].name));
        }

        if (!isFileExist) {
          this.visitFiles.push(renamedFile);
          this.visitFiles.forEach((obj) => {
            this.fileSize += obj.size;
          });

          if (this.visitKey) {
            if (this.savedFiles) {
              this.savedFiles.forEach((obj) => {
                this.fileSize += Number(obj.size);
              });
            }
          }
          if (this.fileSize < parseInt(Constants.maxFileSizeByte)) {
            this.fileSizeError = false;
          } else {
            this.fileSizeError = true;
          }
          this.fileError = false;
        }
      }
    }
  }
  /** clear selected file */
  removeFile(file) {
    this.visitFiles = this.visitFiles.filter((item) => item.lastModified !== file.lastModified);
    this.fileSize = 0;
    this.visitFiles.forEach((obj) => {
      this.fileSize += obj.size;
    });
    if (this.fileSize < parseInt(Constants.maxFileSizeByte)) {
      this.fileSizeError = false;
    } else {
      this.fileSizeError = true;
    }
    this.fileError = false;
  }

  /**
   * To remove the uploaded file attachments
   * @param index index of removed file
   */
  removeUploadedFile(index): void {
    this.attachmentKeysToRemove.push(this.savedFiles[index].key);
    this.savedFiles.splice(index, 1);
  }

  getStaffList() {
    const param: IStaffSearchParam = {
      id: this.staffIds?.[0],
      type: Constants.staffPartner
    };
    if ((this.privilegeManageVisitSchedule || this.privilegeScheduleForThemselves) && isPresent(this.createVisitForm.value.siteName)) {
      if (!this.privilegeManageVisitSchedule && this.privilegeAllowRolesToSchedule && this.privilegeScheduleForThemselves) {
        param.id = this.sharedService.userData.userId;
      }
      this.sharedService.isLoading = true;
      this.visitScheduleService.getUserList(param).subscribe(
        (data: ApiResponse<ArrayWithCount<UserList[]>>) => {
          this.staffAvailability = data.data.response;
          if (!this.privilegeManageVisitSchedule && !this.privilegeAllowRolesToSchedule && this.privilegeScheduleForThemselves) {
            this.staffAvailability = [];
          }
          // Get the organization name if assigned staff is a partner
          if (!isBlank(this.visitKey) && !isBlank(this.staffAvailability) && this.staffIds?.length) {
            const staffObject = this.staffAvailability.find((item) => {
              return +item.userid === +this.staffIds[0];
            });
            this.setPartnerOrganization(staffObject);
          }
          this.sharedService.isLoading = false;
        },
        () => {
          this.sharedService.isLoading = false;
        }
      );
    } else {
      this.staffAvailability = [];
    }
  }

  /** open modal for select patient */
  async selectPatient(event?: Event): Promise<void> {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    this.sharedService.isLoading = true;
    const modal = await this.modalController.create({
      component: PatientFilterPage,
      cssClass: 'common-advanced-select',
      componentProps: {
        patientID: this.patientID ? this.patientID : '',
        siteId: this.createVisitForm.value.siteNames ? this.createVisitForm.value.siteNames.join(',') : '',
        form: VisitScheduleConstants.createVisit
      }
    });
    modal.onDidDismiss().then((modalData) => {
      if (!isBlank(modalData.data)) {
        this.patientData(modalData);
      }
    });
    return modal.present();
  }

  async selectVisitTypeLocation(event?: Event): Promise<void> {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    let siteIds = !isBlank(this.formControl.siteNames.value) ? this.formControl.siteNames.value : [];
    if (isBlank(siteIds)) {
      siteIds = this.mySites.map((item) => item.id);
    }
    const modal = await this.modalController.create({
      component: AsyncAdvanceSelectComponent,
      componentProps: {
        displayKey: 'locationName',
        searchParams: {
          ...VisitScheduleConstants.commonSearchParam,
          siteIds,
          status: 1
        },
        selectedItems: this.selectedVisitLocation?.id ? [{ id: this.selectedVisitLocation?.id }] : [],
        loadDataFunction: this.visitScheduleService.searchVisitLocations,
        headerTitle: 'TITLES.SELECT_VISIT_TYPE_LOCATION'
      }
    });
    modal.onDidDismiss().then((modalData) => {
      if (!isBlank(modalData.data)) {
        this.createVisitForm.controls['visitationTypeLocation'].setValue(modalData.data?.selectedIds?.[0] ?? null);
        this.markControlAsTouchedAndDirty('visitationTypeLocation');
        this.selectedVisitLocation = modalData.data.selectedItems?.[0] ?? null;
        if (this.createVisitForm.controls['visitChair'].value) {
          this.createVisitForm.controls['visitChair'].setValue('');
          this.markControlAsTouchedAndDirty('visitChair');
        }
        this.selectedVisitChair = {};
        this.updateClinicianFieldRequirement();
      }
    });
    return modal.present();
  }

  async selectVisitChair(event?: Event): Promise<void> {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    const modal = await this.modalController.create({
      component: AsyncAdvanceSelectComponent,
      componentProps: {
        displayKey: 'chairName',
        searchParams: {
          ...VisitScheduleConstants.commonSearchParam,
          visitLocationId: this.selectedVisitLocation?.id,
          status: 1
        },
        selectedItems: this.selectedVisitChair?.id ? [{ id: this.selectedVisitChair?.id }] : [],
        loadDataFunction: this.visitScheduleService.searchVisitChairs,
        headerTitle: 'TITLES.SELECT_VISIT_CHAIR'
      }
    });
    modal.onDidDismiss().then((modalData) => {
      if (!isBlank(modalData.data)) {
        this.createVisitForm.controls['visitChair'].setValue(modalData.data.selectedIds?.[0] ?? null);
        this.markControlAsTouchedAndDirty('visitChair');
        this.selectedVisitChair = modalData.data.selectedItems?.[0] ?? null;
        this.updateClinicianFieldRequirement();
      }
    });
    return modal.present();
  }

  patientData(modalData): void {
    this.createVisitForm.controls['patientName'].setValue(modalData.data.patientName);
    if (isBlank(modalData.data.patientData)) {
      this.createVisitForm.controls['visitAddress'].setValue('');
      this.createVisitForm.controls['phoneNumber'].setValue('');
      this.countryId = Constants.defaultCountryId;
      this.setCountryIsoCode();
      this.patientID = '';
      this.createVisitForm.controls['visitationType'].reset();
    } else {
      if (!isBlank(modalData.data.patientData.country_iso_code)) {
        this.countryIsoCode = modalData.data.patientData.country_iso_code;
      }
      if (!isBlank(modalData.data.patientData.country_code)) {
        this.countryId = modalData.data.patientData.country_code;
      }
      this.createVisitForm.patchValue({
        siteName: modalData.data.patientData.site_id
      });
      if (modalData.data.patientData.shippingAddress) {
        this.createVisitForm.patchValue({
          visitationType: VisitScheduleConstants.homeVisitType,
          visitAddress: modalData.data.patientData.shippingAddress
        });
      } else {
        this.createVisitForm.patchValue({
          visitationType: '',
          visitAddress: ''
        });
      }
      const mobile =
        isPresent(modalData.data.patientData.caregiver_userid) && isPresent(modalData.data.patientData.caregiverMobile)
          ? modalData.data.patientData.caregiverMobile
          : modalData.data.patientData.mobile;
      this.countryId =
        isPresent(modalData.data.patientData.caregiver_userid) &&
          isPresent(modalData.data.patientData.caregiverMobile) &&
          isPresent(modalData.data.patientData.caregiverCountryCode)
          ? modalData.data.patientData.caregiverCountryCode
          : isPresent(modalData.data.patientData.country_code)
            ? modalData.data.patientData.country_code
            : this.countryId;
      this.createVisitForm.controls['phoneNumber'].setValue(mobile);
      this.patientID = modalData.data.patientData.userid;
      this.patientAddress = modalData.data.patientData.shippingAddress;
    }
    this.createVisitForm.controls['patientName'].setValue(modalData.data.patientName);
    this.getCountyDetails();
    if (this.enableTherapyFields) {
      this.createVisitForm.controls['therapyType'].reset();
      this.createVisitForm.controls['dosageAppointment'].reset();
      this.selectedTherapyType = {};
      this.selectedDosageAppointment = {};
      this.setVisitDurationFromTherapy();
    }
  }

  async checkAvailability() {
    const createVisitFormData = this.createVisitForm.getRawValue();
    const timezoneDisplayName = this.timeZones
      .filter((k) => {
        return k.city === createVisitFormData.timeZone;
      })
      .map((v) => {
        return v.name;
      });
    const availabilityParam: IAvailabilityParam = {
      startTime: this.createVisitForm.value.startTime,
      endTime: this.createVisitForm.value.endTime,
      startIntervalTime: convertDateTimeToIsoFormat(
        formatDate(createVisitFormData.startDate, Constants.dateFormat.ymd),
        formatTime(createVisitFormData.startTime, this.dateFormat.h2ma, this.dateFormat.hhmm0)
      ),
      endIntervalTime: convertDateTimeToIsoFormat(
        formatDate(createVisitFormData.endDate, Constants.dateFormat.ymd),
        formatTime(createVisitFormData.endTime, this.dateFormat.h2ma, this.dateFormat.hhmm0)
      ),
      siteId: createVisitFormData.siteName ? createVisitFormData.siteName : 0,
      statusFilter: '',
      selectedUserId: this.staffIds ? this.staffIds.join(', ') : '',
      tenantTimeZoneName: createVisitFormData.timeZone,
      timezoneDisplayName: timezoneDisplayName.toString()
    };

    const modal = await this.modalController.create({
      component: ViewAvailabilityCalendarPage,
      cssClass: 'common-advanced-select',
      componentProps: {
        availabilityConfig: availabilityParam,
        clinicianName: this.createVisitForm.value.visitAssignedTo,
        clinicianId: this.staffIds ? this.staffIds.join(', ') : '',
        patientID: this.patientID ? this.patientID : '',
        patientSiteId: this.createVisitForm.value.siteName
      }
    });

    modal.onDidDismiss().then((modelData: { data: { staffData: UserList } }) => {
      if (!isBlank(modelData.data) && !isBlank(modelData.data.staffData)) {
        this.visitAssignToStaff([modelData.data.staffData]);
      }
    });

    return modal.present();
  }

  async saveAsDraftConfirm(): Promise<void> {
    let buttons;
    let cssClass;
    let message = 'LABELS.DO_YOU_WANT_TO_DRAFT_VISIT';
    if (this.checkRecurrenceVisit && this.visitKey) {
      buttons = [
        { text: 'BUTTONS.CANCEL', confirm: false, class: 'warn-btn alert-cancel' },
        { text: 'BUTTONS.SINGLE', confirm: true, class: 'warn-btn alert-ok', value: Constants.editType.single },
        { text: 'BUTTONS.ALL', confirm: true, class: 'warn-btn alert-ok', value: Constants.editType.series }
      ];
      cssClass = 'custom-alert';
      message = 'LABELS.VISIT_SAVE_AS_DRAFT_MODIFY';
    } else {
      buttons = [
        { text: 'BUTTONS.CANCEL', confirm: false, class: 'warn-btn alert-cancel' },
        { text: 'BUTTONS.YES', confirm: true, class: 'warn-btn alert-ok' }
      ];
      cssClass = 'visit-alert';
      message = 'LABELS.DO_YOU_WANT_TO_DRAFT_VISIT';
    }

    this.commonService
      .showAlert({
        message,
        header: 'MESSAGES.ARE_YOU_SURE',
        buttons,
        mode: 'ios',
        cssClass
      })
      .then((confirmation) => {
        if (confirmation === false) {
          this.commonService.closeAllAlert();
        } else {
          this.isDraft = true;
          this.editType = confirmation === true ? Constants.editType.series : confirmation;
          this.manageVisit();
        }
      });
  }

  async saveVisitConfirm() {
    if (!this.visitKey) {
      const headerLabel = 'MESSAGES.VISIT_STATUS_CONFIRM';
      const messageLabel = this.staffIds?.length ? 'LABELS.DO_YOU_WANT_TO_SEND_CONFIRM' : 'LABELS.DO_YOU_WANT_TO_SAVE';
      let alert;
      if (this.staffIds?.length) {
        alert = await this.alertController.create({
          header: this.commonService.getTranslateData(headerLabel),
          message: `${this.commonService.getTranslateData(messageLabel)} ${this.createVisitForm.controls.visitAssignedTo.value}?`,
          mode: 'ios',
          cssClass: 'custom-alert',
          buttons: [
            {
              text: this.commonService.getTranslateData('BUTTONS.YES'),
              handler: () => {
                this.isDraft = false;
                this.manageVisit();
              }
            },
            {
              text: this.commonService.getTranslateData('BUTTONS.NOT_YET'),
              handler: () => {
                this.isDraft = true;
                this.manageVisit();
              }
            },
            {
              text: this.commonService.getTranslateData('BUTTONS.CANCEL'),
              role: Constants.cancel
            }
          ]
        });
      } else {
        alert = await this.alertController.create({
          header: this.commonService.getTranslateData(headerLabel),
          message: this.commonService.getTranslateData(messageLabel),
          cssClass: 'visit-alert',
          mode: 'ios',
          buttons: [
            {
              text: this.commonService.getTranslateData('BUTTONS.CANCEL'),
              role: Constants.cancel
            },
            {
              text: this.commonService.getTranslateData('BUTTONS.SAVE'),
              handler: () => {
                this.isDraft = false;
                this.manageVisit();
              }
            }
          ]
        });
      }
      await alert.present();
    }
  }

  visitDataController(): void {
    const createVisitFormData = this.createVisitForm.getRawValue();
    const staffIdsArray = this.staffIds ? this.staffIds.map((id) => id.trim()).filter((id) => id !== '') : [];
    this.visitInfo = {
      siteId: createVisitFormData.siteName,
      patientId: this.patientID,
      countryCode: this.countryId ? (this.countryId.charAt(0) === '+' ? this.countryId.substring(1) : this.countryId) : this.countryId,
      countryIsoCode: this.countryIsoCode,
      phoneNumber: createVisitFormData.phoneNumber,
      visitAddress: createVisitFormData.visitAddress,
      visitTitle: createVisitFormData.visitTitle,
      visitationDetails: createVisitFormData.visitDetails,
      isBillable: createVisitFormData.billable ? 1 : 0,
      assignedTo: staffIdsArray,
      isSubcontracted: createVisitFormData.subcontracted ? 1 : 0,
      organization: createVisitFormData.subcontracted ? createVisitFormData.organization : '',
      staffConfirmation: String(createVisitFormData.staffPartnerVisitStatus),
      patientConfirmation: createVisitFormData.patientVisitStatus ? String(createVisitFormData.patientVisitStatus) : '',
      additionalDetails: createVisitFormData.additionalDetails,
      startAt: convertDateTimeToIsoFormat(
        formatDate(createVisitFormData.startDate, Constants.dateFormat.ymd),
        formatDate(
          [createVisitFormData.startDate, createVisitFormData.startTime].join(' '),
          this.dateFormat.hhmm0
        )
      ),
      endAt: convertDateTimeToIsoFormat(
        formatDate(createVisitFormData.endDate, Constants.dateFormat.ymd),
        formatDate(
          [createVisitFormData.endDate, createVisitFormData.endTime].join(' '),
          this.dateFormat.hhmm0
        )
      ),
      until:
        createVisitFormData.scheduleCadence !== Constants.none
          ? createVisitFormData.endOn
            ? convertDateTimeToIsoFormat(
              formatDate(createVisitFormData.endOn, Constants.dateFormat.ymd),
              formatTime(createVisitFormData.endTime, this.dateFormat.h2ma, this.dateFormat.hhmm0)
            )
            : ''
          : '',
      locationTimezone: createVisitFormData.timeZone,
      repeatInterval: createVisitFormData.recurrence
        ? createVisitFormData.repeatEvery
          ? createVisitFormData.repeatEvery
          : 0
        : 0,
      repeatType: createVisitFormData.recurrence
        ? createVisitFormData.scheduleCadence !== Constants.none
          ? createVisitFormData.scheduleCadence
          : Constants.none
        : Constants.none,
      repeatDays:
        createVisitFormData.recurrence && createVisitFormData.scheduleCadence === Constants.weekly
          ? createVisitFormData.weekly
            ? createVisitFormData.weekly.toString()
            : ''
          : '',
      repeatMonthType:
        createVisitFormData.recurrence && createVisitFormData.scheduleCadence === Constants.monthly
          ? createVisitFormData.monthly
          ? createVisitFormData.monthly
          : ''
          : '',
      visitStatus: this.isDraft ? '2' : '1',
      duration: '',
      visitType: createVisitFormData.visitationType ? createVisitFormData.visitationType.toString() : '',
      visitTypeLocation: createVisitFormData.visitationTypeLocation,
      visitChair: createVisitFormData.visitChair,
      therapyType: createVisitFormData.therapyType,
      dosageAppointment: createVisitFormData.dosageAppointment,
      requestPatientConfirmation: createVisitFormData.requestPatientConfirmation ? 1 : 0,
      confirmationBeforeStaffConfirm: createVisitFormData.confirmationBeforeStaffConfirm == '1' ? 1 : 0,
      sendPatientNotification: createVisitFormData.sendPatientNotification ? 1 : 0,
      setPatientReminder: createVisitFormData.setPatientReminder ? 1 : 0,
      staffReasonForCancel:
        this.createVisitForm.controls.staffPartnerVisitStatus.value === Constants.staffVisitStatus[5].value ||
          this.createVisitForm.controls.staffPartnerVisitStatus.value === Constants.staffVisitStatus[3].value
          ? createVisitFormData.staffReasonForCancel
          : '',
      patientReasonForCancel:
        createVisitFormData.patientVisitStatus === this.patientVisitStatusData[2].value ||
          createVisitFormData.patientVisitStatus === this.patientVisitStatusData[3].value
          ? createVisitFormData.patientReasonForCancel
          : ''
    };

    if (createVisitFormData.actualDate) {
      this.visitInfo = {
        ...this.visitInfo,
        actualTimeIn: createVisitFormData.actualTimeIn
          ? convertDateTimeToIsoFormat(
            formatDate(createVisitFormData.actualDate, Constants.dateFormat.ymd),
            formatTime(
              createVisitFormData.actualTimeIn,
              Constants.dateFormat.h2ma,
              Constants.dateFormat.hhmm0
            )
          )
          : '',
        actualTimeOut: createVisitFormData.actualTimeOut
          ? convertDateTimeToIsoFormat(
            formatDate(createVisitFormData.actualDate, Constants.dateFormat.ymd),
            formatTime(
              createVisitFormData.actualTimeOut,
              Constants.dateFormat.h2ma,
              Constants.dateFormat.hhmm0
            )
          )
          : ''
      };
    }

    if (this.createVisitForm.value.staffPartnerVisitStatus === Constants.staffVisitStatus[4].value) {
      this.visitInfo = {
        ...this.visitInfo,
        totalMileage: this.enableTotalMileageField ? createVisitFormData.totalMileage : undefined,
        totalDriveTime: this.enableDriveTimeField ? createVisitFormData.totalDriveTime : undefined,
      };
    }

    // change startAt,endAt when modify a recurrence visit
    if (this.visitKey) {
      this.visitInfo = {
        ...this.visitInfo,
        editType: this.checkRecurrenceVisit ? this.editType : 2
      };

      if (this.visitInfo.editType === Constants.editType.single) {
        this.visitInfo = {
          ...this.visitInfo,
          previousEndAt: this.previousEndAt,
          previousStartAt: this.previousStartAt,
          startAt: convertDateTimeToIsoFormat(
            formatDate(createVisitFormData.startDate, Constants.dateFormat.ymd),
            formatDate([createVisitFormData.startDate, createVisitFormData.startTime].join(' '), this.dateFormat.hhmm0)
          ),
          endAt: convertDateTimeToIsoFormat(
            formatDate(createVisitFormData.endDate, Constants.dateFormat.ymd),
            formatDate([createVisitFormData.endDate, createVisitFormData.endTime].join(' '), this.dateFormat.hhmm0)
          ),
          repeatInterval: 0,
          repeatType: Constants.none,
          repeatMonthType: '',
          repeatDays: '',
          until: ''
        };
      } else if (
        this.visitInfo.editType === Constants.editType.series &&
        moment(formatDate(createVisitFormData.startDate, Constants.dateFormat.ymd)).isSameOrBefore(
          formatDate(createVisitFormData.endDate, Constants.dateFormat.ymd)
        ) &&
        this.visitInfo.repeatType === Constants.none
      ) {
        this.visitInfo = {
          ...this.visitInfo,
          startAt: convertDateTimeToIsoFormat(
            formatDate(createVisitFormData.startDate, Constants.dateFormat.ymd),
            formatDate([createVisitFormData.startDate, createVisitFormData.startTime].join(' '), this.dateFormat.hhmm0)
          ),
          endAt: convertDateTimeToIsoFormat(
            formatDate(createVisitFormData.endDate, Constants.dateFormat.ymd),
            formatDate([createVisitFormData.endDate, createVisitFormData.endTime].join(' '), this.dateFormat.hhmm0)
          )
        };
      } else {
        this.visitInfo = {
          ...this.visitInfo,
          startAt: convertDateTimeToIsoFormat(
            formatDate(this.startAt, Constants.dateFormat.ymd),
            formatTime(createVisitFormData.startTime, Constants.dateFormat.h2ma, Constants.dateFormat.hhmm0)
          ),
          endAt: convertDateTimeToIsoFormat(
            formatDate(this.endAt, Constants.dateFormat.ymd),
            formatTime(createVisitFormData.endTime, Constants.dateFormat.h2ma, Constants.dateFormat.hhmm0)
          )
        };
      }
    }
  }

  manageVisit() {
    this.visitDataController();
    const createVisitFormData = this.createVisitForm.getRawValue();
    const createVisitData: any = { ...this.visitInfo };
    if (this.createVisitForm.value.showMultipleDatesSelection && this.createVisitForm.value.multipleDates) {
      const allParams = this.createVisitForm.value.multipleDates.split(', ').map((date) => {
        return {
          ...createVisitData,
          startAt: convertDateTimeToIsoFormat(
            formatDate(date, Constants.dateFormat.ymd),
            formatDate([date, this.createVisitForm.value.startTime].join(' '), this.dateFormat.hhmm0)
          ),
          endAt: convertDateTimeToIsoFormat(
            formatDate(date, Constants.dateFormat.ymd),
            formatDate([date, this.createVisitForm.value.endTime].join(' '), this.dateFormat.hhmm0)
          )
        }
      });
      forkJoin(allParams.map((param) => this.visitScheduleService.manageVisit(param))).subscribe((allResponses: any[]) => {

        if (!this.fileSizeError &&
          (this.visitFiles?.length || !isBlank(this.attachmentKeysToRemove))) {
          const uploadDataObservers = []
          allResponses.forEach((res: any) => {
            const visitAttachment = [...this.visitFiles];
            const uploadData = new FormData();
            visitAttachment.forEach((item) => {
              uploadData.append('visitattachment[]', item);
            });
            const visitKey = res.data.visitKey
            uploadData.append('visit', visitKey);
            uploadData.append('removeattachment', this.attachmentKeysToRemove.toString());
            if (visitKey && !this.fileSizeError && (this.visitFiles?.length || !isBlank(this.attachmentKeysToRemove))) {
              uploadDataObservers.push(this.visitScheduleService.fileAttach(uploadData))
            }
          })
          this.sharedService.consoloLoader = true;
          forkJoin(uploadDataObservers).subscribe((allFileResponses: ApiResponse<AttachmentData[]>[]) => {
            const failedResp = allFileResponses.filter((res) => !res.success);
            this.sharedService.consoloLoader = false;
            if (failedResp.length) {
              this.commonService.showMessage(this.commonService.getTranslateData('ERROR_MESSAGES.FILE_ATTACHMENT_FAILED'));
            } else if (isPresent(this.visitFiles)) {
              this.commonService.showMessage(this.commonService.getTranslateData('SUCCESS_MESSAGES.VISIT_FILE_ATTACHMENT_SUCCESS'));
            }
            this.visitFiles = [];
            this.visitMessagesForMultipleDates(allResponses);
          })
        } else {
          this.visitMessagesForMultipleDates(allResponses);
        }
      });
    } else {
      if (this.isEditPage() &&
        this.editType === Constants.editType.series) {
        if (this.isRecursiveEnabled() && this.isEditRecursiveRevised()) {
          const pastVisit = { ...createVisitData };
          const newVisit = { ...createVisitData };
          pastVisit.until = convertDateTimeToIsoFormat(
            formatDate(createVisitFormData.seriesEndDate, Constants.dateFormat.ymd),
            formatDate([createVisitFormData.seriesEndDate, createVisitFormData.endTime].join(' '), this.dateFormat.hhmm0)
          );
          newVisit.startAt = convertDateTimeToIsoFormat(
            formatDate(createVisitFormData.revisedStartDate, Constants.dateFormat.ymd),
            formatDate([createVisitFormData.revisedStartDate, createVisitFormData.revisedStartTime].join(' '), this.dateFormat.hhmm0)
          );
          newVisit.endAt = convertDateTimeToIsoFormat(
            formatDate(createVisitFormData.revisedStartDate, Constants.dateFormat.ymd),
            formatDate([createVisitFormData.revisedStartDate, createVisitFormData.revisedEndTime].join(' '), this.dateFormat.hhmm0)
          );
          newVisit.until = convertDateTimeToIsoFormat(
            formatDate(createVisitFormData.revisedEndDate, Constants.dateFormat.ymd),
            formatDate([createVisitFormData.revisedEndDate, createVisitFormData.revisedEndTime].join(' '), this.dateFormat.hhmm0)
          );

          pastVisit.repeatType = this.visitData.repeatType;
          pastVisit.repeatInterval = this.visitData.repeatInterval;
          pastVisit.repeatDays = this.visitData.repeatDays;
          pastVisit.repeatMonthType = this.visitData.repeatMonthType;
          pastVisit.organization = this.visitData.organizationName;
          pastVisit.locationTimezone = this.visitData.locationTimeZone;
          pastVisit.assignedTo = this.visitData.assignedToUserId ? [this.visitData.assignedToUserId] : [];
          pastVisit.isSubcontracted = this.visitData.subContractedStatus === Constants.isSubContracted;
          delete newVisit.editType;

          forkJoin({
            createdVisit: this.visitScheduleService.manageVisit(newVisit),
            oldVisit: this.visitScheduleService.manageVisitData(this.visitKey, pastVisit)
          })
            .subscribe(({ createdVisit, oldVisit }) => {
              if (
                oldVisit &&
                oldVisit.data &&
                oldVisit.data.visitKey &&
                !this.fileSizeError &&
                (this.visitFiles?.length || !isBlank(this.attachmentKeysToRemove))
              ) {
                this.uploadAttachment(oldVisit.data.visitKey, oldVisit);
              } else {
                this.visitMessages(oldVisit);
              }
              this.visitScheduleService.oldToNewSeries(
                {
                  "newSeriesId": createdVisit.data.visitId,
                  "oldSeriesEndDate": pastVisit.until,
                  "oldSeriesId": Number(this.visitData.id)
                }
              ).subscribe();
            });
          return false;
        } else {
          this.visitInfo.startAt = convertDateTimeToIsoFormat(
            formatDate(createVisitFormData.seriesStartDate, Constants.dateFormat.ymd),
            formatDate([createVisitFormData.seriesStartDate, createVisitFormData.startTime].join(' '), this.dateFormat.hhmm0)
          );
          this.visitInfo.endAt = convertDateTimeToIsoFormat(
            formatDate(createVisitFormData.seriesStartDate, Constants.dateFormat.ymd),
            formatDate([createVisitFormData.seriesStartDate, createVisitFormData.endTime].join(' '), this.dateFormat.hhmm0)
          );
          this.visitInfo.until = convertDateTimeToIsoFormat(
            formatDate(createVisitFormData.seriesEndDate, Constants.dateFormat.ymd),
            formatDate([createVisitFormData.seriesEndDate, createVisitFormData.endTime].join(' '), this.dateFormat.hhmm0)
          );
        }
      }

      this.manageVisitSubmit().subscribe((response: ApiResponse<VisitSubmitData>) => {
        /* file attachment */
        if (
          response &&
          response.data &&
          response.data.visitKey &&
          !this.isDelete &&
          !this.fileSizeError &&
          (this.visitFiles?.length || !isBlank(this.attachmentKeysToRemove))
        ) {
          this.uploadAttachment(response.data.visitKey, response);
        } else {
          this.visitMessages(response);
        }
      });
    }
  }

  /* sucess message for create visit and draft */
  visitMessages(response: ApiResponse<any>): void {
    this.commonService.dismissPopover();
    if (this.isDelete) {
      if (response.success) {
        this.commonService.showMessage(this.commonService.getTranslateData('SUCCESS_MESSAGES.VISIT_DELETED_MESSAGE'));
        this.redirectToCalendarView();
      } else {
        this.commonService.showMessage(this.commonService.getTranslateData('ERROR_MESSAGES.VISIT_DELETION_FAILED'));
      }
    } else if (this.isDraft) {
      if (response.success) {
        this.commonService.showMessage(this.commonService.getTranslateData('SUCCESS_MESSAGES.SAVE_AS_DRAFT_SUCCESS_MESSAGE'));
        this.redirectToCalendarView();
      } else {
        this.commonService.showMessage(this.commonService.getTranslateData('ERROR_MESSAGES.SAVE_AS_DRAFT_FAILED'));
      }
    } else if (!this.isDraft && response.success) {
      const successMessage = isPresent(this.visitKey) ? 'SUCCESS_MESSAGES.VISIT_UPDATED_SUCCESS' : 'SUCCESS_MESSAGES.VISIT_CREATED_SUCCESS';
      this.commonService.showMessage(this.commonService.getTranslateData(successMessage));
      this.redirectToCalendarView();
    } else {
      const errorMessage = isPresent(this.visitKey) ? 'ERROR_MESSAGES.VISIT_SCHEDULE_UPDATE_FAILED' : 'ERROR_MESSAGES.VISIT_SCHEDULE_CREATION_FAILED';
      this.commonService.showMessage(this.commonService.getTranslateData(errorMessage));
    }
  }

  /* success message for create visit and draft */
  visitMessagesForMultipleDates(response: any): void {
    this.commonService.dismissPopover();
    const failedResp = response.filter((res) => !res.success);
    if (this.isDraft) {
      if (!failedResp.length) {
        this.commonService.showMessage(this.commonService.getTranslateData('SUCCESS_MESSAGES.SAVE_AS_DRAFT_SUCCESS_MESSAGE'));
        this.redirectToCalendarView();
      } else {
        this.commonService.showMessage(this.commonService.getTranslateData('ERROR_MESSAGES.SAVE_AS_DRAFT_FAILED'));
      }
    } else if (!this.isDraft && failedResp.length === 0) {
      this.commonService.showMessage(this.commonService.getTranslateData('SUCCESS_MESSAGES.VISIT_CREATED_SUCCESS'));
      this.redirectToCalendarView();
    } else {
      this.commonService.showMessage(this.commonService.getTranslateData('ERROR_MESSAGES.VISIT_SCHEDULE_CREATION_FAILED'));
    }
  }

  redirectToCalendarView() {
    setTimeout(() => {
      this.createVisitForm.reset();
      this.initializeFormControls();
      this.navController.navigateBack(PageRoutes.scheduleCenterVisits);
      this.commonService.closeAllAlert();
    }, 2000);
  }

  manageVisitSubmit() {
    if (this.visitKey) {
      if (this.clearLocation.timeInLatLongClear || this.clearLocation.timeOutLatLongClear) {
        this.visitInfo = { ...this.visitInfo, ...this.clearLocation };
      }
      return this.visitScheduleService.manageVisitData(this.visitKey, this.visitInfo);
    }
    return this.visitScheduleService.manageVisit(this.visitInfo);
  }

  cancel(): void {
    const buttons = [
      { text: 'BUTTONS.CANCEL', confirm: false, class: 'warn-btn alert-cancel' },
      { text: 'BUTTONS.OK', confirm: true, class: 'warn-btn alert-ok' }
    ];
    const message = this.commonService.getTranslateData('MESSAGES.GOING_TO_DISCARD_CHANGES');
    const header = this.commonService.getTranslateData('MESSAGES.ARE_YOU_SURE');
    this.commonService.showAlert({ message, header, buttons, mode: 'ios', cssClass: 'visit-alert' }).then((confirmation) => {
      if (confirmation) {
        this.createVisitForm.reset();
        this.initializeFormControls();
        this.visitKey = undefined;
        this.navController.navigateBack(PageRoutes.scheduleCenterVisits);
      }
    });
  }

  async deleteVisitConfirmation() {
    if (this.isEditSeriesMode()) {
      this.deleteSeriesModal.present();
    } else {
      const buttons = [
        { text: 'BUTTONS.CANCEL', confirm: false, class: 'warn-btn alert-cancel' },
        { text: 'BUTTONS.OK', confirm: true, class: 'warn-btn alert-ok' }
      ];
      const cssClass = 'visit-alert';
      const message = 'LABELS.DELETE_VISIT';

      this.commonService
        .showAlert({
          message,
          header: 'MESSAGES.ARE_YOU_SURE',
          buttons,
          mode: 'ios',
          cssClass
        })
        .then((confirmation) => {
          if (confirmation) {
            this.deleteVisit();
          } else {
            this.commonService.closeAllAlert();
          }
        });
    }
  }

  deleteVisit() {
    const createVisitFormData = this.createVisitForm.getRawValue();
    const deleteData: IVisitDelete = {
      action: Constants.delete,
      editType: this.editType,
      endAt: convertDateTimeToIsoFormat(
        formatDate(createVisitFormData.endDate, Constants.dateFormat.ymd),
        formatTime(createVisitFormData.endTime, this.dateFormat.h2ma, this.dateFormat.hhmm0)
      ),
      startAt: convertDateTimeToIsoFormat(
        formatDate(createVisitFormData.startDate, Constants.dateFormat.ymd),
        formatTime(createVisitFormData.startTime, this.dateFormat.h2ma, this.dateFormat.hhmm0)
      )
    };
    if (this.visitKey) {
      this.visitScheduleService.manageVisitData(this.visitKey, deleteData).subscribe((response) => {
        if (response.success) {
          this.commonService.showMessage(this.commonService.getTranslateData('SUCCESS_MESSAGES.VISIT_DELETED_MESSAGE'));
          setTimeout(() => {
            this.commonService.closeAllAlert();
            this.navController.navigateBack(PageRoutes.scheduleCenterVisits);
          }, 2000);
        } else {
          this.commonService.showMessage(this.commonService.getTranslateData('ERROR_MESSAGES.VISIT_DELETION_FAILED'));
        }
      });
    }
  }
  /**
   * updateActualTime is generic function to update actual time in/out to link to picker
   * @param formControlName
   * @param time
   */
  updateActualTime(formControlName: string, time: string | string[]): void {
    if (this.createVisitForm && this.createVisitForm.contains(formControlName)) {
      if (!time || time === Constants.invalidDate) {
        time = this.createVisitForm.controls[formControlName].value || moment().format(Constants.dateFormat.h2ma);
      } else if (time.indexOf('T') > -1) {
        time = moment(time).format(Constants.dateFormat.h2ma);
      }
      const dateTime = moment(`${this.createVisitForm.controls['actualDate'].value} ${time}`);
      if (dateTime && dateTime.isValid()) {
        this.createVisitForm.controls[formControlName].setValue(dateTime.format(Constants.dateFormat.hhmma));
        const formattedTime = dateTime.format(Constants.dateFormat.hhmm0);
        if (formControlName === VisitScheduleConstants.actualTimeIn) {
          if (this.visitData?.timeInLatLong) {
            this.clearLocation.timeInLatLongClear = 1;
          }
          this.fetchActualDateTimeIn = formattedTime;
          if (this.enableTherapyFields &&
            this.selectedDosageAppointment &&
            this.selectedDosageAppointment.duration &&
            !isBlank(this.createVisitForm.controls['actualDate'].value)
          ) {
            const outTime = moment(dateTime).add(this.durationMin, 'minutes');
            this.createVisitForm.controls['actualTimeOut'].setValue(outTime.format(Constants.dateFormat.hhmma));
            this.fetchActualDateTimeOut = outTime.format(Constants.dateFormat.hhmm0);
          }
        } else {
          if (this.visitData?.timeOutLatLong) {
            this.clearLocation.timeOutLatLongClear = 1;
          }
          this.fetchActualDateTimeOut = formattedTime;
        }
      }
    }
  }
  actualDateSelection(date): void {
    this.createVisitForm.controls['actualDate'].setValue(moment(date).format(Constants.dateFormat.mdy));
    if (isBlank(this.createVisitForm.controls['actualTimeIn'].value)) {
      this.fetchActualDateTimeIn = '';
    }
    if (isBlank(this.createVisitForm.controls['actualTimeOut'].value)) {
      this.fetchActualDateTimeOut = '';
    }
  }

  getViewHistory(): void {
    const param = {
      defaultTimeZone: this.timeZone,
      visit_id: this.visitKey
    };
    this.sharedService.getViewHistoryDetails(param).subscribe((data) => {
      this.viewHistoryData = data.response;
      this.viewHistoryDetails();
    });
  }

  async viewHistoryDetails(): Promise<void> {
    const modal = await this.modalController.create({
      component: ViewHistoryPage,
      cssClass: 'common-advanced-select',
      componentProps: {
        historyData: this.viewHistoryData
      }
    });
    return modal.present();
  }
  checkConfigValues(): void {
    this.timeZone = this.sharedService.getConfigValue(Config.tenantTimezoneName);
    this.privilegeManageVisitSchedule = this.permissionService.userHasPermission(Permissions.manageVisitSchedule);
    this.privilegeScheduleForThemselves = this.permissionService.userHasPermission(Permissions.allowStaffToScheduleForThemselves);
    this.privilegeAllowRolesToSchedule = this.permissionService.userHasPermission(Permissions.allowRolesToSchedule);
    this.privilegeAllowEditCompletedVisits = this.permissionService.userHasPermission(Permissions.allowEditCompletedVisits);
    
    // Check if therapy fields should be shown
    this.enableTherapyFields = this.sharedService.getConfigValue('visit_enable_therapy_fields') === '1';
    // Check if mileage field should be shown and required
    this.enableTotalMileageField = this.sharedService.getConfigValue('visit_enable_total_mileage_field') === '1';
    // Check if drive time field should be shown and required
    this.enableDriveTimeField = this.sharedService.getConfigValue('visit_enable_drive_time_field') === '1';
  }

  canEditCompletedVisits(): boolean {
    if (!this.privilegeAllowEditCompletedVisits) {
      return false;
    }

    const hasCreateModifyPermission = this.privilegeScheduleForThemselves || this.privilegeManageVisitSchedule;

    return hasCreateModifyPermission;
  }

  shouldAllowVisitEditing(): boolean {
    return !this.isReviewCompleted && (!this.isCompleted || this.canEditCompletedVisits());
  }

  changeDateFormat(value: string, format: string): string {
    return formatDate(value, format);
  }
  closeRecurrence() {
    this.createVisitForm.patchValue({
      recurrence: false
    });
    this.createVisitForm.controls['scheduleCadence'].reset(Constants.daily);
    this.deleteRecurrence();
    this.scheduleTab = false;
    this.dailyUntilDateShow = true;
  }
  deleteRecurrence() {
    this.createVisitForm.controls['scheduleCadence'].reset();
    this.createVisitForm.controls['repeatEvery'].reset('1');
    this.createVisitForm.controls['monthly'].reset();
    this.createVisitForm.controls['weekly'].reset();
    this.createVisitForm.controls['endOn'].reset();
    this.repeatSchedules = Constants.repeatSchedules;
    this.dailyUntilDateShow = false;
  }
  staffStatusChange() {
    if (!this.canChangeVisitStatus()) {
      const originalStatus = this.visitData?.staffVisitStatusId || Constants.staffVisitStatus[0].value;
      this.createVisitForm.patchValue({
        staffPartnerVisitStatus: originalStatus
      }, { emitEvent: false });
      this.showPermissionDeniedMessage();
      return;
    }

    this.checkStaffStatus();
    this.checkUserStatus();
    if (this.pageLoaderStaffStatus) {
      this.pageLoaderStaffStatus = false;
    } else {
      if (
        this.createVisitForm.controls.staffPartnerVisitStatus.value !== Constants.staffVisitStatus[5].value &&
        this.createVisitForm.controls.staffPartnerVisitStatus.value !== Constants.staffVisitStatus[3].value
      ) {
        this.createVisitForm.get('staffReasonForCancel').clearValidators();
        this.createVisitForm.get('staffReasonForCancel').updateValueAndValidity();
        this.createVisitForm.controls['staffReasonForCancel'].reset('');
      } else {
        this.createVisitForm.controls['staffReasonForCancel'].reset('');
      }
    }
  }

  checkStaffStatus() {
    if (this.createVisitForm.value.staffPartnerVisitStatus === Constants.staffVisitStatus[4].value) {
      this.createVisitForm.patchValue({
        patientVisitStatus: Constants.patientVisitStatus[4].value
      });
      if (this.enableTotalMileageField) {
        this.createVisitForm.get('totalMileage').setValidators([Validators.required]);
        this.createVisitForm.get('totalMileage').clearValidators();
      }
      if (this.enableDriveTimeField) {
        this.createVisitForm.get('totalDriveTime').setValidators([Validators.required]);
        this.createVisitForm.get('totalDriveTime').updateValueAndValidity();
      }
    } else if (!this.createVisitForm.value.actualTimeIn) {
      this.createVisitForm.patchValue({
        actualDate: '',
        actualTimeIn: '',
        actualTimeOut: ''
      });
    }
    
  }

  recurrenceChange() {
    if (!this.visitKey) {
      if (this.createVisitForm.value.recurrence) {
        this.createVisitForm.patchValue({
          endOn: this.createVisitForm.value.recurrence ? moment(this.createVisitForm.value.endDate).add(1, 'd').format(Constants.dateFormat.mdy) : ''
        });
        this.endOnMinDate = moment(this.createVisitForm.value.endDate).add(1, 'd').format(Constants.dateFormat.ymd);
      } else {
        this.createVisitForm.value.endOn = '';
        this.createVisitForm.patchValue({ scheduleCadence: Constants.schedulesCadence[0].value });
      }
      this.checkStartAndEndDate();
    }
  }

  checkStartAndEndDate() {
    if (this.createVisitForm.value.recurrence) {
      if (
        moment(moment(this.createVisitForm.value.startDate).format(Constants.dateFormat.ymd)).isSame(
          moment(this.createVisitForm.value.endDate).format(Constants.dateFormat.ymd)
        )
      ) {
        this.repeatSchedules = this.repeatSchedules.map((item) => {
          item.disabled = false;
          return item;
        });
        this.createVisitForm.patchValue({ scheduleCadence: Constants.schedulesCadence[1].value });
      } else {
        this.repeatSchedules = this.repeatSchedules.map((item, i: number) => {
          if (i === 0) {
            item.disabled = true;
          } else {
            item.disabled = false;
          }
          return item;
        });

        this.createVisitForm.controls['scheduleCadence'].reset(Constants.weekly);
        if (this.createVisitForm.value.scheduleCadence === Constants.weekly) {
          this.scheduleTab = true;
          this.scheduleCadenceChange();
        }
      }
    }
  }

  toggleAccordion = () => {
    const nativeEl = this.accordionGroup;
    if (nativeEl.value === Constants.open) {
      nativeEl.value = undefined;
    } else {
      nativeEl.value = Constants.open;
    }
  };

  expandFirstErrorSectionAndScroll(): void {
    this.clearScrollTimeouts();

    const errorSections = [
      {
        id: 'patient-information-accordion',
        hasError: () => this.hasPatientInformationErrors()
      },
      {
        id: 'schedule-details-accordion',
        hasError: () => this.hasScheduleDetailsErrors()
      },
      {
        id: 'schedule-status-accordion',
        hasError: () => this.hasScheduleStatusErrors()
      },
      {
        id: 'additional-details-accordion',
        hasError: () => this.hasAdditionalDetailsErrors()
      }
    ];

    const firstErrorSection = errorSections.find((section) => section.hasError());

    if (firstErrorSection) {
      if (this.accordionGroupMain) {
        this.accordionGroupMain.value = firstErrorSection.id;
        this.accordionExpandTimeoutId = window.setTimeout(() => {
          if (this.accordionGroupMain && this.accordionGroupMain.value !== firstErrorSection.id) {
            this.accordionGroupMain.value = firstErrorSection.id;
          }
        }, CreateVisitPage.ACCORDION_EXPAND_TIMEOUT);
      }

      this.scrollToErrorTimeoutId = window.setTimeout(() => {
        const sectionElement = document.getElementById(firstErrorSection.id);
        if (sectionElement) {
          const errorTextElements = sectionElement.querySelectorAll('.validation-error-text');
          let firstVisibleError = null;

          if (errorTextElements.length > 0) {
            for (let i = 0; i < errorTextElements.length; i += 1) {
              const errorElement = errorTextElements[i] as HTMLElement;
              if (errorElement.offsetParent !== null && errorElement.offsetHeight > 0) {
                firstVisibleError = errorElement;
                break;
              }
            }
          }

          if (firstVisibleError) {
            firstVisibleError.scrollIntoView({
              behavior: 'smooth',
              block: 'center',
              inline: 'nearest'
            });
          } else {
            sectionElement.scrollIntoView({
              behavior: 'smooth',
              block: 'start',
              inline: 'nearest'
            });
          }
        }
      }, CreateVisitPage.SCROLL_TO_ERROR_TIMEOUT);
    }
  }

  hasPatientInformationErrors(): boolean {
    const hasPatientNameError = this.isControlInvalidAndTouched(this.formControl.patientName);
    const hasSiteError = this.isControlInvalidAndTouched(this.formControl.siteNames);

    let hasTherapyErrors = false;
    if (this.enableTherapyFields) {
      const hasTherapyTypeError = this.isControlInvalidAndTouched(this.formControl.therapyType, true);
      const hasDosageError = this.isControlInvalidAndTouched(this.formControl.dosageAppointment, true);
      hasTherapyErrors = hasTherapyTypeError || hasDosageError;
    }

    // Visit location type validation (required for all visit types)
    const hasVisitLocationTypeError = this.isControlInvalidAndTouched(this.formControl.visitationType);

    // Visit location validations (only for AIC visit types)
    const isAICVisitType = this.createVisitForm.value.visitationType === this.VisitType.AIC;
    const hasVisitLocationError = isAICVisitType && this.isControlInvalidAndTouched(this.formControl.visitationTypeLocation, true);
    const hasVisitChairError =
      isAICVisitType && this.createVisitForm.value.visitationTypeLocation && this.isControlInvalidAndTouched(this.formControl.visitChair, true);

    return hasPatientNameError || hasSiteError || hasTherapyErrors || hasVisitLocationTypeError || hasVisitLocationError || hasVisitChairError;
  }

  hasScheduleDetailsErrors(): boolean {
    return this.isControlInvalidAndTouched(this.formControl.visitTitle) || this.dateTimeValidator || this.endOnDateTimeValidator;
  }

  hasScheduleStatusErrors(): boolean {
    const hasVisitAssignedToError = this.isRequiredField('visitAssignedTo') && this.isControlInvalidAndTouched(this.formControl.visitAssignedTo);

    const hasOrganizationError =
      this.formControl.subcontracted.value && this.isControlInvalidAndTouched(this.formControl.organization);

    return hasVisitAssignedToError || hasOrganizationError;
  }

  hasAdditionalDetailsErrors(): boolean {
    return this.fileError || this.fileSizeError;
  }

  siteChange() {
    this.createVisitForm.controls['patientName'].reset();
    this.patientID = '';
    this.createVisitForm.controls['phoneNumber'].reset();
    this.createVisitForm.controls['visitAddress'].reset();
    this.countryId = this.userDetails.countryCode ? this.userDetails.countryCode : this.defaultCountryId;
    this.setCountryIsoCode();
    this.staffIds = [];
    this.createVisitForm.controls['visitAssignedTo'].reset();
    this.getCountyDetails();
    this.createVisitForm.controls['visitationTypeLocation'].reset();
    this.createVisitForm.controls['visitChair'].reset();
    this.selectedVisitLocation = {};
    this.selectedVisitChair = {};
    if (this.enableTherapyFields) {
      this.createVisitForm.controls['therapyType'].reset();
      this.createVisitForm.controls['dosageAppointment'].reset();
      this.selectedTherapyType = {};
      this.selectedDosageAppointment = {};
      this.setVisitDurationFromTherapy();
    }
  }

  uploadAttachment(visitKey: string, response: ApiResponse<any>): void {
    const visitattachment = this.visitFiles;
    const uploadData = new FormData();
    uploadData.append('visit', visitKey);
    visitattachment.forEach((item) => {
      uploadData.append('visitattachment[]', item);
    });
    uploadData.append('removeattachment', this.attachmentKeysToRemove.toString());
    if (visitKey && !this.fileSizeError && (this.visitFiles?.length || !isBlank(this.attachmentKeysToRemove))) {
      this.sharedService.consoloLoader = true;
      this.visitScheduleService.fileAttach(uploadData).subscribe(
        (res: ApiResponse<AttachmentData[]>) => {
          if (!res.success) {
            this.commonService.showMessage(this.commonService.getTranslateData('ERROR_MESSAGES.FILE_ATTACHMENT_FAILED'));
          } else if (isPresent(this.visitFiles)) {
            this.commonService.showMessage(this.commonService.getTranslateData('SUCCESS_MESSAGES.VISIT_FILE_ATTACHMENT_SUCCESS'));
          }
          this.sharedService.consoloLoader = false;
          this.visitFiles = [];
          this.visitMessages(response);
        },
        () => {
          this.commonService.showMessage(this.commonService.getTranslateData('ERROR_MESSAGES.FILE_ATTACHMENT_FAILED'));
          this.sharedService.consoloLoader = false;
          this.visitFiles = [];
          this.visitMessages(response);
        }
      );
    }
  }

  getVisitDetails(): void {
    this.sharedService.isLoading = true;
    if (this.visitKey && this.calendarEventData) {
      this.visitScheduleService.getVisits('', this.visitKey).subscribe((result) => {
        if (result && result.data) {
          this.patientVisitStatusId = Number(result.data.patientVisitStatusId);
          this.staffVisitStatusId = Number(result.data.staffVisitStatusId);
          const formData: IModifyVistFormData = {
            siteName: Number(result.data.siteId),
            patientName: result.data.patientArray?.length ? this.sharedService.patientNameDisplay(result.data.patientArray[0]) : '',
            phoneNumber: result.data.phoneNumber,
            visitationType: isBlank(result.data.visitType) || result.data.visitType === '0' ? null : result.data.visitType,
            visitationTypeLocation: result.data.visitTypeLocation,
            visitChair: result.data.visitChair,
            therapyType: isBlank(result.data.therapyType) || result.data.therapyType === '0' ? '' : result.data.therapyType,
            dosageAppointment: isBlank(result.data.dosageAppointment) || result.data.dosageAppointment === '0' ? '' : result.data.dosageAppointment,
            visitAddress: result.data.visitAddress,
            visitTitle: result.data.eventVisitTitle,
            visitDetails: result.data.visitationDetails,
            timeZone: result.data.locationTimeZone,
            startDate: formatDate(this.calendarEventData.startDate, this.dateFormat.mdy),
            startTime: formatDate(this.calendarEventData.startDate, this.dateFormat.h2ma),
            endDate: formatDate(this.calendarEventData.endDate, this.dateFormat.mdy),
            endTime: formatDate(this.calendarEventData.endDate, this.dateFormat.h2ma),
            billable: result.data.isBillable === Constants.isBillable ? true : false,
            visitAssignedTo: result.data.assignedUserName,
            subcontracted: result.data.subContractedStatus === Constants.isSubContracted,
            organization: result.data.organizationName,
            staffPartnerVisitStatus: Number(result.data.staffVisitStatusId),
            staffReasonForCancel: result.data.staffReasonForCancel,
            actualDate: result.data.actualTimeIn
              ? formatDate(
                convertDateTimeToCustomFormat(
                  formatDate(result.data.actualTimeIn, Constants.dateFormat.ymd),
                  formatDate(result.data.actualStartTime, Constants.dateFormat.hhmm0)
                ),
                this.dateFormat.mdy
              )
              : '',
            actualTimeIn: result.data.actualStartTime
              ? formatDate(
                convertDateTimeToCustomFormat(
                  formatDate(result.data.actualStartTime, Constants.dateFormat.ymd),
                  formatDate(result.data.actualStartTime, Constants.dateFormat.hhmm0)
                ),
                this.dateFormat.h2ma
              )
              : '',
            actualTimeOut: result.data.actualEndTime
              ? formatDate(
                convertDateTimeToCustomFormat(
                  formatDate(result.data.actualEndTime, Constants.dateFormat.ymd),
                  formatDate(result.data.actualEndTime, Constants.dateFormat.hhmm0)
                ),
                this.dateFormat.h2ma
              )
              : '',
            totalMileage: isBlank(result.data.totalMileage) ? '' : parseInt(result.data.totalMileage).toString(),
            totalDriveTime: this.formatDriveTime(result.data.totalDriveTime),
            patientVisitStatus: Number(result.data.patientVisitStatusId),
            additionalDetails: result.data.additionalDetails,
            endOn: result.data.untilDate
              ? formatDate(convertDateTimeToCustomFormat(result.data.untilDate, result.data.endTime), this.dateFormat.mdy)
              : '',
            requestPatientConfirmation: this.sharedService.isConfigTrue(result.data.requestPatientConfirmation),
            confirmationBeforeStaffConfirm: result.data.confirmationBeforeStaffConfirm,
            sendPatientNotification: this.sharedService.isConfigTrue(result.data.sendPatientNotification),
            setPatientReminder: this.sharedService.isConfigTrue(result.data.setPatientReminder),
            scheduleCadence: result.data.repeatType,
            patientReasonForCancel: result.data.patientReasonForCancel,
            recurrence: result.data.repeatType !== Constants.none,
            repeatEvery: result.data.repeatInterval,
            monthly: result.data.repeatMonthType,
            weekly: result.data.repeatDays ? result.data.repeatDays.split(',') : []
          };
          this.visitData = {
            ...result.data,
            startDate: formData.startDate,
            endDate: formData.endDate,
            startTime: formData.startTime,
            endTime: formData.endTime
          };
          if (!this.visitData?.actualTimeIn) {
            this.showStartButton = true;
          }
          if (!isBlank(result.data.visitTypeLocation)) {
            this.visitScheduleService.getVisitTypeLocation(result.data.visitTypeLocation).subscribe((data) => {
              this.selectedVisitLocation = data;
            });
          }
          if (!isBlank(result.data.visitChair)) {
            this.visitScheduleService.getVisitChair(result.data.visitChair).subscribe((data) => {
              this.selectedVisitChair = data;
            });
          }
          if (this.enableTherapyFields) {
            if (!isBlank(result.data.therapyType) && result.data.therapyType !== '0') {
              this.visitScheduleService.getTherapyTypeById(result.data.therapyType).subscribe((data) => {
                this.selectedTherapyType = data;
              });
            }
            if (!isBlank(result.data.dosageAppointment) && result.data.dosageAppointment !== '0') {
              this.visitScheduleService.getTherapyById(result.data.dosageAppointment).subscribe((data) => {
                this.selectedDosageAppointment = data;
                this.setVisitDurationFromTherapy(true);
              });
            }
          }
          this.createVisitForm.patchValue({
            siteNames: [Number(result.data.siteId)],
            seriesStartDate: formatDate(convertDateTimeToCustomFormat(result.data.eventStartAt, this.calendarEventData.startTime), this.dateFormat.mdy),
            seriesEndDate: formatDate(convertDateTimeToCustomFormat(result.data.eventEndAt, this.calendarEventData.endTime), this.dateFormat.mdy),
            revisedEndDate: formatDate(convertDateTimeToCustomFormat(result.data.eventEndAt, this.calendarEventData.endTime), this.dateFormat.mdy),
            revisedStartTime: formatDate(
              convertDateTimeToCustomFormat(this.calendarEventData.start, this.calendarEventData.startTime),
              this.dateFormat.h2ma
            ),
            revisedEndTime: formatDate(convertDateTimeToCustomFormat(this.calendarEventData.end, this.calendarEventData.endTime), this.dateFormat.h2ma),
            driveTimeHours: isBlank(formData.totalDriveTime) ? '00' : formData.totalDriveTime.split(':')[0],
            driveTimeMinutes: isBlank(formData.totalDriveTime) ? '00' : formData.totalDriveTime.split(':')[1],
            ...formData
          });
          this.visitationTypeChange();

          this.initialStartDate = formatDate(this.createVisitForm.value.startDate, Constants.dateFormat.ymd);
          if (formData.actualDate) {
            if (formData.actualTimeIn) {
              this.fetchActualDateTimeIn = formatDate(`${formData.actualDate} ${formData.actualTimeIn}`, Constants.dateFormat.hhmm0);
            }
            if (formData.actualTimeOut) {
              this.fetchActualDateTimeOut = formatDate(`${formData.actualDate} ${formData.actualTimeOut}`, Constants.dateFormat.hhmm0);
            }
          }
          this.checkUserStatus();
          this.checkStaffStatus();
          this.previousEndAt = this.calendarEventData.end
            ? convertDateTimeToIsoFormat(this.calendarEventData.end, this.calendarEventData.endTime)
            : '';
          this.previousStartAt = this.calendarEventData.start
            ? convertDateTimeToIsoFormat(this.calendarEventData.start, this.calendarEventData.startTime)
            : '';
          this.startAt = convertDateTimeToCustomFormat(result.data.startAt, result.data.startTime);
          this.endAt = convertDateTimeToCustomFormat(result.data.endAt, result.data.endTime);
          this.patientAddress = result.data.patientArray?.length ? result.data.patientArray[0].shippingAddress : '';
        }
        if (!isBlank(result.data.attachment)) {
          this.savedFiles = result.data.attachment;
          this.savedFiles.forEach((item) => {
            item['formattedFileName'] = item.name.substring(Constants.uploadedVisitFileNameDefaultSubstringLength);
          });
        }

        this.patientID = result.data.patientId;
        this.staffIds = result.data.staffId ? result.data.staffId.split(',') : '';
        this.countryId = result.data.countryCode
          ? result.data.countryCode.charAt(0) === '+'
            ? result.data.countryCode
            : `+${result.data.countryCode}`
          : this.defaultCountryId;

        this.countryIsoCode = isPresent(result.data.countryIsoCode) ? result.data.countryIsoCode : environment.defaultCountryFlag;
        this.getCountyDetails();
        this.checkRecurrenceVisit = result.data.repeatType !== Constants.none;
        this.checkDraftVisit = result.data.visitStatusId === VisitScheduleConstants.isDraft;
        this.isCompleted =
          this.patientVisitStatusId === Constants.patientVisitStatus[4].value && this.staffVisitStatusId === Constants.staffVisitStatus[4].value;
        this.isReviewCompleted = result.data.visitStatusId === VisitScheduleConstants.reviewCompleted;
        this.sharedService.isLoading = false;
        this.setTimePicker();
        this.fetchRevisedStartDateTime = formatTime(this.createVisitForm.value.startTime, Constants.dateFormat.h2ma, Constants.dateFormat.hhmm0);
        this.fetchRevisedEndDateTime = formatTime(this.createVisitForm.value.endTime, Constants.dateFormat.h2ma, Constants.dateFormat.hhmm0);

        this.viewRecurrenceTab = !this.visitKey || this.checkDraftVisit;
        this.organization = result.data.organizationName;
        this.selectedUserID = result.data.assignedToUserId;
        this.updateOrganizationValidators();
        if (this.staffIds?.length) {
          this.getStaffList();
        }
        if (result.data.visitType === VisitScheduleConstants.homeVisitType) {
          this.patientAddress = result.data.patientArray?.length ? result.data.patientArray[0].shippingAddress : result.data.visitAddress;
        }
        if (!this.shouldAllowVisitEditing()) {
          this.createVisitForm.disable();
        }
        this.scheduleCadenceChange();
        if (this.visitKey) {
          this.onEditLoad();
        }
        this.updateClinicianFieldRequirement();

      });
    } else {
      this.router.navigate([PageRoutes.scheduleCenterVisits]);
    }
  }

  canCompleteVisit(): boolean {
    if (!this.visitData || !this.staffIds?.length) {
      return false;
    }

    const currentUserId = this.sharedService.userData?.userId;
    if (!currentUserId) {
      return false;
    }

    if (this.privilegeManageVisitSchedule) {
      return true;
    }

    const isAssignedToCurrentUser = this.staffIds.includes(currentUserId.toString());
    return isAssignedToCurrentUser;
  }

  canEditVisit(): boolean {
    if (!this.visitData) {
      return false;
    }

    const currentUserId = this.sharedService.userData?.userId;
    if (!currentUserId) {
      return false;
    }

    if (!this.privilegeManageVisitSchedule && !this.privilegeScheduleForThemselves) {
      return false;
    }

    const visitCreatedBy = this.visitData.createdBy;
    const isCreatedByCurrentUser = visitCreatedBy === currentUserId.toString();

    if (this.privilegeManageVisitSchedule) {
      if (isCreatedByCurrentUser) {
        return true;
      }

      if (this.staffIds?.length) {
        return true;
      }
    }

    if (this.privilegeScheduleForThemselves) {
      return isCreatedByCurrentUser;
    }

    return false;
  }

  canChangeVisitStatus(): boolean {
    return this.canCompleteVisit();
  }

  canEditVisitFields(): boolean {
    return this.canCompleteVisit();
  }

  canAccessEditFlow(): boolean {
    return this.canEditVisit();
  }

  showPermissionDeniedMessage(): void {
    const message = this.commonService.getTranslateData('ERROR_MESSAGES.PERMISSION_DENIED');
    this.commonService.showMessage(message);
  }

  applyPermissionBasedFormState(): void {
    const completeVisitFields = [
      'actualTimeIn', 'actualTimeOut', 'totalMileage',
      'driveTimeHours', 'driveTimeMinutes', 'staffPartnerVisitStatus'
    ];

    const editVisitFields = [
      'visitTitle', 'patientName', 'visitAssignedTo', 'visitDetails',
      'visitAddress', 'phoneNumber', 'countryCode',
      'siteNames', 'siteName', 'visitationType', 'visitationTypeLocation', 'visitChair',
      'startDate', 'startTime', 'endDate', 'endTime', 'timeZone',
      'seriesStartDate', 'seriesEndDate', 'revisedStartDate', 'revisedEndDate',
      'revisedStartTime', 'revisedEndTime',
      'scheduleCadence', 'repeatEvery', 'weekly', 'monthly', 'repeatSchedule',
      'recurrence', 'endOn', 'showRevisedTimeUpdate', 'showMultipleDatesSelection', 'multipleDates',
      'billable', 'subcontracted', 'organization',
      'requestPatientConfirmation', 'sendPatientNotification', 'setPatientReminder',
      'confirmationBeforeStaffConfirm', 'additionalDetails'
    ];

    if (!this.canEditVisitFields()) {
      completeVisitFields.forEach(field => {
        const control = this.createVisitForm.get(field);
        if (control) {
          control.disable();
        }
      });
    }

    if (!this.canAccessEditFlow()) {
      editVisitFields.forEach(field => {
        const control = this.createVisitForm.get(field);
        if (control) {
          control.disable();
        }
      });
    }
  }

  checkUserStatus() {
    if (this.visitKey) {
      if (this.createVisitForm.value.staffPartnerVisitStatus === Constants.staffVisitStatus[1].value) {
        this.staffPartnerVisitStatusData = Constants.staffVisitStatus.map((x): IVisitStatus => {
          x.disabled =
            x.value === Constants.staffVisitStatus[0].value ||
            (x.value === Constants.staffVisitStatus[3].value && this.staffVisitStatusId === Constants.staffVisitStatus[2].value);
          return x;
        });
        if (this.visitData.staffVisitStatus === Constants.staffVisitStatus[2].text) {
          this.staffPartnerVisitStatusData.splice(0, 1);
        }
        this.patientVisitStatusData = Constants.patientVisitStatus.map((x): IVisitStatus => {
          x.disabled = x.value !== Constants.patientVisitStatus[0].value;
          return x;
        });
        this.createVisitForm.patchValue({
          patientVisitStatus: Constants.patientVisitStatus[0].value
        });
      } else if (this.createVisitForm.value.staffPartnerVisitStatus === Constants.staffVisitStatus[2].value) {
        this.staffPartnerVisitStatusData = Constants.staffVisitStatus.map((x): IVisitStatus => {
          x.disabled =
            x.value === Constants.staffVisitStatus[0].value ||
            (x.value === Constants.staffVisitStatus[3].value && this.staffVisitStatusId === Constants.staffVisitStatus[2].value);
          return x;
        });
        if (this.staffPartnerVisitStatusData[0].text === Constants.staffVisitStatus[0].text) {
          this.staffPartnerVisitStatusData.splice(0, 1);
        }
        this.patientVisitStatusData = Constants.patientVisitStatus.map((x): IVisitStatus => {
          x.disabled =
            x.value !== Constants.patientVisitStatus[0].value &&
            x.value !== Constants.patientVisitStatus[1].value &&
            x.value !== Constants.patientVisitStatus[2].value &&
            x.value !== Constants.patientVisitStatus[3].value;
          return x;
        });
        if (
          this.staffVisitStatusId === Constants.staffVisitStatus[4].value ||
          (this.staffVisitStatusId === Constants.staffVisitStatus[2].value &&
            this.createVisitForm.value.staffPartnerVisitStatus !== Constants.staffVisitStatus[4].value)
        ) {
          this.createVisitForm.patchValue({
            patientVisitStatus: this.patientVisitStatusId
          });
        }
      } else if (this.createVisitForm.value.staffPartnerVisitStatus === Constants.staffVisitStatus[3].value) {
        this.staffPartnerVisitStatusData = Constants.staffVisitStatus.map((x): IVisitStatus => {
          x.disabled = x.value === Constants.staffVisitStatus[0].value;
          return x;
        });

        this.patientVisitStatusData = Constants.patientVisitStatus.map((x): IVisitStatus => {
          x.disabled = true;
          return x;
        });

        this.createVisitForm.patchValue({
          patientVisitStatus: this.patientVisitStatusId
        });
      } else if (this.createVisitForm.value.staffPartnerVisitStatus === Constants.staffVisitStatus[4].value) {
        this.staffPartnerVisitStatusData = Constants.staffVisitStatus.map((x): IVisitStatus => {
          x.disabled =
            this.staffVisitStatusId === Constants.staffVisitStatus[4].value ||
            x.value === Constants.staffVisitStatus[0].value ||
            (x.value === Constants.staffVisitStatus[3].value && this.staffVisitStatusId === Constants.staffVisitStatus[2].value);
          return x;
        });

        this.patientVisitStatusData = Constants.patientVisitStatus.map((x): IVisitStatus => {
          x.disabled = true;
          return x;
        });
        this.createVisitForm.patchValue({
          patientVisitStatus: Constants.patientVisitStatus[4].value
        });
      } else if (this.createVisitForm.value.staffPartnerVisitStatus === Constants.staffVisitStatus[5].value) {
        this.staffPartnerVisitStatusData = Constants.staffVisitStatus.map((x): IVisitStatus => {
          x.disabled =
            x.value === Constants.staffVisitStatus[0].value ||
            (x.value === Constants.staffVisitStatus[3].value && this.staffVisitStatusId === Constants.staffVisitStatus[2].value) ||
            (x.value === Constants.staffVisitStatus[4].value && !this.isCurrentTimeAfterStartTime);
          return x;
        });
        if (
          this.visitData.staffVisitStatus === Constants.staffVisitStatus[2].text &&
          this.staffPartnerVisitStatusData[0].value === Constants.staffVisitStatus[0].value
        ) {
          this.staffPartnerVisitStatusData.splice(0, 1);
        }

        this.patientVisitStatusData = Constants.patientVisitStatus.map((x): IVisitStatus => {
          x.disabled = x.value !== Constants.patientVisitStatus[0].value;
          return x;
        });

        this.createVisitForm.patchValue({
          patientVisitStatus: Constants.patientVisitStatus[1].value
        });
      } else if (this.isSubcontractedWithOrganization()) {
        this.staffPartnerVisitStatusData = Constants.staffVisitStatus.map((x): IVisitStatus => {
          x.disabled =
            x.value !== Constants.staffVisitStatus[1].value &&
            x.value !== Constants.staffVisitStatus[2].value &&
            x.value !== Constants.staffVisitStatus[0].value;
          return x;
        });
        if (this.createVisitForm.value.staffPartnerVisitStatus === Constants.staffVisitStatus[2].value) {
          this.patientVisitStatusData = Constants.patientVisitStatus.map((x): IVisitStatus => {
            x.disabled = x.value !== Constants.patientVisitStatus[0].value && x.value !== Constants.patientVisitStatus[1].value;
            return x;
          });
        } else {
          this.patientVisitStatusData = Constants.patientVisitStatus.map((x): IVisitStatus => {
            x.disabled = x.value !== Constants.patientVisitStatus[0].value;
            return x;
          });
          this.createVisitForm.patchValue({
            patientVisitStatus: Constants.patientVisitStatus[0].value
          });
        }
      } else if (
        this.createVisitForm.value.visitationType === VisitType.AIC &&
        !this.staffIds?.length &&
        this.createVisitForm.value.staffPartnerVisitStatus !== Constants.staffVisitStatus[4].value
      ) {
        this.staffPartnerVisitStatusData = Constants.staffVisitStatus.map((x): IVisitStatus => {
          return { ...x, disabled: false };
        });
      } else {
        this.staffPartnerVisitStatusData = Constants.staffVisitStatus.map((x): IVisitStatus => {
          x.disabled =
            x.value !== Constants.staffVisitStatus[0].value || (x.value !== Constants.staffVisitStatus[4].value && this.isCurrentTimeAfterStartTime);
          return x;
        });

        this.patientVisitStatusData = Constants.patientVisitStatus.map((x): IVisitStatus => {
          x.disabled = true;
          return x;
        });
      }
    } else {
      if (this.staffIds?.length) {
        this.staffPartnerVisitStatusData = Constants.staffVisitStatus.map((x): IVisitStatus => {
          x.disabled = x.value !== Constants.staffVisitStatus[1].value && x.value !== Constants.staffVisitStatus[2].value;
          return x;
        });
        if (this.createVisitForm.value.staffPartnerVisitStatus === Constants.staffVisitStatus[2].value) {
          this.patientVisitStatusData = Constants.patientVisitStatus.map((x): IVisitStatus => {
            x.disabled = x.value !== Constants.patientVisitStatus[0].value && x.value !== Constants.patientVisitStatus[1].value;
            return x;
          });
        } else {
          this.patientVisitStatusData = Constants.patientVisitStatus.map((x): IVisitStatus => {
            x.disabled = x.value !== Constants.patientVisitStatus[0].value;
            return x;
          });
          this.createVisitForm.patchValue({
            patientVisitStatus: Constants.patientVisitStatus[0].value
          });
        }
      } else if (!this.createVisitForm.value.visitAssignedTo && this.isSubcontractedWithOrganization()) {
        this.staffPartnerVisitStatusData = Constants.staffVisitStatus.map((x): IVisitStatus => {
          x.disabled =
            x.value !== Constants.staffVisitStatus[1].value &&
            x.value !== Constants.staffVisitStatus[2].value &&
            x.value !== Constants.staffVisitStatus[0].value;
          return x;
        });
        if (this.createVisitForm.value.staffPartnerVisitStatus === Constants.staffVisitStatus[2].value) {
          this.patientVisitStatusData = Constants.patientVisitStatus.map((x): IVisitStatus => {
            x.disabled = x.value !== Constants.patientVisitStatus[0].value && x.value !== Constants.patientVisitStatus[1].value;
            return x;
          });
        } else {
          this.patientVisitStatusData = Constants.patientVisitStatus.map((x): IVisitStatus => {
            x.disabled = x.value !== Constants.patientVisitStatus[0].value;
            return x;
          });
          this.createVisitForm.patchValue({
            patientVisitStatus: Constants.patientVisitStatus[0].value
          });
        }
      } else if (!this.createVisitForm.value.visitAssignedTo && this.createVisitForm.value.visitationType === VisitType.AIC && this.createVisitForm.value.visitChair) {
        this.staffPartnerVisitStatusData = Constants.staffVisitStatus.map((x): IVisitStatus => {
          x.disabled =
            x.value !== Constants.staffVisitStatus[1].value &&
            x.value !== Constants.staffVisitStatus[2].value
          return x;
        });
        
        this.patientVisitStatusData = Constants.patientVisitStatus.map((x): IVisitStatus => {
          x.disabled = x.value !== Constants.patientVisitStatus[0].value;
          return x;
        });

        this.createVisitForm.patchValue({
          staffPartnerVisitStatus: Constants.staffVisitStatus[2].value
        });
        
      } else {
        this.staffPartnerVisitStatusData = Constants.staffVisitStatus.map((x): IVisitStatus => {
          x.disabled = x.value !== Constants.staffVisitStatus[0].value;
          return x;
        });

        this.patientVisitStatusData = Constants.patientVisitStatus.map((x): IVisitStatus => {
          x.disabled = x.value !== Constants.patientVisitStatus[0].value;
          return x;
        });
      }
    }
  }

  updateVisit(): void {
    let buttons;
    let cssClass;
    let message = 'LABELS.YOU_ARE_GOING_TO_UPDATE_THIS_VISIT';
    if (this.visitKey) {
      if (this.checkRecurrenceVisit) {
        buttons = [
          { text: 'BUTTONS.CANCEL', confirm: false, class: 'warn-btn alert-cancel' },
          { text: 'BUTTONS.SINGLE', confirm: true, class: 'warn-btn alert-ok', value: Constants.editType.single },
          { text: 'BUTTONS.ALL', confirm: true, class: 'warn-btn alert-ok', value: Constants.editType.series }
        ];
        cssClass = 'custom-alert';
        message = 'MESSAGES.VISIT_MODIFY_REPEATED_EVENTS_CONFIRM';
      } else {
        buttons = [
          { text: 'BUTTONS.CANCEL', confirm: false, class: 'warn-btn alert-cancel' },
          { text: 'BUTTONS.OK', confirm: true, class: 'warn-btn alert-ok' }
        ];
        cssClass = 'visit-alert';
        message = 'LABELS.YOU_ARE_GOING_TO_UPDATE_THIS_VISIT';
      }
      this.commonService
        .showAlert({
          message,
          header: 'MESSAGES.ARE_YOU_SURE',
          buttons,
          mode: 'ios',
          cssClass
        })
        .then((confirmation) => {
          if (confirmation === true) {
            this.editType = Constants.editType.series;
            this.manageVisit();
          } else if (confirmation === false) {
            this.commonService.closeAllAlert();
          } else {
            this.editType = confirmation;
            this.manageVisit();
          }
        });
    }
  }
  ionViewWillLeave() {
    this.createVisitForm.reset();
    this.initializeFormControls();
    this.visitKey = '';
    this.patientID = '';
    this.staffIds = [];
    this.commonService.dismissModal();
  }

  async presentImageViewerModal(item, index: number): Promise<void> {
    const componentProps = this.sharedService.fetchImageViewerModalProps(item, index);
    if (componentProps) {
      if (componentProps.type === Constants.documentTypes.pdf) {
        this.sharedService.isLoading = true;
        this.sharedService.presentPdfFromLink({ url: componentProps.url, source: AssetSource.SCHEDULE_CENTER }).then((data: any) => {
          this.sharedService.isLoading = false;
          if (data.status && isPresent(data.url)) {
            if (this.sharedService.platform.is('ios') && this.sharedService.platform.is('capacitor')) {
              this.browser.create(data.url, '_blank', this.sharedService.inAppBrowserOptions());
            } else {
              this.presentAdvancedViewerModal({ url: data.url, type: Constants.documentTypes.pdf, component: AdvancedViewerComponent });
            }
          } else if (this.sharedService.platform.is('capacitor')) {
            this.browser.create(componentProps.url, '_system');
          }
        });
      } else {
        this.presentAdvancedViewerModal({ ...componentProps, type: Constants.documentTypes.pdf, component: AdvancedViewerComponent });
      }
    }
  }
  async presentAdvancedViewerModal(componentProps) {
    const modal = await this.modalController.create({
      component: AdvancedViewerComponent,
      componentProps
    });
    return modal.present();
  }
  // timeZone selection based start date and time binding
  timeZoneChange(): void {
    if (this.isEditPage()) return;
    const defultTime = this.timeCalculator(`${this.selectedDateTime.date} ${this.selectedDateTime.time}`);
    this.createVisitForm.patchValue({
      startDate: formatDate(`${this.selectedDateTime.date} ${this.selectedDateTime.time}`, this.dateFormat.mdy),
      endDate: formatDate(`${this.selectedDateTime.date} ${this.selectedDateTime.time}`, this.dateFormat.mdy),
      startTime: moment(defultTime.setMinutes(defultTime.getMinutes() + 30)).format(Constants.dateFormat.hhmma),
      endTime: moment(defultTime.setMinutes(defultTime.getMinutes() + 60)).format(Constants.dateFormat.hhmma)
    });
    this.endOnMinDate = moment(this.createVisitForm.value.endDate).add(1, 'd').format(Constants.dateFormat.ymd);
  }

  visitAvailablityCheckandSave(): void {
    const createVisitFormData = this.createVisitForm.getRawValue();
    let availabilityParam: ICheckVisitAvailability = {
      startAt: convertDateTimeToIsoFormat(
        formatDate(createVisitFormData.startDate, Constants.dateFormat.ymd),
        formatDate([createVisitFormData.startDate, createVisitFormData.startTime].join(' '), this.dateFormat.hhmm0)
      ),
      endAt: convertDateTimeToIsoFormat(
        formatDate(createVisitFormData.endDate, Constants.dateFormat.ymd),
        formatDate([createVisitFormData.endDate, createVisitFormData.endTime].join(' '), this.dateFormat.hhmm0)
      ),
      until: createVisitFormData.endOn
        ? convertDateTimeToIsoFormat(
          formatDate(createVisitFormData.endOn, Constants.dateFormat.ymd),
            formatTime(createVisitFormData.endTime, this.dateFormat.h2ma, this.dateFormat.hhmm0)
        )
        : '',
      repeatType: createVisitFormData.recurrence
        ? createVisitFormData.scheduleCadence !== Constants.none
          ? createVisitFormData.scheduleCadence
          : Constants.none
        : Constants.none,
      repeatDays: createVisitFormData.recurrence
        ? createVisitFormData.weekly
          ? createVisitFormData.weekly.toString()
          : ''
        : '',
      repeatInterval: createVisitFormData.recurrence
        ? createVisitFormData.repeatEvery
          ? createVisitFormData.repeatEvery
          : 0
        : 0,
      repeatMonthType: createVisitFormData.recurrence ? (createVisitFormData.monthly ? createVisitFormData.monthly : '') : '',
      patientId: this.patientID,
      staffId: Array.isArray(this.staffIds) ? this.staffIds.join(', ') : ''
    };

    if (this.visitKey) {
      availabilityParam = {
        ...availabilityParam,
        visitKey: this.visitKey
      };
    }

    if (!createVisitFormData.visitAssignedTo && createVisitFormData.subcontracted && createVisitFormData.organization) {
      if (this.visitKey) {
        this.manageVisit();
      } else {
        this.saveVisitConfirm();
      }
    } else if (
      createVisitFormData.staffPartnerVisitStatus === Constants.staffVisitStatus[3].value ||
      createVisitFormData.staffPartnerVisitStatus === Constants.staffVisitStatus[5].value
    ) {
      if (!this.visitKey && !this.isDraft) {
        this.saveVisitConfirm();
      } else if (this.visitKey && !this.isDraft) {
        this.manageVisit();
      }
    } else {
      if (createVisitFormData.showMultipleDatesSelection && createVisitFormData.multipleDates) {
        const allParams = createVisitFormData.multipleDates.split(', ').map((date) => {
          return {
            startAt: convertDateTimeToIsoFormat(
              formatDate(date, Constants.dateFormat.ymd),
              formatDate([date, createVisitFormData.startTime].join(' '), this.dateFormat.hhmm0)
            ),
            endAt: convertDateTimeToIsoFormat(
              formatDate(date, Constants.dateFormat.ymd),
              formatDate([date, createVisitFormData.endTime].join(' '), this.dateFormat.hhmm0)
            ),
            until: '',
            repeatType: Constants.none,
            repeatDays: '',
            repeatInterval: 0,
            repeatMonthType: '',
            patientId: this.patientID,
            staffId: this.staffIds.join(', ')
          }
        });
        
        forkJoin(allParams.map((param) => this.visitScheduleService.checkVisitAvailability(param))).subscribe((response: any[]) => {
          const datesNotAvailable = [];
          response.forEach((res: any, i) => {
            if (res.success && !isBlank(res.data.visit_key)) {
              datesNotAvailable.push(allParams[i].startAt);
            }
          })
          if (datesNotAvailable.length) {
            const buttons = [
              { text: 'BUTTONS.CANCEL', confirm: false, class: 'warn-btn alert-cancel' },
              { text: 'BUTTONS.CONTINUE', confirm: true, class: 'warn-btn alert-ok' }
            ];

            this.commonService
              .showAlert({
                message: this.commonService.getTranslateData('MESSAGES.VISIT_POTENTIAL_SCHEDULE_MULTIPLE') +
                  '\n Date(s):' +
                  datesNotAvailable
                    .map((date) => moment(date).format('MM/DD/YYYY'))
                    .join(', '),
                header: 'MESSAGES.ARE_YOU_SURE',
                buttons,
                mode: 'ios',
                cssClass: 'visit-alert'
              })
              .then((confirmation) => {
                if (confirmation) {
                  if (!this.visitKey && !this.isDraft) {
                    this.saveVisitConfirm();
                  }
                }
                this.commonService.closeAllAlert();
              });
          } else {
            this.saveVisitConfirm();
          }
        });
      } else {
        this.visitScheduleService.checkVisitAvailability(availabilityParam).subscribe((response) => {
          if (response && response.success && response.status.message === VisitScheduleConstants.scheduleExist) {
            this.availabilityCheckAlert();
          } else if (!this.visitKey && !this.isDraft) {
            this.saveVisitConfirm();
          } else if (this.visitKey && !this.isDraft) {
            this.manageVisit();
          }
        });
      }
    }
  }

  // check visit exists or not, then save or update
  availabilityCheckAlert(): void {
    const buttons = [
      { text: 'BUTTONS.CANCEL', confirm: false, class: 'warn-btn alert-cancel' },
      { text: 'BUTTONS.CONTINUE', confirm: true, class: 'warn-btn alert-ok' }
    ];

    this.commonService
      .showAlert({
        message: 'LABELS.CONFIRM_VISIT_OVERLAPPING',
        header: 'MESSAGES.ARE_YOU_SURE',
        buttons,
        mode: 'ios',
        cssClass: 'visit-alert'
      })
      .then((confirmation) => {
        if (confirmation) {
          if (!this.visitKey && !this.isDraft) {
            this.saveVisitConfirm();
          } else if (this.visitKey && !this.isDraft) {
            this.manageVisit();
          }
        }
        this.commonService.closeAllAlert();
      });
  }

  setTimePicker(): void {
    this.fetchStartDateTime = formatTime(this.createVisitForm.value.startTime, Constants.dateFormat.h2ma, Constants.dateFormat.hhmm0);
    this.fetchEndDateTime = formatTime(this.createVisitForm.value.endTime, Constants.dateFormat.h2ma, Constants.dateFormat.hhmm0);
  }
  /**
   * Check if current time exceeded start date time value to disable/enable staff completed option
   */
  get isCurrentTimeAfterStartTime(): boolean {
    const today = formatDate(`${this.todayDateTime.date} ${this.todayDateTime.time}`, Constants.dateFormat.mmddyyhma);
    return moment(today).isSameOrAfter(
      `${this.createVisitForm.controls.startDate.value} ${this.createVisitForm.controls.startTime.value}`
    );
  }
  /**
   * actualDateTimeFilled is a function to check actual date, time in and time out filled or not.
   * returns boolean
   */
  get actualDateTimeFilled(): boolean {
    return (
      isPresent(this.createVisitForm.controls.actualDate.value) &&
      isPresent(this.createVisitForm.controls.actualTimeIn.value) &&
      isPresent(this.createVisitForm.controls.actualTimeOut.value)
    );
  }
  get actualDateTimeValidator(): boolean {
    if (
      moment(`${this.createVisitForm.value.actualDate} ${this.createVisitForm.value.actualTimeOut}`).isAfter(
        `${this.createVisitForm.value.actualDate} ${this.createVisitForm.value.actualTimeIn}`
      )
    ) {
      return true;
    }
    return false;
  }

  setStaffPartnerName(data: IStaffData): string {
    let displayName = data.displayname;
    displayName = data.user_job_type ? `${displayName} ${data.user_job_type}` : displayName;
    displayName = data.user_type === Constants.partner ? `${displayName}(${data.user_type})` : displayName;
    displayName = data.user_type === Constants.partner && data.organization !== '' ? `${displayName}(${data.organization})` : displayName;
    return displayName;
  }

  reviewCompleted(): void {
    this.visitDataController();
    const buttons = [
      { text: 'BUTTONS.CANCEL', confirm: false, class: 'warn-btn alert-cancel' },
      { text: 'BUTTONS.OK', confirm: true, class: 'warn-btn alert-ok' }
    ];

    this.commonService
      .showAlert({
        message: 'MESSAGES.COMPLETE_VISIT',
        header: 'MESSAGES.ARE_YOU_SURE',
        buttons,
        mode: 'ios',
        cssClass: 'visit-alert'
      })
      .then((confirmation) => {
        if (confirmation) {
          const reqParam = {
            action: VisitScheduleConstants.complete,
            editType: this.visitInfo.repeatType === Constants.none ? Constants.editType.series : Constants.editType.single,
            endAt: this.visitInfo.endAt,
            startAt: this.visitInfo.startAt
          };
          this.sharedService.isLoading = true;
          this.visitScheduleService.manageVisitData(this.visitKey, reqParam).subscribe((result) => {
            if (result && result.success) {
              this.commonService.showMessage(this.commonService.getTranslateData('SUCCESS_MESSAGES.VISIT_REVIEW_COMPLETE_SUCCESS'));
              this.sharedService.isLoading = false;
              this.navController.navigateBack(PageRoutes.scheduleCenterVisits);
            } else {
              this.commonService.showMessage(this.commonService.getTranslateData('ERROR_MESSAGES.VISIT_REVIEW_COMPLETE_FAILED'));
              this.sharedService.isLoading = false;
            }
          });
        }
        this.commonService.closeAllAlert();
      });
  }
  visitationTypeChange() {
    let address = this.createVisitForm.value.visitAddress;
    const isHomeVisitWithPatientAddress = this.createVisitForm.value.visitationType === VisitScheduleConstants.homeVisitType && this.patientAddress;

    if (isHomeVisitWithPatientAddress) {
      address = this.patientAddress;
    }

    this.createVisitForm.patchValue({
      visitAddress: address
    });
    if (this.createVisitForm.value.visitationType === VisitType.AIC) {
      this.createVisitForm.get('visitationTypeLocation').setValidators([Validators.required]);
      this.createVisitForm.get('visitChair').setValidators([Validators.required]);
    } else {
      this.createVisitForm.get('visitationTypeLocation').clearValidators();
      this.createVisitForm.get('visitChair').clearValidators();
      this.createVisitForm.controls.visitationTypeLocation.reset('');
      this.markControlAsTouchedAndDirty('visitationTypeLocation');
      this.createVisitForm.controls.visitChair.reset('');
      this.markControlAsTouchedAndDirty('visitChair');
      this.selectedVisitLocation = {};
      this.selectedVisitChair = {};
    }
    this.createVisitForm.get('visitationTypeLocation').updateValueAndValidity();
    this.createVisitForm.get('visitChair').updateValueAndValidity();
    this.updateClinicianFieldRequirement();
  }
  resetVisitType() {
    this.createVisitForm.controls.visitationType.reset();
    this.markControlAsTouchedAndDirty('visitationType');
    this.createVisitForm.controls.visitAddress.reset();
    this.markControlAsTouchedAndDirty('visitAddress');
    this.createVisitForm.get('visitationTypeLocation').clearValidators();
    this.createVisitForm.controls.visitationTypeLocation.reset();
    this.markControlAsTouchedAndDirty('visitationTypeLocation');
    this.createVisitForm.get('visitChair').clearValidators();
    this.createVisitForm.controls.visitChair.reset();
    this.markControlAsTouchedAndDirty('visitChair');
    this.createVisitForm.get('visitationTypeLocation').updateValueAndValidity();
    this.createVisitForm.get('visitChair').updateValueAndValidity();
    this.selectedVisitLocation = {};
    this.selectedVisitChair = {};
    this.updateClinicianFieldRequirement();
  }

  removeValidationPatientStatusChange() {
    if (this.pageLoaderPatientStatus) {
      this.pageLoaderPatientStatus = false;
    } else {
      if (
        this.createVisitForm.controls.patientVisitStatus.value !== Constants.patientVisitStatus[2].value &&
        this.createVisitForm.controls.patientVisitStatus.value !== Constants.patientVisitStatus[3].value
      ) {
        this.createVisitForm.get('patientReasonForCancel').clearValidators();
        this.createVisitForm.get('patientReasonForCancel').updateValueAndValidity();
        this.createVisitForm.controls['patientReasonForCancel'].reset('');
      } else {
        this.createVisitForm.controls['patientReasonForCancel'].reset('');
      }
    }
  }
  organizationChange() {
    if (!this.staffIds.length && this.createVisitForm.value.subcontracted && !this.createVisitForm.value.organization) {
      this.createVisitForm.patchValue({
        staffPartnerVisitStatus: Constants.staffVisitStatus[0].value
      });
    }
    this.checkUserStatus();
  }

  private updateOrganizationValidators(): void {
    const organizationControl = this.createVisitForm.get('organization');
    if (this.createVisitForm.value.subcontracted) {
      organizationControl.setValidators([Validators.required]);
    } else {
      organizationControl.clearValidators();
    }
    organizationControl.updateValueAndValidity();
  }

  private isSubcontractedWithOrganization(): boolean {
    return this.createVisitForm.value.subcontracted && this.createVisitForm.value.organization;
  }

  private markControlAsTouchedAndDirty(controlName: string): void {
    const control = this.createVisitForm.get(controlName);
    if (control) {
      control.markAsTouched();
      control.markAsDirty();
    }
  }

  private isControlInvalidAndTouched(control: any, checkFormSubmitted = false): boolean {
    return control?.invalid && (control?.touched || (checkFormSubmitted && this.formSubmitted));
  }

  private clearScrollTimeouts(): void {
    if (this.accordionExpandTimeoutId !== null) {
      clearTimeout(this.accordionExpandTimeoutId);
      this.accordionExpandTimeoutId = null;
    }
    if (this.scrollToErrorTimeoutId !== null) {
      clearTimeout(this.scrollToErrorTimeoutId);
      this.scrollToErrorTimeoutId = null;
    }
  }

  isActionButtonDisabled(type) {
    if (!this.shouldAllowVisitEditing()) {
      return true;
    }

    if (type === 'save-as-draft') {
        const patientNameValid = !!this.createVisitForm.get('patientName')?.value;
        const visitTitleValid = !!this.createVisitForm.get('visitTitle')?.value;
        if (
            patientNameValid &&
            visitTitleValid &&
            !this.fileSizeError &&
            !this.fileError &&
            !this.endOnDateTimeValidator &&
            !this.dateTimeValidator
        ) {
            return false;
        }
    }

    if (type === 'update') {
      if (this.canCompleteVisit() && !this.canEditVisit()) {
        const isCompleted = this.createVisitForm.controls.staffPartnerVisitStatus.value === this.staffStatus[4].value;
        const isCompletedWithInvalidDateTime = isCompleted && !this.actualDateTimeValidator;
        const isCompletedWithoutDriveTime = isCompleted && this.createVisitForm.controls.totalDriveTime.value === '' && this.enableDriveTimeField;
        const hasFileErrors = this.fileSizeError || this.fileError;
        const hasDateTimeErrors = this.endOnDateTimeValidator || this.dateTimeValidator;

        const shouldDisable = isCompletedWithInvalidDateTime || isCompletedWithoutDriveTime || hasFileErrors || hasDateTimeErrors;
        return shouldDisable;
      }
    }

    if (!this.createVisitForm.valid || this.fileSizeError || this.fileError || this.endOnDateTimeValidator || this.dateTimeValidator) {
      return true;
    } else {
      const isCompleted = this.createVisitForm.controls.staffPartnerVisitStatus.value === Constants.staffVisitStatus[4].value;
      const isCompletedWithInvalidDateTime = isCompleted && !this.actualDateTimeValidator;
      const isSubcontractedWithoutOrganization = !this.formControl.organization.value && this.formControl.subcontracted.value;
      const isClinicianRequiredButMissing =
        this.isRequiredField('visitAssignedTo') && !this.createVisitForm.value.visitAssignedTo && !this.formControl.subcontracted.value;
      const isCompletedWithoutDriveTime = isCompleted && this.createVisitForm.controls.totalDriveTime.value === '' && this.enableDriveTimeField;
      if (type === 'save') return isSubcontractedWithoutOrganization || isClinicianRequiredButMissing;
      if (type === 'update' || type === 'review-complete') {
        return isCompletedWithInvalidDateTime || isSubcontractedWithoutOrganization || isClinicianRequiredButMissing || isCompletedWithoutDriveTime;
      }
      return false;
    }
  }

  isRequiredField(field: string) {
    const form_field = this.createVisitForm.get(field);
    if (!form_field) {
        return false;
    }
    if (!form_field.validator) {
        return false;
    }

    const validator = form_field.validator({} as AbstractControl);
    return validator?.required;
}

  isEditPage() {
    return !isBlank(this.visitKey);
  }

  isEditSeriesMode() {
    return this.editType === Constants.editType.series && !isBlank(this.visitKey);
  }

  isSeriesCompleted() {
    if (isBlank(this.visitData)) return false;
    const endAt = convertToMomentInUTC(
      moment(this.visitData.eventEndAt, DateFormat.YYMMDD_FORMAT_HYPHEN).format(DateFormat.MMDDYY_FORMAT_SLASH),
      this.convertTo24Hour(this.visitData.endTime),
      this.visitData.locationTimeZone
    );
    const today = moment().utc();
    return endAt.isBefore(today);
  }

  isEditRecursiveRevised() {
    if (isBlank(this.visitData)) return false;
    const startAt = convertToMomentInUTC(
      moment(this.visitData.eventStartAt, DateFormat.YYMMDD_FORMAT_HYPHEN).format(DateFormat.MMDDYY_FORMAT_SLASH),
      this.convertTo24Hour(this.visitData.startTime),
      this.visitData.locationTimeZone
    );
    const today = moment().utc();
    return startAt.isBefore(today) && !this.isSeriesCompleted() && this.isEditSeriesMode() && this.isEditPage()
  }

  onEditLoad() {
    if (this.shouldAllowVisitEditing()) {
      this.createVisitForm.enable();
    }
    this.createVisitForm.enable();
    this.applyPermissionBasedFormState();
    const endDate = convertToMomentInUTC(
      this.visitData.endDate,
      this.convertTo24Hour(this.visitData.endTime),
      this.visitData.locationTimeZone
    );
    if (this.editType === Constants.editType.series) {
      this.setEndMinDate();
    }
    const today = moment().utc();
    this.isVisitInPastEvent = endDate.isBefore(today) && this.editType !== Constants.editType.series;
    this.isVisitInPastSeries = this.editType === Constants.editType.series && this.isSeriesCompleted();

    if (this.isVisitInPastEvent || this.isVisitInPastSeries) {
      if (this.isVisitInPastSeries) {
        this.createVisitForm.get('startTime').disable();
        this.createVisitForm.get('endTime').disable();
      }
      this.createVisitForm.get('startDate').disable();
      this.createVisitForm.get('endDate').disable();
      this.createVisitForm.get('timeZone').disable();
      this.createVisitForm.get('seriesStartDate').disable();
      this.createVisitForm.get('seriesEndDate').disable();
    } else {
      this.isVisitInProgressSeries = this.editType === Constants.editType.series && this.isEditRecursiveRevised();
      if (this.isVisitInProgressSeries) {
        this.createVisitForm.get('startDate').disable();
        this.createVisitForm.get('endDate').disable();
        this.createVisitForm.get('startTime').disable();
        this.createVisitForm.get('endTime').disable();
        this.createVisitForm.get('seriesStartDate').disable();
      }
    }

  }

  /** start date change function */
  seriesStartDateChange(date): void {
    this.createVisitForm.patchValue({
      seriesStartDate: formatDate(date, Constants.dateFormat.mdy),
    });
    this.endOnSeriesMinDate = moment(this.createVisitForm.value.seriesStartDate).add(1, 'd').format(Constants.dateFormat.ymd);
    if (this.createVisitForm.value.scheduleCadence === Constants.weekly) {
      this.changeWeeklySelection();
    }
  }

  /** end date change function */
  seriesEndDateChange(date): void {
    this.createVisitForm.patchValue({
      seriesEndDate: formatDate(date, Constants.dateFormat.mdy),
      endOn: formatDate(date, Constants.dateFormat.mdy),
    });
    if (this.isRevisedDatesActive) {
      const revisedDate = moment(this.createVisitForm.value.seriesEndDate).add(1, 'd');
      this.revisedStartDateMinValue = revisedDate.format(Constants.dateFormat.ymd);
      if (!revisedDate.isBefore(moment(this.createVisitForm.value.revisedStartDate))) {
        this.createVisitForm.patchValue({
          revisedStartDate: revisedDate.format(this.dateFormat.mdy),
        });
        if (this.createVisitForm.value.scheduleCadence === Constants.weekly) {
          this.changeWeeklySelection();
        }
      }
    }
  }

  /** start date change function */
  revisedStartDateChange(date): void {
    this.createVisitForm.patchValue({
      revisedStartDate: formatDate(date, Constants.dateFormat.mdy),
    });
    if (this.createVisitForm.value.scheduleCadence === Constants.weekly) {
      this.changeWeeklySelection();
    }
    const revisedDate = moment(this.createVisitForm.value.revisedStartDate).add(1, 'd');
    this.revisedEndDateMinValue = revisedDate.format(Constants.dateFormat.ymd);
    if (!revisedDate.isBefore(moment(this.createVisitForm.value.revisedEndDate))) {
      this.createVisitForm.patchValue({
        revisedEndDate: revisedDate.format(this.dateFormat.mdy),
      });
    }
  }

  /** end date change function */
  revisedEndDateChange(date): void {
    this.createVisitForm.patchValue({
      revisedEndDate: formatDate(date, Constants.dateFormat.mdy),
      endOn: formatDate(date, Constants.dateFormat.mdy)
    });
  }

  /** end time change function */
  revisedEndTimeChange(data) {
    if (data) {
      this.fetchRevisedEndDateTime = formatTime(this.createVisitForm.value.revisedEndTime, Constants.dateFormat.h2ma, Constants.dateFormat.hhmm0);
      const end = formatDate([this.createVisitForm.value.revisedEndDate, data].join(' '), this.dateFormat.h2ma);
      this.createVisitForm.patchValue({
        revisedEndTime: end
      });
    }
  }

  /** start time change function */
  revisedStartTimeChange(data) {
    if (data) {
      const time = [this.createVisitForm.value.revisedStartDate, data].join(' ');
      const startTime = formatDate(time, this.dateFormat.h2ma);
      const end = moment(time).add(30, 'minute');
      const endTime = formatDate(end, this.dateFormat.h2ma);
      this.createVisitForm.patchValue({
        revisedStartTime: startTime,
        revisedEndTime: endTime,
      });
      this.fetchRevisedStartDateTime = formatTime(this.createVisitForm.value.revisedStartTime, Constants.dateFormat.h2ma, Constants.dateFormat.hhmm0);
      this.fetchRevisedEndDateTime = formatTime(this.createVisitForm.value.revisedEndTime, Constants.dateFormat.h2ma, Constants.dateFormat.hhmm0);
    }
  }

  getRevisedDates(createVisitData) {
    const startTime = this.fetchRevisedStartDateTime
    if (!isBlank(startTime) && !isBlank(createVisitData.timeZone)) {
      const startDate = convertToMomentInUTC(
        moment().format(DateFormat.MMDDYY_FORMAT_SLASH),
        this.fetchRevisedStartDateTime,
        createVisitData.timeZone
      );
      const present = moment().utc();
      if (startDate.isBefore(present)) {
        return {
          old: moment(),
          new: moment().add(1, 'days')
        }
      } else {
        return {
          old: moment().subtract(1, 'days'),
          new: moment()
        }
      }
    }
  }

  changeWeeklySelection() {
    let date = this.createVisitForm.value.startDate;
    if (this.isRevisedDatesActive) {
      date = this.createVisitForm.value.revisedStartDate;
    } else if (!(this.isRevisedDatesActive && this.isVisitInPastEvent && this.isVisitInPastSeries) && this.isEditSeriesMode()) {
      date = this.createVisitForm.value.seriesStartDate;
    }
    this.weeklySchedule.map((data) => {
      if (data.value === formatDate(date, Constants.dateFormat.dd).toUpperCase()) {
        data.selected = true;
        this.createVisitForm.controls['weekly'].patchValue([...new Set([...this.createVisitForm.value.weekly, data.value])]);
        data.disabled = true;
      } else {
        data.disabled = false;
      }
      return data;
    });
  }

  isRecursiveEnabled() {
    if (this.visitData && this.editType === Constants.editType.series && (this.isEditRecursiveRevised() || this.isSeriesCompleted())) {
      const createVisitFormData = this.createVisitForm.getRawValue();
      const currentStartAt = convertToMomentInUTC(
        createVisitFormData.startDate,
        formatTime(createVisitFormData.startTime, Constants.dateFormat.h2ma, Constants.dateFormat.hhmm0),
        createVisitFormData.timeZone
      );
      const currentEndAt = convertToMomentInUTC(
        createVisitFormData.endDate,
        formatTime(createVisitFormData.endTime, Constants.dateFormat.h2ma, Constants.dateFormat.hhmm0),
        createVisitFormData.timeZone
      );
      const savedStartAt = convertToMomentInUTC(
        this.visitData.startDate,
        formatTime(this.visitData.startTime, Constants.dateFormat.h2ma, Constants.dateFormat.hhmm0),
        this.visitData.locationTimeZone
      );
      const savedEndAt = convertToMomentInUTC(
        this.visitData.endDate,
        formatTime(this.visitData.endTime, Constants.dateFormat.h2ma, Constants.dateFormat.hhmm0),
        this.visitData.locationTimeZone
      );
      const isAssignedNotChanged =
        this.areStaffIdsEqual() &&
        (isBlank(createVisitFormData.organization) ? '' : createVisitFormData.organization) ===
        (isBlank(this.visitData.organizationName) ? '' : this.visitData.organizationName);

      this.isRevisedDatesActive = !(currentStartAt.isSame(savedStartAt) &&
        currentEndAt.isSame(savedEndAt) &&
        this.checkRecursiveChanged(createVisitFormData) &&
        isAssignedNotChanged && createVisitFormData.timeZone === this.visitData.locationTimeZone && !createVisitFormData.showRevisedTimeUpdate
      );
      return this.isRevisedDatesActive;
    }
    return false;
  }

  areStaffIdsEqual() {
    // Handle null/undefined/empty cases
    if (!this.staffIds?.length && !this.visitData?.staffId) return true;
    if (!this.staffIds?.length || !this.visitData?.staffId) return false;

    // Convert both to arrays, sort them, and join back to strings for comparison
    const currentStaffIds = [...this.staffIds].sort().join(',');
    const visitDataStaffIds = this.visitData.staffId
      .split(',')
      .map((id) => id.trim())
      .sort()
      .join(',');

    return currentStaffIds === visitDataStaffIds;
  };

  checkRecursiveChanged(createVisitFormData) {
    return (
      createVisitFormData.scheduleCadence === this.visitData.repeatType &&
      ((createVisitFormData.scheduleCadence === 'daily' && createVisitFormData.repeatEvery === this.visitData.repeatInterval) ||
        (createVisitFormData.scheduleCadence === 'weekly' && createVisitFormData.weekly && this.removeCommaFromString(createVisitFormData.weekly.toString()) === this.removeCommaFromString(this.visitData.repeatDays)) ||
        (createVisitFormData.scheduleCadence === 'monthly' && createVisitFormData.monthly === this.visitData.repeatMonthType))
    );
  }

  convertTo24Hour(time) {
    const parts = time.split(' ');
    const timePart = parts[0];
    const period = parts[1];

    const timeParts = timePart.split(':');
    let hours = parseInt(timeParts[0]);
    const minutes = timeParts[1];

    if (period === 'PM' && hours < 12) {
      hours += 12;
    }
    if (period === 'AM' && hours === 12) {
      hours = 0;
    }

    return `${hours.toString().length < 2 ? '0' + hours : hours}:${minutes}:00`;
  }

  multipleDateChange(dates): void {
    if (dates) {
      this.createVisitForm.patchValue({
        multipleDatesFormat: dates,
        multipleDates: dates
          .sort((a, b) => moment(a, Constants.dateFormat.ymd).diff(moment(b, Constants.dateFormat.ymd)))
          .map((date) => formatDate(date, Constants.dateFormat.mdy))
          .join(', ')
      });
    }
  }

  toggleMultipleDates(): void {
    this.createVisitForm.patchValue({
      multipleDates: [formatDate(`${this.selectedDateTime.date} ${this.selectedDateTime.time}`, this.dateFormat.mdy)].join(', '),
      multipleDatesFormat: [formatDate(`${this.selectedDateTime.date} ${this.selectedDateTime.time}`, this.dateFormat.ymd)]
    });
    this.createVisitForm.patchValue({
      recurrence: false
    });
  }

  visitAssignToStaff(staffData: UserList[]): void {
    let assignedToUserId = [];
    if (this.visitData && typeof this.visitData.assignedToUserId === 'string') {
      assignedToUserId = JSON.parse(this.visitData.assignedToUserId);
    }

    if (staffData && staffData.length > 0 && this.visitData && Array.isArray(assignedToUserId)) {
      staffData.forEach((item) => {
        if (!item.displayname && item.userId) {
          const found = assignedToUserId.find((u) => +u.userid === +item.userId && (u.userDisplayName || u.displayname));
          if (found) {
            // eslint-disable-next-line no-param-reassign
            item.displayname = found.userDisplayName ?? found.displayname;
          }
        }
      });
    }
    // Ensure all staffData userIds are present in assignedToUserId
    staffData.forEach((item) => {
      const userId = item.userid || item.userId;
      if (userId && !assignedToUserId.some((u) => +u.userid === +userId)) {
        assignedToUserId.push(item);
      }
    });
    this.visitData.assignedToUserId = JSON.stringify(assignedToUserId);

    if (staffData && staffData.length > 0) {
      this.staffIds = staffData.map((x) => x.userid || x.userId);
      if (!this.staffIds?.length) {
        this.createVisitForm.controls['visitAssignedTo'].setValue('');
        if (!this.createVisitForm.value.subcontracted && !this.createVisitForm.value.organization) {
          this.createVisitForm.patchValue({
            staffPartnerVisitStatus: Constants.staffVisitStatus[0].value
          });
        }
      } else {
        const staffNames = staffData.map((staff) => this.setStaffPartnerName(staff));
        this.createVisitForm.controls['visitAssignedTo'].setValue(staffNames.join(', '));
        if (
          (!this.createVisitForm.value.subcontracted && !this.createVisitForm.value.organization) ||
          this.createVisitForm.value.staffPartnerVisitStatus === Constants.staffVisitStatus[0].value
        ) {
          // Get the staff visit status if current page is in edit mode
          let visitStatus = Constants.staffVisitStatus[1].value;
          if (!isBlank(this.visitKey) && this.isSingleSelect && +staffData[0].userId === +this.staffIds[0]) {
            visitStatus = this.staffVisitStatusId;
          }
          this.createVisitForm.patchValue({
            staffPartnerVisitStatus: visitStatus
          });
        }
        // FIXME: this is a temporary fix to show the organization name in the staff partner visit status
        if (staffData[0].organization) {
          this.setPartnerOrganization(staffData[0]);
        } else {
          this.partnerOrgName = '';
        }
      }
    }
    this.checkUserStatus();
  }

  get canDismissDeleteSeries() {
    return this.deleteSeriesModel === 'today' || (this.deleteSeriesModel === 'from' && !isBlank(this.deleteSeriesFrom));
  }

  onDeleteSelectionChanged(value: string): void {
    this.deleteSeriesModel = value;
    if (this.deleteSeriesModel === 'today') this.deleteSeriesFrom = '';
  }

  setDeleteFromDate(event: DateSelectKeyValue): void {
    if (!isBlank(event.value)) {
      this.deleteSeriesModel = 'from';
      this.deleteSeriesFrom = formatDate(event.value, Constants.dateFormat.ymd);
    }
  }

  deleteVisitSeries(): void {
    const createVisitData = this.createVisitForm.getRawValue();
    const currentDate = this.deleteSeriesModel === 'from' ? this.deleteSeriesFrom : moment().format(DateFormat.YYMMDD_FORMAT_HYPHEN);
    const convertedDateTime = convertFromTzToTz(currentDate, moment().format(DateFormat.TIME_24_HR), moment.tz.guess(), createVisitData.timeZone);
    const currentDateTime = new Date(convertedDateTime);
    const seriesStartDateTime = new Date(`
      ${createVisitData.seriesStartDate} ${formatTime(createVisitData.startTime, Constants.dateFormat.h2ma, Constants.dateFormat.hhmm0)}
    `);
    this.isDelete = true;
    if (seriesStartDateTime > currentDateTime) {
      this.deleteVisit();
    } else if (
      this.deleteSeriesModel === 'today' ||
      (this.deleteSeriesModel === 'from' && this.deleteSeriesFrom === moment().format(DateFormat.YYMMDD_FORMAT_HYPHEN))
    ) {
      const param = {
        startDate: `${currentDate}T00:00:00Z`,
        endDate: `${currentDate}T23:59:59Z`,
        calendarType: 'allVisit',
        siteId: createVisitData.siteName,
        type: createVisitData.visitationType,
        title: createVisitData.visitTitle
      };
      this.visitScheduleService.getVisits(param).subscribe((response) => {
        if (response.success) {
          const visit = response.data.find((item) => item.visitKey === this.visitKey);
          if (!isBlank(visit)) {
            const eventStartDateTime = new Date(visit.startDate);
            if (eventStartDateTime > currentDateTime) {
              createVisitData.until = moment().subtract(1, 'days').format(DateFormat.YYMMDD_FORMAT_HYPHEN);
            } else {
              createVisitData.until = currentDate;
            }
            this.createVisitForm.patchValue({
              seriesEndDate: createVisitData.until
            });
            this.manageVisit();
          } else {
            this.createVisitForm.patchValue({
              until: moment(currentDate).subtract(1, 'days').format(DateFormat.YYMMDD_FORMAT_HYPHEN)
            });
            this.manageVisit();
          }
        }
      });
    } else {
      this.createVisitForm.patchValue({
        seriesEndDate: moment(currentDate).subtract(1, 'days').format(DateFormat.YYMMDD_FORMAT_HYPHEN)
      });
      this.manageVisit();
    }
  }

  setCountryIsoCode(): void {
    this.countryIsoCode = environment.defaultCountryFlag;
  }

  onRevisedChange(): void {
    const tempRevisedDatesActive = this.isRevisedDatesActive;
    this.isRecursiveEnabled();
    if (tempRevisedDatesActive === this.isRevisedDatesActive) {
      return;
    }
    if (this.isRevisedDatesActive) {
      const newDates = this.getRevisedDates(this.createVisitForm.getRawValue());
      if (newDates) {
        this.revisedStartDateMinValue = newDates.new.format(Constants.dateFormat.ymd);
        this.createVisitForm.patchValue({
          revisedStartDate: newDates.new.format(this.dateFormat.mdy),
          seriesEndDate: newDates.old.format(this.dateFormat.mdy),
        });
        this.endOnSeriesMinDate = newDates.old.format(Constants.dateFormat.ymd);
        this.revisedEndDateMinValue = newDates.new.format(Constants.dateFormat.ymd);
        if (this.visitData.repeatType === Constants.weekly) {
          this.changeWeeklySelection();
        }
      }
    } else {
      this.createVisitForm.patchValue({
        seriesEndDate: formatDate(convertDateTimeToCustomFormat(this.visitData.untilDate, this.visitData.endTime), this.dateFormat.mdy),
        endOn: formatDate(convertDateTimeToCustomFormat(this.visitData.untilDate, this.visitData.endTime), this.dateFormat.mdy)
      });
      this.setEndMinDate();
    }
  }

  setEndMinDate(): void {
    const startDate = moment(this.visitData.eventStartAt, Constants.dateFormat.ymd);
    const today = moment();
    if (startDate.isAfter(today)) {
      this.endOnSeriesMinDate = this.endOnMinDate = startDate.add(1, 'd').format(Constants.dateFormat.ymd);
    } else {
      this.endOnSeriesMinDate = this.endOnMinDate = today.format(Constants.dateFormat.ymd);
    }
  }

  removeCommaFromString(input): string {
    if (isBlank(input)) return '';
    return input.replace(/,*$/, '');
  }

  ngOnDestroy(): void {
    if (this.formValueChangeHandle) this.formValueChangeHandle.unsubscribe();
    this.clearScrollTimeouts();
    this.subscriptions.unsubscribe();
  }

  startVisit(): void {
    if (!this.canChangeVisitStatus()) {
      this.showPermissionDeniedMessage();
      return;
    }

    const actualTimeIn = moment().format(Constants.dateFormat.yyyMMDDT);
    let params = {
      editType: this.checkRecurrenceVisit ? Constants.editType.single : Constants.editType.series,
      actualTimeIn: `${actualTimeIn}Z`,
      timeInLatLong: '',
      timeInAddress: ''
    };
    if (this.checkRecurrenceVisit) {
      const createVisitFormData = this.createVisitForm.getRawValue();
      const startEndAt = {
        startAt: convertDateTimeToIsoFormat(
          formatDate(createVisitFormData.startDate, Constants.dateFormat.ymd),
          formatDate(
            [createVisitFormData.startDate, createVisitFormData.startTime].join(' '),
            this.dateFormat.hhmm0
          )
        ),
        endAt: convertDateTimeToIsoFormat(
          formatDate(createVisitFormData.endDate, Constants.dateFormat.ymd),
          formatDate(
            [createVisitFormData.endDate, createVisitFormData.endTime].join(' '),
            this.dateFormat.hhmm0
          )
        )
      };
      params = { ...params, ...startEndAt };
    }
    this.setLatLong(params);
  }

  stopVisit(): void {
    if (!this.canChangeVisitStatus()) {
      this.showPermissionDeniedMessage();
      return;
    }

    const actualTimeOut = moment().format(Constants.dateFormat.yyyMMDDT);
    const actualTimeIn = moment(`${this.createVisitForm.value.actualDate} ${this.createVisitForm.value.actualTimeIn}`);
    const isSameOrBeforeStartTime = moment(actualTimeOut).isSameOrBefore(actualTimeIn, 'minutes');
    if (isSameOrBeforeStartTime) {
      const message = this.commonService.getTranslateData('VALIDATION_MESSAGES.ACTUAL_END_TIME_GREATER');
      this.commonService.showMessage(message);
      return;
    }

    if (this.enableTotalMileageField || this.enableDriveTimeField) {
      this.createVisitForm.patchValue({
        actualTimeOut: formatDate(
          convertDateTimeToCustomFormat(formatDate(actualTimeOut, Constants.dateFormat.ymd), formatDate(actualTimeOut, Constants.dateFormat.hhmm0)),
          this.dateFormat.h2ma
        ),
        staffPartnerVisitStatus: Constants.staffVisitStatus[4].value,
        patientVisitStatus: Constants.patientVisitStatus[4].value
      });

      const hours = this.createVisitForm.get('driveTimeHours').value ?? '00';
      const minutes = this.createVisitForm.get('driveTimeMinutes').value ?? '00';
      const validationMessage = this.getDriveTimeValidationMessage(hours, minutes);
      if (validationMessage) {
        this.accordionGroupMain.value = 'schedule-status-accordion';
        this.showValidationMessage(validationMessage);
        return;
      }
      if (this.isActionButtonDisabled('update')) {
        // Update only happening when mileage or drive time config is enabled, if any fields became mandatory, then update API will throw error
        this.showValidationMessage('VALIDATION_MESSAGES.REQUIRED_FIELDS');
        return;
      }
    }

    const params = {
      editType: Constants.editType.series,
      actualTimeOut: `${actualTimeOut}Z`,
      timeOutLatLong: '',
      timeOutAddress: ''
    };
    this.setLatLong(params, true);
  }
  private getDriveTimeValidationMessage(hours: string, minutes: string): string {
    if (this.isMileageAndDriveTimeMissing(hours, minutes)) {
      return 'VALIDATION_MESSAGES.REQUIRED_DRIVE_TIME_AND_TOTAL_MILEAGE';
    }
    if (this.isMileageMissing()) {
      return 'VALIDATION_MESSAGES.REQUIRED_TOTAL_MILEAGE';
    }
    if (this.isDriveTimeMissing(hours, minutes)) {
      return 'VALIDATION_MESSAGES.REQUIRED_DRIVE_TIME';
    }
    return '';
  }
  setLatLong(params: any, isStopVisit = false): void {
    this.sharedService.isLoading = true;
    this.sharedService
      .getLatLong()
      .then(() => {
        if (isStopVisit) {
          params.timeOutLatLong = `${this.sharedService.vcLatitude},${this.sharedService.vcLongitude}`;
        } else {
          params.timeInLatLong = `${this.sharedService.vcLatitude},${this.sharedService.vcLongitude}`;
        }
        this.getAddressFromLatLong(this.sharedService.vcLatitude, this.sharedService.vcLongitude, (address) => {
          if (address) {
            if (isStopVisit) {
              params.timeOutAddress = address;
            } else {
              params.timeInAddress = address;
            }
          }
          this.setStartStopVisitFunctionality(params, isStopVisit);
        });
      })
      .catch(() => {
        if (isBlank(localStorage.getItem(Constants.storageKeys.deviceLocationMessage))) {
          const message = this.commonService.getTranslateData('MESSAGES.DISABLED_LOCATION_WARNING');
          this.commonService.showMessage(message);
          localStorage.setItem(Constants.storageKeys.deviceLocationMessage, 'true');
        }
        this.setStartStopVisitFunctionality(params, isStopVisit);
      });
  }

  getAddressFromLatLong(lat, long, callBack: any): void {
    const getMapKey = environment.mapAPIKey;
    if (lat && long) {
      this.http
        .get(
          `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${long}&location_type=ROOFTOP&result_type=street_address&&key=${getMapKey}`
        )
        .subscribe((response: any) => {
          if (response.status === 'OK' && response.results.length) {
            callBack(response.results[0].formatted_address);
          } else if (response.status === 'ZERO_RESULTS' && isPresent(response.plus_code)) {
            callBack(response.plus_code.compound_code);
          } else {
            callBack(false);
          }
        }, (error: any) => {
          callBack(false);
        }
        );
    } else {
      callBack(false);
    }
  }

  setStartStopVisitFunctionality(params: any, isStopVisit?: boolean): void {
    this.sharedService.isLoading = false;
    this.visitScheduleService.startStopVisit(this.visitKey, params).subscribe((response: any) => {
      if (response.success) {
        this.visitKey = response.data.visitKey;
          if (isStopVisit && (this.enableDriveTimeField || this.enableTotalMileageField)) {
            this.visitAvailablityCheckandSave();
          } else {
            this.getVisitDetails();
          }
      } else if (isPresent(response.data.error)) {
        this.commonService.showMessage(response.data.error.message);
      }
    }, (error: any) => {
        this.commonService.showMessage(this.commonService.getTranslateData('ERROR_MESSAGES.VISIT_UPDATE_STATUS_FAILED'));
    });
  }

  async showGoogleMapWithMarker(latlong: string): Promise<void> {
    if (latlong) {
      const getLatLong = latlong.split(',');
      const modal = await this.modalController.create({
        component: MapViewComponent,
        componentProps: { mapLocation: [{ lat: Number(getLatLong[0]), lng: Number(getLatLong[1]) }] }
      });
      await modal.present();
    }
  }

  async selectTherapyType(event?: Event): Promise<void> {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    let siteIds = !isBlank(this.createVisitForm.value.siteName) ? this.createVisitForm.value.siteName : [];
    const modal = await this.modalController.create({
      component: AsyncAdvanceSelectComponent,
      componentProps: {
        displayKey: 'therapyTypeName',
        searchParams: {
          ...VisitScheduleConstants.commonSearchParam,
          searchParams: {therapyTypeName: "%%", siteIds },
        },
        selectedItems: this.selectedTherapyType?.id ? [{ id: this.selectedTherapyType.id }] : [],
        loadDataFunction: this.visitScheduleService.searchTherapyTypes,
        headerTitle: 'TITLES.SELECT_THERAPY_TYPE',
        status: -1
      }
    });
    modal.onDidDismiss().then((modalData) => {
      if (!isBlank(modalData.data)) {
        this.createVisitForm.controls['therapyType'].setValue(modalData.data.selectedIds?.[0] ?? null);
        this.markControlAsTouchedAndDirty('therapyType');
        this.selectedTherapyType = modalData.data.selectedItems?.[0] ?? null;
        if (this.createVisitForm.controls['dosageAppointment'].value) {
          this.createVisitForm.controls['dosageAppointment'].setValue('');
          this.markControlAsTouchedAndDirty('dosageAppointment');
        }
        this.selectedDosageAppointment = {};
        this.setVisitDurationFromTherapy();
      }
    });
    await modal.present();
  }

  async selectDosageAppointment(event?: Event): Promise<void> {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    const modal = await this.modalController.create({
      component: AsyncAdvanceSelectComponent,
      componentProps: {
        displayKey: 'therapyName',
        searchParams: {
          ...VisitScheduleConstants.commonSearchParam,
          searchParams: {therapyName: "%%"},
          therapyTypeId: this.selectedTherapyType?.id,
          status: -1
        },
        selectedItems: this.selectedDosageAppointment?.id ? [{ id: this.selectedDosageAppointment.id }] : [],
        loadDataFunction: this.visitScheduleService.searchTherapies,
        headerTitle: 'TITLES.SELECT_THERAPY',
      }
    });
    modal.onDidDismiss().then((modalData) => {
      if (!isBlank(modalData.data)) {
        this.createVisitForm.controls['dosageAppointment'].setValue(modalData.data.selectedIds?.[0] ?? null);
        this.markControlAsTouchedAndDirty('dosageAppointment');
        this.selectedDosageAppointment = modalData.data.selectedItems?.[0] ?? null;
        this.setVisitDurationFromTherapy();
      }
    });
    await modal.present();
  }

    /**
   * Updates clinician field validation based on visit type and chair selection
   * If visit type is AIC and a chair is selected, clinician becomes optional
   * If visit type is AIC and no chair is selected, clinician is required
   * For all other visit types, clinician field follows existing validation rules
   */
    updateClinicianFieldRequirement() {
      const visitTypeValue = this.createVisitForm.get('visitationType').value;
      const visitChairValue = this.createVisitForm.get('visitChair').value;
      const isSubcontracted = this.createVisitForm.get('subcontracted').value;
    
      // If subcontracted is true, clinician is never required
      if (isSubcontracted) {
        this.createVisitForm.get('visitAssignedTo').clearValidators();
        this.createVisitForm.get('visitAssignedTo').updateValueAndValidity();
        this.checkUserStatus();
        return;
      }
  
      // Check if visit type is AIC
      if (visitTypeValue === VisitType.AIC.toString()) {
        if (visitChairValue) {
          // If chair is selected, clinician is optional
          this.createVisitForm.get('visitAssignedTo').clearValidators();
        } else {
          // If no chair is selected, clinician is required
          this.createVisitForm.get('visitAssignedTo').setValidators([Validators.required]);
        }
        this.createVisitForm.get('visitAssignedTo').updateValueAndValidity();
      } else {
        this.createVisitForm.get('visitAssignedTo').setValidators([Validators.required]);
        this.createVisitForm.get('visitAssignedTo').updateValueAndValidity();
      }
      this.checkUserStatus();
    }

    /** Initialize drive time hours and minutes arrays */
  initializeDriveTimeArrays(): void {
    this.driveTimeHours = Array.from({ length: 51 }, (_, i) => i.toString().padStart(2, '0'));
    this.driveTimeMinutes = Array.from({ length: 60 }, (_, i) => i.toString().padStart(2, '0'));
  }

  /**
   * Updates the total drive time based on hours and minutes selections
   */
  updateDriveTime(): void {
    // Check permissions before allowing drive time update
    if (!this.canEditVisitFields()) {
      this.showPermissionDeniedMessage();
      return;
    }

    const hours = this.createVisitForm.get('driveTimeHours').value ?? '00';
    const minutes = this.createVisitForm.get('driveTimeMinutes').value ?? '00';
    // Combine hours and minutes into the required format (HH:MM)
    const formattedDriveTime = `${hours}:${minutes}`;
    if (formattedDriveTime === '00:00') {
      this.createVisitForm.get('totalDriveTime').setValue('');
    } else {
      this.createVisitForm.get('totalDriveTime').setValue(formattedDriveTime);
    }
  }

  formatDriveTime(value: string): string {
    if (!value) return '';
    if (value === '00:00' || value === '00:00:00') return '';
    const parts = value.split(':');
    if (parts.length >= 2) {
      const hours = parts[0].padStart(2, '0');
      const minutes = parts[1].padStart(2, '0');
      return `${hours}:${minutes}`;
    }
    return '';
  }

  setVisitDurationFromTherapy(justDurationMinUpdate?: boolean): void {
    // Set duration based on selected therapy appointment or default to 30 minutes
    if (this.selectedDosageAppointment && this.selectedDosageAppointment.duration) {
      this.durationMin = this.selectedDosageAppointment.duration;
    } else {
      this.durationMin = 30;
    }
  
    // Update end time based on the current start time and therapy duration
    if (!justDurationMinUpdate && this.createVisitForm.value.startTime && this.fetchStartDateTime) {
      const startDateTime = moment(`${this.createVisitForm.value.startDate} ${this.createVisitForm.value.startTime}`, 
        `${Constants.dateFormat.mdy} ${Constants.dateFormat.hhmma}`);
      
      // Calculate end time by adding the therapy duration to the start time
      const endDateTime = moment(startDateTime).add(this.durationMin, 'minutes');
      const endTime = endDateTime.format(Constants.dateFormat.hhmma);
      
      // Update form with the new end time
      this.createVisitForm.patchValue({
        endTime: endTime
      });
      
      // Update fetchEndDateTime for the time picker
      this.fetchEndDateTime = formatTime(endTime, Constants.dateFormat.h2ma, Constants.dateFormat.hhmm0);
    }
  }
  resetVisitAssignedToData() {
    this.staffIds = [];
    this.visitData.assignedToUserId = '[]';
    this.createVisitForm.controls['visitAssignedTo'].setValue('');
    if (!this.createVisitForm.value.subcontracted && !this.createVisitForm.value.organization) {
      this.createVisitForm.patchValue({
        staffPartnerVisitStatus: Constants.staffVisitStatus[0].value
      });
    }
    this.checkUserStatus();
  }
  get isSingleSelect() {
    return Number(this.sharedService.getConfigValue('visit_max_staff_assigned_limit')) <= 1;
  }

  isMileageAndDriveTimeMissing(hours: string, minutes: string): boolean {
    return (
      this.enableTotalMileageField && !this.createVisitForm.get('totalMileage').value && this.enableDriveTimeField && `${hours}${minutes}` === '0000'
    );
  }

  isMileageMissing(): boolean {
    return this.enableTotalMileageField && !this.createVisitForm.get('totalMileage').value;
  }

  isDriveTimeMissing(hours: string, minutes: string): boolean {
    return this.enableDriveTimeField && `${hours}${minutes}` === '0000';
  }

  showValidationMessage(key: string): void {
    const message = this.commonService.getTranslateData(key);
    this.commonService.showMessage(message);
  }

  handleAction(action: string) {
    this.formSubmitted = true;
    if (this.isActionButtonDisabled(action)) {
      this.markAllFieldsAsTouched();
      this.expandFirstErrorSectionAndScroll();
    } else if (action === 'save' || action === 'update') {
      this.visitAvailablityCheckandSave();
    } else if (action === 'save-as-draft') {
      this.saveAsDraftConfirm();
    } else if (action === 'review-complete') {
      this.reviewCompleted();
    }
  }

  markAllFieldsAsTouched(): void {
    Object.keys(this.createVisitForm.controls).forEach((key) => {
      const control = this.createVisitForm.get(key);
      if (control) {
        control.markAsTouched();
      }
    });
  }
}
