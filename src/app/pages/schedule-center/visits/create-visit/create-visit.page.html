<app-header [headerTitle]="visitKey ? 'TITLES.MODIFY_VISIT' : 'TITLES.CREATE_VISIT'"></app-header>
<ion-content class="create-visit common-start-padding">
  <div class="modify-content">
    <ion-row class="row modify-row">
      <ion-col>
        <ion-button color="de-york" class="ion-text-capitalize" (click)="getViewHistory()" id="view-history" *ngIf="viewHistory">
          {{ 'BUTTONS.VIEW_HISTORY' | translate }}
        </ion-button>
        <ion-button
          color="de-york"
          class="ion-text-capitalize"
          (click)="startVisit()"
          id="start-visit"
          *ngIf="
            !viewHistory && visitKey && editType === constants.editType.single && !visitData?.actualTimeIn && showStartButton && !checkDraftVisit && canChangeVisitStatus()
          "
        >
          {{ 'BUTTONS.START_VISIT' | translate }}
        </ion-button>
        <ion-button
          color="danger"
          class="ion-text-capitalize"
          (click)="stopVisit()"
          id="stop-visit"
          *ngIf="!viewHistory && visitKey && editType === constants.editType.single && visitData?.actualTimeIn && !visitData?.actualTimeOut && canChangeVisitStatus()"
        >
          {{ 'BUTTONS.STOP_VISIT' | translate }}
        </ion-button>
        <ion-button
          expand="block"
          color="de-york"
          class="btn-sm ion-text-capitalize"
          (click)="handleAction('review-complete')"
          id="review-complete"
          *ngIf="(isCompleted || isReviewCompleted) && enableReviewComplete"
        >
          {{ 'BUTTONS.REVIEW_COMPLETE' | translate }}
        </ion-button>
      </ion-col>
      <ion-col class="ion-text-end">
        <ion-button fill="clear" (click)="deleteVisitConfirmation()" id="trash" *ngIf="visitKey" [disabled]="isReviewCompleted">
          <ion-icon name="trash-sharp" class="trash-icon" [ngClass]="{ 'trash-disabled': isReviewCompleted }"></ion-icon>
        </ion-button>
        <ion-modal
          #deleteSeriesModal
          trigger="delete-series"
          class="delete-series-modal"
          [presentingElement]="presentingElement"
          [canDismiss]="canDismissDeleteSeries"
        >
          <ng-template>
            <ion-header>
              <ion-toolbar class="delete-modal">
                <ion-buttons slot="start">
                  <ion-button class="cancel" (click)="deleteSeriesModal.dismiss()">{{ 'BUTTONS.CANCEL' | translate
                    }}</ion-button>
                </ion-buttons>
                <ion-title>{{ 'TITLES.DELETE_SERIES' | translate }}</ion-title>
                <ion-buttons slot="end">
                  <ion-button class="ok" (click)="deleteSeriesModal.dismiss(); deleteVisitSeries()">{{
                    'BUTTONS.CONTINUE' | translate }}</ion-button>
                </ion-buttons>
              </ion-toolbar>
            </ion-header>
            <ion-content>
              <ion-radio-group [(ngModel)]="deleteSeriesModel">
                <ion-list lines="none">
                  <ion-item (click)="onDeleteSelectionChanged('today')">
                    <ion-label class="ion-text-wrap" for="deleteToday">{{ 'LABELS.DELETE_ALL_FUTURE_EVENTS' | translate }}</ion-label>
                    <ion-radio color="primary" slot="start" id="deleteToday" value="today"></ion-radio>
                  </ion-item>
                  <ion-item id="open-date-input" (click)="onDeleteSelectionChanged('from')">
                    <ion-label class="ion-text-wrap" for="deleteFrom">{{ 'LABELS.DELETE_ALL_FUTURE_EVENTS_FROM' | translate }}</ion-label>
                    <ion-input
                      id="deleteFromDate"
                      type="text"
                      readonly="true"
                      [(ngModel)]="deleteSeriesFrom"
                      placeholder="{{ constants.dateFormat.ymd }}"
                    ></ion-input>
                    <app-custom-datepicker
                      triggerID="open-date-input"
                      pickerType="date"
                      [minDate]="formatDate(todayDateTime.date, constants.dateFormat.ymd)"
                      [maxDate]="formatDate(createVisitForm.get('revisedEndDate').value, constants.dateFormat.ymd)"
                      (selectDate)="setDeleteFromDate($event)"
                    ></app-custom-datepicker>
                    <ion-radio color="primary" slot="start" id="deleteFrom" value="from"></ion-radio>
                  </ion-item>
                </ion-list>
              </ion-radio-group>
            </ion-content>
          </ng-template>
        </ion-modal>
      </ion-col>
    </ion-row>
  </div>
  <div class="accordion-content">
    <form [formGroup]="createVisitForm">
      <ion-accordion-group class="show-behind" id="accordion-group" #accordionGroupMain>
        <ion-accordion id="patient-information-accordion" value="patient-information-accordion">
          <ion-item slot="header" class="accordion-head" [ngClass]="{ 'validation-error-header': hasPatientInformationErrors() }">
            {{ 'LABELS.PATIENT_INFORMATION' | translate }}
          </ion-item>
          <ion-list slot="content" class="content">
            <ion-row class="row">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.SITE' | translate }}*</ion-label>
              </ion-col>
              <ion-col size="7">
                <ion-select
                  placeholder="{{ 'PLACEHOLDERS.SELECT_SITE' | translate }}"
                  formControlName="siteNames"
                  id="site-name"
                  [multiple]="true"
                  class="wrap-text common-input"
                  mode="md"
                  name="siteNames"
                  [ngClass]="{
                    'validation-error-border': formControl.siteNames?.invalid && (formControl.siteNames?.touched || formControl.siteNames?.dirty)
                  }"
                  (ionChange)="siteChange()"
                  [interfaceOptions]="{ header: 'LABELS.SITE' | translate }"
                >
                  <ion-select-option *ngFor="let sites of mySites; let i = index" [value]="sites.id" [id]="sites.id">
                    {{ sites.name }}
                  </ion-select-option>
                </ion-select>
                <p
                  class="validation-error-text"
                  *ngIf="formControl.siteNames?.invalid && (formControl.siteNames?.touched || formControl.siteNames?.dirty)"
                >
                  {{ 'VALIDATION_MESSAGES.SITE_REQUIRED' | translate }}
                </p>
              </ion-col>
            </ion-row>
            <ion-row class="row">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.PATIENT_NAME' | translate }}*</ion-label>
              </ion-col>
              <ion-col size="7">
                <ion-input
                  formControlName="patientName"
                  type="text"
                  class="wrap-text common-input disable-cursor"
                  [ngClass]="{
                    'validation-error-border':
                      formControl.patientName?.invalid && (formControl.patientName?.touched || formControl.patientName?.dirty),
                    disable: formControl.siteNames?.invalid
                  }"
                  (click)="selectPatient($event)"
                  id="patient-name"
                  [disabled]="formControl.siteNames.invalid || isReviewCompleted"
                  autocapitalize="on"
                >
                </ion-input>
                <p
                  class="validation-error-text"
                  *ngIf="formControl.patientName?.invalid && (formControl.patientName?.touched || formControl.patientName?.dirty)"
                >
                  {{ 'VALIDATION_MESSAGES.REQUIRED_PATIENT' | translate }}
                </p>
              </ion-col>
            </ion-row>
            <ion-row class="row">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.PATIENT_PHONE_NUMBER' | translate }}</ion-label>
              </ion-col>
              <ion-col class="flag-col">
                <div class="common-flag-sec" [ngClass]="{ 'disabled-color': isReviewCompleted || isEditPage() || !isUserPatient }">
                  <div
                    class="country-popover"
                    id="profile-country-popover"
                    (click)="presentCountryPopover($event)"
                    [ngClass]="{ 'disable-filter': isReviewCompleted || isEditPage() || !isUserPatient }"
                  >
                    <span class="fi fi-{{ countryDetails.code }} flag-image"></span>
                    <ion-icon class="ion-caret-down" name="caret-down"></ion-icon>
                  </div>
                </div>
              </ion-col>
              <ion-col>
                <ion-input
                  readonly="true"
                  formControlName="phoneNumber"
                  class="common-input"
                  id="phone-number"
                  [ngClass]="{
                    'validation-error-border':
                      formControl.phoneNumber?.value &&
                      formControl.phoneNumber?.invalid &&
                      (formControl.phoneNumber?.touched || formControl.phoneNumber?.dirty)
                  }"
                >
                </ion-input>
                <p
                  class="validation-error-text"
                  *ngIf="
                    formControl.phoneNumber?.value &&
                    formControl.phoneNumber?.invalid &&
                    (formControl.phoneNumber?.touched || formControl.phoneNumber?.dirty)
                  "
                >
                  {{ 'ERROR_MESSAGES.INVALID_PHONE_NUMBER' | translate }}
                </p>
              </ion-col>
            </ion-row>

            <ion-row class="row" *ngIf="enableTherapyFields">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.THERAPY_TYPE' | translate }}*</ion-label>
              </ion-col>
              <ion-col size="7">
                <ion-input
                  type="text"
                  formControlName="therapyType"
                  class="common-input"
                  (click)="createVisitForm.value.siteName === '' ? '' : selectTherapyType($event)"
                  (keydown)="$event.stopPropagation()"
                  [ngClass]="{
                    'validation-error-border':
                      formControl.therapyType?.invalid && (formControl.therapyType?.touched || formControl.therapyType?.dirty || formSubmitted)
                  }"
                  id="therapy-type"
                  [disabled]="createVisitForm.value.siteName === ''"
                  readonly="true"
                  [value]="selectedTherapyType?.therapyTypeName"
                >
                </ion-input>
                <p
                  class="validation-error-text"
                  *ngIf="formControl.therapyType?.invalid && (formControl.therapyType?.touched || formControl.therapyType?.dirty || formSubmitted)"
                >
                  {{ 'VALIDATION_MESSAGES.REQUIRED_THERAPY_TYPE' | translate }}
                </p>
              </ion-col>
            </ion-row>

            <!-- Therapy dropdown -->
            <ion-row class="row" *ngIf="enableTherapyFields && createVisitForm.value.therapyType">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.THERAPY' | translate }}*</ion-label>
              </ion-col>
              <ion-col size="7">
                <ion-input
                  type="text"
                  formControlName="dosageAppointment"
                  class="common-input"
                  (click)="selectDosageAppointment($event)"
                  (keydown)="$event.stopPropagation()"
                  [ngClass]="{
                    'validation-error-border':
                      formControl.dosageAppointment?.invalid &&
                      (formControl.dosageAppointment?.touched || formControl.dosageAppointment?.dirty || formSubmitted)
                  }"
                  id="dosage-appointment"
                  readonly="true"
                  [value]="selectedDosageAppointment?.therapyName"
                >
                </ion-input>
                <p
                  class="validation-error-text"
                  *ngIf="
                    formControl.dosageAppointment?.invalid &&
                    (formControl.dosageAppointment?.touched || formControl.dosageAppointment?.dirty || formSubmitted)
                  "
                >
                  {{ 'VALIDATION_MESSAGES.REQUIRED_THERAPY' | translate }}
                </p>
              </ion-col>
            </ion-row>
            <ion-row class="row">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.VISITATION_TYPE' | translate }}*</ion-label>
              </ion-col>
              <ion-col size="7">
                <ion-select
                  name="visitationType"
                  formControlName="visitationType"
                  class="common-input"
                  [ngClass]="{
                    'validation-error-border':
                      formControl.visitationType?.invalid && (formControl.visitationType?.touched || formControl.visitationType?.dirty)
                  }"
                  mode="md"
                  placeholder="{{ 'LABELS.SELECT' | translate }}"
                  id="visitation-type"
                  (ionChange)="visitationTypeChange()"
                  [cancelText]="btnReset"
                  (ionCancel)="resetVisitType()"
                  [interfaceOptions]="{ header: 'LABELS.VISITATION_TYPE' | translate }"
                >
                  <ion-select-option *ngFor="let option of visitationTypes; let i = index" [value]="option.visitType" id="visitation-type-{{ i }}">
                    {{ option.name | translate }}
                  </ion-select-option>
                </ion-select>
                @if (formControl.visitationType?.invalid && (formControl.visitationType?.touched || formControl.visitationType?.dirty)) {
                  <ion-note class="validation-error-text">
                    {{ 'VALIDATION_MESSAGES.INVALID_VISIT_TYPE' | translate }}
                  </ion-note>
                }
              </ion-col>
            </ion-row>
            <ion-row class="row" *ngIf="createVisitForm.value.visitationType === VisitType.AIC">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.VISITATION_TYPE_LOCATION' | translate }}*</ion-label>
              </ion-col>
              <ion-col size="7">
                <ion-input
                  type="text"
                  formControlName="visitationTypeLocation"
                  class="common-input"
                  (click)="selectVisitTypeLocation($event)"
                  [ngClass]="{
                    'validation-error-border':
                      formControl.visitationTypeLocation?.invalid &&
                      (formControl.visitationTypeLocation?.touched || formControl.visitationTypeLocation?.dirty)
                  }"
                  id="visitation-type-location"
                  readonly="true"
                  [value]="selectedVisitLocation?.locationName"
                >
                </ion-input>
                <p
                  class="validation-error-text"
                  *ngIf="
                    formControl.visitationTypeLocation?.invalid &&
                    (formControl.visitationTypeLocation?.touched || formControl.visitationTypeLocation?.dirty)
                  "
                >
                  {{ 'VALIDATION_MESSAGES.REQUIRED_VISIT_LOCATION' | translate }}
                </p>
              </ion-col>
            </ion-row>
            <ion-row class="row"
              *ngIf="createVisitForm.value.visitationType === VisitType.AIC && createVisitForm.value.visitationTypeLocation">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.VISIT_CHAIR' | translate }}*</ion-label>
              </ion-col>
              <ion-col size="7">
                <ion-input
                  type="text"
                  formControlName="visitChair"
                  class="common-input"
                  (click)="selectVisitChair($event)"
                  [ngClass]="{
                    'validation-error-border': formControl.visitChair?.invalid && (formControl.visitChair?.touched || formControl.visitChair?.dirty)
                  }"
                  id="visit_chair"
                  readonly="true"
                  [value]="selectedVisitChair?.chairName"
                >
                </ion-input>
                <p
                  class="validation-error-text"
                  *ngIf="formControl.visitChair?.invalid && (formControl.visitChair?.touched || formControl.visitChair?.dirty)"
                >
                  {{ 'VALIDATION_MESSAGES.REQUIRED_VISIT_CHAIR' | translate }}
                </p>
              </ion-col>
            </ion-row>
            <ion-row class="row">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.VISIT_ADDRESS' | translate }}</ion-label>
              </ion-col>
              <ion-col size="7">
                <ion-textarea formControlName="visitAddress" type="text" class="common-input" rows="4" id="visit-address" autocapitalize="on">
                </ion-textarea>
              </ion-col>
            </ion-row>
          </ion-list>
        </ion-accordion>
        <ion-accordion id="schedule-details-accordion" value="schedule-details-accordion">
          <ion-item slot="header" class="accordion-head" [ngClass]="{ 'validation-error-header': hasScheduleDetailsErrors() }">
            {{ 'LABELS.SCHEDULE_DETAILS' | translate }}
          </ion-item>
          <ion-list slot="content" class="content">
            <ion-row class="row">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.VISIT_TITLE' | translate }}*</ion-label>
              </ion-col>
              <ion-col size="7">
                <ion-input
                  formControlName="visitTitle"
                  type="text"
                  class="common-input"
                  [ngClass]="{
                    'validation-error-border': formControl.visitTitle?.invalid && (formControl.visitTitle?.touched || formControl.visitTitle?.dirty)
                  }"
                  id="visit-title"
                  autocapitalize="on"
                >
                </ion-input>
                <p
                  class="validation-error-text"
                  *ngIf="formControl.visitTitle?.invalid && (formControl.visitTitle?.touched || formControl.visitTitle?.dirty)"
                >
                  {{ 'VALIDATION_MESSAGES.REQUIRED_VISIT_TITLE' | translate }}
                </p>
              </ion-col>
            </ion-row>
            <ion-row class="row">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.VISIT_DETAILS' | translate }}</ion-label>
              </ion-col>
              <ion-col size="7">
                <ion-textarea formControlName="visitDetails" type="text" class="common-input" rows="4" id="visit-details" autocapitalize="on">
                </ion-textarea>
              </ion-col>
            </ion-row>
            <ion-row class="row">
              <ion-col>
                <span>{{ 'LABELS.TIMEZONE' | translate }}</span>
              </ion-col>
              <ion-col size="7">
                <ion-select
                  id="timezone"
                  formControlName="timeZone"
                  *ngIf="timeZones"
                  class="form-field-right common-input"
                  mode="md"
                  (ionChange)="timeZoneChange(); onRevisedChange()"
                  [interfaceOptions]="{ header: 'LABELS.TIMEZONE' | translate }"
                >
                  <ion-select-option *ngFor="let option of timeZones" [value]="option?.city">{{ option?.name | translate }} </ion-select-option>
                </ion-select>
              </ion-col>
            </ion-row>
            <ion-row class="row ion-margin-top ion-margin-bottom" class="ion-align-items-center" *ngIf="!isEditPage()">
              <ion-col size="5" class="ion-align-items-center">
                <ion-label>{{ 'LABELS.MULTIPLE_VISITS' | translate }}</ion-label>
              </ion-col>
              <ion-col size="2">
                <ion-toggle
                  color="success"
                  formControlName="showMultipleDatesSelection"
                  mode="ios"
                  id="show-update-time"
                  (ionChange)="toggleMultipleDates()"
                ></ion-toggle>
              </ion-col>
            </ion-row>
            <ion-row class="row ion-align-items-center"
              *ngIf="!isEditPage() && createVisitForm.value.showMultipleDatesSelection">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.MULTIPLE_DATES' | translate }}</ion-label>
              </ion-col>
              <ion-col size="7">
                <ion-input type="text" formControlName="multipleDates" id="multiple-date" class="common-input" autocapitalize="on">
                  <ion-icon name="calendar-sharp" class="icon-size" slot="start"></ion-icon>
                </ion-input>
                <ion-modal trigger="multiple-date" id="custom-modal" show-backdrop="true" mode="ios">
                  <ng-template>
                    <ion-datetime
                      #multipleDates
                      presentation="date"
                      [multiple]="true"
                      show-default-buttons="true"
                      (ionChange)="multipleDateChange(multipleDates.value)"
                      id="multiple-date-picker"
                      [value]="createVisitForm.value.multipleDatesFormat"
                      [max]="maxYear"
                      mode="ios"
                      color="blumine"
                      cancelText="{{ 'BUTTONS.CANCEL_UPPER' | translate }}"
                      doneText="{{ 'BUTTONS.DONE_UPPER' | translate }}"
                    ></ion-datetime>
                  </ng-template>
                </ion-modal>
              </ion-col>
            </ion-row>
            <ion-row
              *ngIf="createVisitForm.value.showMultipleDatesSelection && createVisitForm.value.multipleDatesFormat.length > 2">
              <ion-col>
                <ion-text color="warning">
                  <ion-icon name="warning"></ion-icon>
                  {{ 'MESSAGES.VISIT_SELECTED_DATES_NOTE' | translate: { selectedDates: createVisitForm.value.multipleDates } }}
                </ion-text>
              </ion-col>
            </ion-row>
            <ion-row class="row ion-align-items-center"
              *ngIf="!createVisitForm.value.showMultipleDatesSelection && !isEditSeriesMode()">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.SCHEDULE_START_DATE' | translate }}</ion-label>
              </ion-col>
              <ion-col size="7">
                <ion-input type="text" formControlName="startDate" id="start-date" class="common-input" autocapitalize="on">
                  <ion-icon name="calendar-sharp" class="icon-size" slot="start"></ion-icon>
                </ion-input>
                <ion-modal trigger="start-date" id="custom-modal" show-backdrop="true" mode="ios" *ngIf="!createVisitForm.get('startDate').disabled">
                  <ng-template>
                    <ion-datetime
                      #startDate
                      presentation="date"
                      show-default-buttons="true"
                      (ionChange)="startDateChange(startDate.value)"
                      [value]="changeDateFormat(createVisitForm.value.startDate, dateFormat.ymd)"
                      mode="ios"
                      color="blumine"
                      id="start-date-picker"
                      [max]="maxYear"
                      cancelText="{{ 'BUTTONS.CANCEL_UPPER' | translate }}"
                      doneText="{{ 'BUTTONS.DONE_UPPER' | translate }}"
                    >
                    </ion-datetime>
                  </ng-template>
                </ion-modal>
              </ion-col>
            </ion-row>
            <ion-row class="row ion-align-items-center" *ngIf="isEditSeriesMode()">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.SERIES_START_DATE' | translate }}</ion-label>
              </ion-col>
              <ion-col size="7">
                <ion-input type="text" formControlName="seriesStartDate" id="series-start-date" class="common-input" autocapitalize="on">
                  <ion-icon name="calendar-sharp" class="icon-size" slot="start"></ion-icon>
                </ion-input>
                <ion-modal
                  trigger="series-start-date"
                  id="custom-modal"
                  show-backdrop="true"
                  mode="ios"
                  *ngIf="!createVisitForm.get('seriesStartDate').disabled"
                >
                  <ng-template>
                    <ion-datetime
                      #seriesStartDate
                      presentation="date"
                      show-default-buttons="true"
                      (ionChange)="seriesStartDateChange(seriesStartDate.value)"
                      [value]="changeDateFormat(createVisitForm.value.seriesStartDate, dateFormat.ymd)"
                      mode="ios"
                      color="blumine"
                      id="series-start-date-picker"
                      [max]="maxYear"
                      cancelText="{{ 'BUTTONS.CANCEL_UPPER' | translate }}"
                      doneText="{{ 'BUTTONS.DONE_UPPER' | translate }}"
                    >
                    </ion-datetime>
                  </ng-template>
                </ion-modal>
              </ion-col>
            </ion-row>
            <ion-row class="row ion-align-items-center" *ngIf="isEditSeriesMode()">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.SERIES_END_DATE' | translate }}</ion-label>
              </ion-col>
              <ion-col size="7">
                <ion-input type="text" formControlName="seriesEndDate" id="series-end-date" class="common-input" autocapitalize="on">
                  <ion-icon name="calendar-sharp" class="icon-size" slot="start"></ion-icon>
                </ion-input>
                <ion-modal
                  trigger="series-end-date"
                  id="custom-modal"
                  show-backdrop="true"
                  mode="ios"
                  *ngIf="!createVisitForm.get('seriesEndDate').disabled"
                >
                  <ng-template>
                    <ion-datetime
                      #seriesEndDate
                      presentation="date"
                      show-default-buttons="true"
                      (ionChange)="seriesEndDateChange(seriesEndDate.value)"
                      [min]="endOnSeriesMinDate"
                      [value]="changeDateFormat(createVisitForm.value.seriesEndDate, dateFormat.ymd)"
                      mode="ios"
                      color="blumine"
                      id="series-end-date-picker"
                      [max]="maxYear"
                      cancelText="{{ 'BUTTONS.CANCEL_UPPER' | translate }}"
                      doneText="{{ 'BUTTONS.DONE_UPPER' | translate }}"
                    >
                    </ion-datetime>
                  </ng-template>
                </ion-modal>
              </ion-col>
            </ion-row>
            <ion-row class="row ion-align-items-center">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.SCHEDULE_START_TIME' | translate }}</ion-label>
              </ion-col>
              <ion-col size="7">
                <ion-input type="text" formControlName="startTime" id="start-time" class="common-input" autocapitalize="on">
                  <ion-icon name="time-outline" class="icon-size" slot="start"></ion-icon>
                </ion-input>
                <ion-modal trigger="start-time" id="time-modal" show-backdrop="true" mode="ios" *ngIf="!createVisitForm.get('startTime').disabled">
                  <ng-template>
                    <ion-datetime
                      #startTimeSelection
                      presentation="time"
                      show-default-buttons="true"
                      [value]="fetchStartDateTime"
                      (ionChange)="startTimeChange(startTimeSelection.value)"
                      mode="ios"
                      id="start-time-picker"
                      hourCycle="h12"
                      color="blumine"
                      cancelText="{{ 'BUTTONS.CANCEL_UPPER' | translate }}"
                      doneText="{{ 'BUTTONS.DONE_UPPER' | translate }}"
                    >
                    </ion-datetime>
                  </ng-template>
                </ion-modal>
              </ion-col>
            </ion-row>
            <ion-row class="row ion-align-items-center" *ngIf="!createVisitForm.value.showMultipleDatesSelection && !isEditSeriesMode()">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.SCHEDULE_END_DATE' | translate }}</ion-label>
              </ion-col>
              <ion-col size="7">
                <ion-input type="text" formControlName="endDate" id="end-date" class="common-input" autocapitalize="on">
                  <ion-icon name="calendar-sharp" class="icon-size" slot="start"></ion-icon>
                </ion-input>
                <ion-modal trigger="end-date" id="custom-modal" show-backdrop="true" mode="ios" *ngIf="!createVisitForm.get('endDate').disabled">
                  <ng-template>
                    <ion-datetime
                      #endDate
                      presentation="date"
                      show-default-buttons="true"
                      (ionChange)="endDateChange(endDate.value)"
                      [value]="changeDateFormat(createVisitForm.value.endDate, dateFormat.ymd)"
                      mode="ios"
                      id="end-date-picker"
                      [min]="changeDateFormat(createVisitForm.value.startDate, dateFormat.ymd)"
                      [max]="maxYear"
                      color="blumine"
                      cancelText="{{ 'BUTTONS.CANCEL_UPPER' | translate }}"
                      doneText="{{ 'BUTTONS.DONE_UPPER' | translate }}"
                    >
                    </ion-datetime>
                  </ng-template>
                </ion-modal>
              </ion-col>
            </ion-row>
            <ion-row class="row ion-align-items-center">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.SCHEDULE_END_TIME' | translate }}</ion-label>
              </ion-col>
              <ion-col size="7">
                <ion-input
                  type="text"
                  formControlName="endTime"
                  id="end-time"
                  class="common-input"
                  [ngClass]="{ 'validation-error-border': dateTimeValidator }"
                  autocapitalize="on"
                >
                  <ion-icon name="time-outline" class="icon-size" slot="start"></ion-icon>
                </ion-input>
                <p class="validation-error-text" *ngIf="dateTimeValidator">
                  {{ 'VALIDATION_MESSAGES.VALIDATE_DATE_TIME' | translate }}
                </p>
                <ion-modal trigger="end-time" id="time-modal" show-backdrop="true" mode="ios" *ngIf="!createVisitForm.get('endTime').disabled">
                  <ng-template>
                    <ion-datetime
                      #endTimeSelection
                      presentation="time"
                      hourCycle="h12"
                      show-default-buttons="true"
                      (ionChange)="endTimeChange(endTimeSelection.value)"
                      id="end-time-picker"
                      [value]="fetchEndDateTime"
                      mode="ios"
                      color="blumine"
                      cancelText="{{ 'BUTTONS.CANCEL_UPPER' | translate }}"
                      doneText="{{ 'BUTTONS.DONE_UPPER' | translate }}"
                    >
                    </ion-datetime>
                  </ng-template>
                </ion-modal>
              </ion-col>
            </ion-row>
            <ion-row *ngIf="isVisitInPastEvent" class="ion-text-center">
              <ion-col>
                <ion-text color="warning">
                  <ion-icon name="warning"></ion-icon>
                  {{ 'LABELS.DATE_TIME_DISABLED_NOTE_FOR_EVENT' | translate }}
                </ion-text>
              </ion-col>
            </ion-row>
            <ion-row *ngIf="isVisitInPastSeries" class="ion-text-center">
              <ion-col>
                <ion-text color="warning">
                  <ion-icon name="warning"></ion-icon>
                  {{ 'LABELS.DATE_TIME_DISABLED_NOTE_FOR_SERIES' | translate }}
                </ion-text>
              </ion-col>
            </ion-row>
            <ion-row class="row ion-align-items-center" [hidden]="!viewRecurrenceTab">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.RECURRENCE' | translate }}</ion-label>
              </ion-col>
              <ion-col size="7">
                <ion-toggle
                  color="success"
                  formControlName="recurrence"
                  mode="ios"
                  id="recurrence"
                  [disabled]="createVisitForm.value.showMultipleDatesSelection"
                  (ionChange)="recurrenceChange()"
                ></ion-toggle>
              </ion-col>
            </ion-row>
            <div [ngClass]="{ 'hide-div': (visitKey && !isEditSeriesMode()) || !createVisitForm.value.recurrence, disabled: isVisitInPastSeries }">
              <ion-row>
                <ion-col size="12">
                  <ion-accordion-group value="first" #accordionGroup>
                    <ion-accordion value="first" id="recurrence-accordion">
                      <ion-item slot="header" color="de-york">
                        <ion-label>{{ 'LABELS.REPEAT_PATTERN' | translate }}</ion-label>
                      </ion-item>
                      <div class="ion-padding" slot="content">
                        <ion-row
                          class="row ion-margin-top ion-margin-bottom"
                          *ngIf="isVisitInProgressSeries && !(isRevisedDatesActive && !createVisitForm.value.showRevisedTimeUpdate)"
                          class="ion-align-items-center"
                        >
                          <ion-col size="10" class="ion-align-items-center">
                            <ion-label>{{ 'LABELS.DO_YOU_WANT_TO_UPDATE_SERIES_DATE_AND_TIME' | translate }}</ion-label>
                          </ion-col>
                          <ion-col size="2">
                            <ion-toggle
                              color="success"
                              formControlName="showRevisedTimeUpdate"
                              (ngModelChange)="onRevisedChange()"
                              mode="ios"
                              id="show-update-time"
                            ></ion-toggle>
                          </ion-col>
                        </ion-row>
                        <ion-row class="row ion-align-items-center" *ngIf="isRevisedDatesActive">
                          <ion-col size="5">
                            <ion-label>{{ 'LABELS.REVISED_START_DATE' | translate }}</ion-label>
                          </ion-col>
                          <ion-col size="7">
                            <ion-input
                              type="text"
                              formControlName="revisedStartDate"
                              id="revised-start-date"
                              class="common-input"
                              autocapitalize="on"
                            >
                              <ion-icon name="calendar-sharp" class="icon-size" slot="start"></ion-icon>
                            </ion-input>
                            <ion-modal trigger="revised-start-date" id="custom-modal" show-backdrop="true" mode="ios">
                              <ng-template>
                                <ion-datetime
                                  #revisedStartDate
                                  presentation="date"
                                  show-default-buttons="true"
                                  (ionChange)="revisedStartDateChange(revisedStartDate.value)"
                                  [value]="changeDateFormat(createVisitForm.value.revisedStartDate, dateFormat.ymd)"
                                  mode="ios"
                                  color="blumine"
                                  id="revised-start-date-picker"
                                  [max]="maxYear"
                                  [min]="revisedStartDateMinValue"
                                  cancelText="{{ 'BUTTONS.CANCEL_UPPER' | translate }}"
                                  doneText="{{ 'BUTTONS.DONE_UPPER' | translate }}"
                                >
                                </ion-datetime>
                              </ng-template>
                            </ion-modal>
                          </ion-col>
                        </ion-row>
                        <ion-row class="row ion-align-items-center" *ngIf="isRevisedDatesActive">
                          <ion-col size="5">
                            <ion-label>{{ 'LABELS.REVISED_START_TIME' | translate }}</ion-label>
                          </ion-col>
                          <ion-col size="7">
                            <ion-input
                              type="text"
                              formControlName="revisedStartTime"
                              id="revised-start-time"
                              class="common-input"
                              autocapitalize="on"
                            >
                              <ion-icon name="time-outline" class="icon-size" slot="start"></ion-icon>
                            </ion-input>
                            <ion-modal trigger="revised-start-time" id="time-modal" show-backdrop="true" mode="ios">
                              <ng-template>
                                <ion-datetime
                                  #revisedStartTimeSelection
                                  presentation="time"
                                  show-default-buttons="true"
                                  [value]="fetchRevisedStartDateTime"
                                  (ionChange)="revisedStartTimeChange(revisedStartTimeSelection.value)"
                                  mode="ios"
                                  id="revised-start-time-picker"
                                  hourCycle="h12"
                                  color="blumine"
                                  cancelText="{{ 'BUTTONS.CANCEL_UPPER' | translate }}"
                                  doneText="{{ 'BUTTONS.DONE_UPPER' | translate }}"
                                >
                                </ion-datetime>
                              </ng-template>
                            </ion-modal>
                          </ion-col>
                        </ion-row>
                        <ion-row class="row ion-align-items-center" *ngIf="isRevisedDatesActive">
                          <ion-col size="5">
                            <ion-label>{{ 'LABELS.REVISED_END_TIME' | translate }}</ion-label>
                          </ion-col>
                          <ion-col size="7">
                            <ion-input type="text" formControlName="revisedEndTime" id="revised-end-time" class="common-input" autocapitalize="on">
                              <ion-icon name="time-outline" class="icon-size" slot="start"></ion-icon>
                            </ion-input>
                            <ion-modal trigger="revised-end-time" id="time-modal" show-backdrop="true" mode="ios">
                              <ng-template>
                                <ion-datetime
                                  #revisedEndTimeSelection
                                  presentation="time"
                                  hourCycle="h12"
                                  show-default-buttons="true"
                                  (ionChange)="revisedEndTimeChange(revisedEndTimeSelection.value)"
                                  id="revised-end-time-picker"
                                  [value]="fetchRevisedEndDateTime"
                                  mode="ios"
                                  color="blumine"
                                  cancelText="{{ 'BUTTONS.CANCEL_UPPER' | translate }}"
                                  doneText="{{ 'BUTTONS.DONE_UPPER' | translate }}"
                                >
                                </ion-datetime>
                              </ng-template>
                            </ion-modal>
                          </ion-col>
                        </ion-row>
                        <ion-row class="row ion-align-items-center">
                          <ion-col size="5">
                            <ion-label>{{ 'LABELS.REPEAT_EVERY' | translate }}</ion-label>
                          </ion-col>
                          <ion-col size="7">
                            <ion-select
                              name="repeatEvery"
                              formControlName="repeatEvery"
                              (ngModelChange)="onRevisedChange()"
                              class="common-input"
                              mode="md"
                              id="repeat-every"
                              [interfaceOptions]="{ header: 'LABELS.REPEAT_EVERY' | translate }"
                            >
                              <ion-select-option
                                *ngFor="let option of repeatIntervals; let i = index"
                                [value]="option.value"
                                id="repeat-every-{{ i }}"
                              >
                                {{ option.value | translate }}
                              </ion-select-option>
                            </ion-select>
                          </ion-col>
                        </ion-row>
                        <ion-row class="row">
                          <ion-col size="5"></ion-col>
                          <ion-col size="7">
                            <ion-select
                              name="scheduleCadence"
                              formControlName="scheduleCadence"
                              class="common-input"
                              (ionChange)="scheduleCadenceChange(); onRevisedChange()"
                              mode="md"
                              id="schedule-cadence"
                            >
                              <ion-select-option
                                *ngFor="let option of repeatSchedules; let i = index"
                                [value]="option.value"
                                id="schedule-cadence-{{ i }}"
                                [disabled]="option.disabled"
                              >
                                {{ option.name | translate }}
                              </ion-select-option>
                            </ion-select>
                          </ion-col>
                        </ion-row>
                        <ion-row class="row" [hidden]="!(scheduleTab && createVisitForm.controls['scheduleCadence'].value === 'weekly')">
                          <ion-col size="5"></ion-col>
                          <ion-col size="7">
                            <ion-select
                              name="weekly"
                              formControlName="weekly"
                              (ngModelChange)="onRevisedChange()"
                              class="common-input"
                              mode="md"
                              id="weekly-schedule"
                              [multiple]="true"
                            >
                              <ion-select-option
                                *ngFor="let option of weeklySchedule; let i = index"
                                [value]="option.value"
                                id="weekly-schedule-{{ i }}"
                                [disabled]="option.disabled"
                              >
                                {{ option.name | translate }}
                              </ion-select-option>
                            </ion-select>
                          </ion-col>
                        </ion-row>
                        <ion-row class="row" [hidden]="!(scheduleTab && createVisitForm.controls['scheduleCadence'].value === 'monthly')">
                          <ion-col size="5"></ion-col>
                          <ion-col size="7">
                            <ion-select
                              name="monthly"
                              formControlName="monthly"
                              (ngModelChange)="onRevisedChange()"
                              class="common-input"
                              mode="md"
                              id="monthly-schedule"
                            >
                              <ion-select-option
                                *ngFor="let option of monthlySchedule; let i = index"
                                [value]="option.value"
                                id="monthly-schedule-{{ i }}"
                              >
                                {{ option.name | translate }}
                              </ion-select-option>
                            </ion-select>
                          </ion-col>
                        </ion-row>
                        <ion-row class="row ion-align-items-center" [hidden]="!dailyUntilDateShow"
                          *ngIf="!isRevisedDatesActive">
                          <ion-col size="5">
                            <ion-label>{{ 'LABELS.END_ON' | translate }}</ion-label>
                          </ion-col>
                          <ion-col size="7">
                            <ion-input
                              type="text"
                              formControlName="endOn"
                              id="end-on"
                              class="common-input"
                              [ngClass]="{ 'validation-error-border': endOnDateTimeValidator }"
                              autocapitalize="on"
                            >
                              <ion-icon name="calendar-sharp" class="icon-size" slot="start"></ion-icon>
                            </ion-input>
                            <p class="validation-error-text" *ngIf="endOnDateTimeValidator">
                              {{ 'VALIDATION_MESSAGES.VALIDATE_END_ON_DATE' | translate }}
                            </p>
                            <ion-modal trigger="end-on" id="custom-modal" show-backdrop="true" mode="ios">
                              <ng-template>
                                <ion-datetime
                                  #endOn
                                  presentation="date"
                                  show-default-buttons="true"
                                  (ionChange)="endOnChange(endOn.value)"
                                  mode="ios"
                                  color="blumine"
                                  [min]="endOnMinDate"
                                  [value]="changeDateFormat(createVisitForm.value.endOn, dateFormat.ymd)"
                                  [max]="maxYear"
                                  cancelText="{{ 'BUTTONS.CANCEL_UPPER' | translate }}"
                                  doneText="{{ 'BUTTONS.DONE_UPPER' | translate }}"
                                >
                                </ion-datetime>
                              </ng-template>
                            </ion-modal>
                          </ion-col>
                        </ion-row>
                        <ion-row class="row ion-align-items-center" *ngIf="isRevisedDatesActive">
                          <ion-col size="5">
                            <ion-label>{{ 'LABELS.REVISED_END_DATE' | translate }}</ion-label>
                          </ion-col>
                          <ion-col size="7">
                            <ion-input type="text" formControlName="revisedEndDate" id="revised-end-date" class="common-input" autocapitalize="on">
                              <ion-icon name="calendar-sharp" class="icon-size" slot="start"></ion-icon>
                            </ion-input>
                            <ion-modal trigger="revised-end-date" id="custom-modal" show-backdrop="true" mode="ios">
                              <ng-template>
                                <ion-datetime
                                  #revisedEndDate
                                  presentation="date"
                                  show-default-buttons="true"
                                  (ionChange)="revisedEndDateChange(revisedEndDate.value)"
                                  [min]="revisedEndDateMinValue"
                                  [value]="changeDateFormat(createVisitForm.value.revisedEndDate, dateFormat.ymd)"
                                  mode="ios"
                                  color="blumine"
                                  id="revised-end-date-picker"
                                  [max]="maxYear"
                                  cancelText="{{ 'BUTTONS.CANCEL_UPPER' | translate }}"
                                  doneText="{{ 'BUTTONS.DONE_UPPER' | translate }}"
                                >
                                </ion-datetime>
                              </ng-template>
                            </ion-modal>
                          </ion-col>
                        </ion-row>
                        <ion-row class="row">
                          <ion-col size="5"></ion-col>
                          <ion-col size="2">
                            <ion-button fill="clear" id="checkmark" (click)="toggleAccordion()">
                              <ion-icon name="checkmark" color="success"></ion-icon>
                            </ion-button>
                          </ion-col>
                          <ion-col size="2" *ngIf="!isEditPage()">
                            <ion-button fill="clear" id="close" (click)="closeRecurrence()">
                              <ion-icon name="close" color="danger"></ion-icon>
                            </ion-button>
                          </ion-col>
                          <ion-col size="2" *ngIf="!isEditPage()" (click)="deleteRecurrence()">
                            <ion-button fill="clear" id="remove">
                              <ion-icon name="trash-sharp"></ion-icon>
                            </ion-button>
                          </ion-col>
                        </ion-row>
                      </div>
                    </ion-accordion>
                  </ion-accordion-group>
                </ion-col>
              </ion-row>
            </div>
            <ion-row class="row ion-align-items-center">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.BILLABLE' | translate }}</ion-label>
              </ion-col>
              <ion-col size="7">
                <ion-toggle color="success" formControlName="billable" mode="ios" id="billable"></ion-toggle>
              </ion-col>
            </ion-row>
          </ion-list>
        </ion-accordion>
        <ion-accordion id="schedule-status-accordion" value="schedule-status-accordion">
          <ion-item slot="header" class="accordion-head" [ngClass]="{ 'validation-error-header': hasScheduleStatusErrors() }">
            {{ 'LABELS.SCHEDULE_STATUS' | translate }}
          </ion-item>
          <ion-list slot="content" class="content">
            <ion-row class="row">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.VISIT_ASSIGNED_TO' | translate }} <span *ngIf="isRequiredField('visitAssignedTo')">* </span></ion-label>
              </ion-col>
              <ion-col size="7">
                <ion-input
                  type="text"
                  formControlName="visitAssignedTo"
                  (ngModelChange)="onRevisedChange()"
                  class="common-input disable-cursor"
                  [ngClass]="{
                    'validation-error-border':
                      isRequiredField('visitAssignedTo') && formControl.visitAssignedTo?.invalid && (formControl.visitAssignedTo?.touched || formControl.visitAssignedTo?.dirty)
                  }"
                  (click)="selectStaff($event)"
                  id="visit-assigned-to"
                  readonly="true"
                >
                  <span *ngIf="!createVisitForm.controls['visitAssignedTo'].value" class="custom-placeholder">
                    {{ 'LABELS.STAFF_CLINICIAN' | translate }}
                  </span>
                </ion-input>
                <p
                  class="validation-error-text"
                  *ngIf="
                    isRequiredField('visitAssignedTo') &&
                    formControl.visitAssignedTo?.invalid &&
                    (formControl.visitAssignedTo?.touched || formControl.visitAssignedTo?.dirty)
                  "
                >
                  {{ 'VALIDATION_MESSAGES.REQUIRED_VISIT_ASSIGNED_TO' | translate }}
                </p>
              </ion-col>
            </ion-row>
            <ion-row class="row">
              <ion-col size="5"></ion-col>
              <ion-col size="7">
                <ion-button class="btn-sm ion-text-capitalize" (click)="checkAvailability()" id="view-availability" color="de-york">
                  {{ 'BUTTONS.VIEW_AVAILABILITY' | translate }}
                </ion-button>
              </ion-col>
            </ion-row>
            <ion-row class="row">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.SUBCONTRACTED' | translate }}</ion-label>
              </ion-col>
              <ion-col size="7">
                <ion-toggle
                  color="success"
                  formControlName="subcontracted"
                  mode="ios"
                  (ionChange)="subcontractChange(); onRevisedChange()"
                  [checked]="createVisitForm.controls['subcontracted'].value"
                  id="subcontracted"
                >
                </ion-toggle>
              </ion-col>
            </ion-row>
            <ion-row class="row" [hidden]="!createVisitForm.controls.subcontracted.value">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.ORGANIZATION' | translate }}*</ion-label>
              </ion-col>
              <ion-col size="7">
                <ion-input
                  formControlName="organization"
                  type="text"
                  class="common-input"
                  [ngClass]="{
                    'validation-error-border':
                      formControl.organization?.invalid && (formControl.organization?.touched || formControl.organization?.dirty)
                  }"
                  id="organization"
                  (ionChange)="organizationChange(); onRevisedChange()"
                  autocapitalize="on"
                >
                </ion-input>
                <p
                  class="validation-error-text"
                  *ngIf="formControl.organization?.invalid && (formControl.organization?.touched || formControl.organization?.dirty)"
                >
                  {{ 'VALIDATION_MESSAGES.UPDATE_ORGANIZATION' | translate }}
                </p>
              </ion-col>
            </ion-row>
            <ion-row class="row">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.STAFF_PARTNER_VISIT_STATUS' | translate }}</ion-label>
              </ion-col>
              <ion-col size="7">
                <ion-select
                  name="staffPartnerVisitStatus"
                  formControlName="staffPartnerVisitStatus"
                  class="common-input"
                  [ngClass]="{ 'permission-disabled': !canChangeVisitStatus() }"
                  (ionChange)="staffStatusChange()"
                  mode="md"
                  id="staff-partner"
                  [disabled]="!canChangeVisitStatus()"
                  [interfaceOptions]="{ header: 'LABELS.STAFF_PARTNER_VISIT_STATUS' | translate }"
                >
                  <ion-select-option
                    *ngFor="let option of staffPartnerVisitStatusData; let i = index"
                    [value]="option.value"
                    id="staff-partner-{{ i }}"
                    [disabled]="option.disabled"
                  >
                    {{ option.text | translate }}</ion-select-option
                  >
                </ion-select>
                <p
                  class="validation-error-text"
                  *ngIf="createVisitForm.controls.staffPartnerVisitStatus.value === staffStatus[4].value && !actualDateTimeFilled"
                >
                  {{ 'VALIDATION_MESSAGES.ACTUAL_DATE_TIME_VALIDATE' | translate }}
                </p>
              </ion-col>
            </ion-row>
            <ion-row
              class="row"
              *ngIf="
                createVisitForm.controls.staffPartnerVisitStatus.value === staffStatus[5].value ||
                createVisitForm.controls.staffPartnerVisitStatus.value === staffStatus[3].value
              "
            >
              <ion-col size="5" *ngIf="createVisitForm.controls.staffPartnerVisitStatus.value === staffStatus[5].value">
                <ion-label>{{ 'LABELS.REASON_FOR_CANCEL' | translate }}*</ion-label>
              </ion-col>
              <ion-col size="5" *ngIf="createVisitForm.controls.staffPartnerVisitStatus.value === staffStatus[3].value">
                <ion-label>{{ 'LABELS.REASON_FOR_STAFF_DECLINE' | translate }}*</ion-label>
              </ion-col>
              <ion-col size="7">
                <ion-input
                  formControlName="staffReasonForCancel"
                  type="text"
                  class="common-input"
                  [ngClass]="{
                    'validation-error-border':
                      formControl.staffReasonForCancel.invalid &&
                      (createVisitForm.controls.staffPartnerVisitStatus.value === staffStatus[5].value ||
                        createVisitForm.controls.staffPartnerVisitStatus.value === staffStatus[3].value)
                  }"
                  [required]="
                    createVisitForm.controls.staffPartnerVisitStatus.value === staffStatus[5].value ||
                    createVisitForm.controls.staffPartnerVisitStatus.value === staffStatus[3].value
                  "
                  id="txt-cancel-decline-reason"
                  autocapitalize="on"
                >
                </ion-input>
                <p
                  class="validation-error-text"
                  *ngIf="formControl.staffReasonForCancel.invalid && createVisitForm.controls.staffPartnerVisitStatus.value === staffStatus[5].value"
                >
                  {{ 'VALIDATION_MESSAGES.VISIT_CANCEL_REASON' | translate }}
                </p>
                <p
                  class="validation-error-text"
                  *ngIf="formControl.staffReasonForCancel.invalid && createVisitForm.controls.staffPartnerVisitStatus.value === staffStatus[3].value"
                >
                  {{ 'VALIDATION_MESSAGES.ENTER_REASON_FOR_STAFF_DECLINE' | translate }}
                </p>
              </ion-col>
            </ion-row>
            <ion-row class="row" *ngIf="createVisitForm.controls.staffPartnerVisitStatus.value === staffStatus[4].value || visitData?.timeInLatLong">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.ACTUAL_DATE' | translate }}*</ion-label>
              </ion-col>
              <ion-col>
                <ion-input
                  type="text"
                  formControlName="actualDate"
                  id="visit-actualDate"
                  class="common-input"
                  [ngClass]="{
                    'validation-error-border':
                      (formControl.actualDate.invalid || !formControl.actualDate.value) &&
                      createVisitForm.controls.staffPartnerVisitStatus.value === staffStatus[4].value
                  }"
                  [required]="createVisitForm.controls.staffPartnerVisitStatus.value === staffStatus[4].value"
                  autocapitalize="on"
                >
                  <ion-icon name="calendar-sharp" class="icon-size" slot="start"></ion-icon>
                </ion-input>
                <ion-modal trigger="visit-actualDate" id="custom-modal" show-backdrop="true" mode="ios">
                  <ng-template>
                    <ion-datetime
                      #visitActualDateSelect
                      presentation="date"
                      show-default-buttons="true"
                      (ionChange)="actualDateSelection(visitActualDateSelect.value)"
                      mode="ios"
                      color="blumine"
                      [max]="maxYear"
                      [value]="createVisitForm.value.actualDate ? changeDateFormat(createVisitForm.value.actualDate, dateFormat.ymd) : null"
                      cancelText="{{ 'BUTTONS.CANCEL_UPPER' | translate }}"
                      doneText="{{ 'BUTTONS.DONE_UPPER' | translate }}"
                    >
                    </ion-datetime>
                  </ng-template>
                </ion-modal>
              </ion-col>
            </ion-row>
            <ion-row class="row" *ngIf="createVisitForm.controls.staffPartnerVisitStatus.value === staffStatus[4].value || visitData?.actualTimeIn">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.ACTUAL_TIME_IN' | translate }}*</ion-label>
              </ion-col>
              <ion-col [size]="visitData?.timeInLatLong ? 6 : 7">
                <ion-input
                  type="text"
                  formControlName="actualTimeIn"
                  id="actualTimeIn"
                  class="common-input"
                  placeholder="{{ timePlaceholder }}"
                  [readonly]="!canEditVisitFields()"
                  [ngClass]="{
                    'validation-error-border':
                      (formControl.actualTimeIn.invalid || !formControl.actualTimeIn.value) &&
                      createVisitForm.controls.staffPartnerVisitStatus.value === staffStatus[4].value,
                    'permission-readonly': !canEditVisitFields()
                  }"
                  [required]="createVisitForm.controls.staffPartnerVisitStatus.value === staffStatus[4].value"
                  autocapitalize="on"
                >
                  <ion-icon name="time-outline" class="icon-size" slot="start"></ion-icon>
                </ion-input>
                <ion-modal trigger="actualTimeIn" id="time-modal" mode="ios">
                  <ng-template>
                    <ion-datetime
                      #actualTimeInSelection
                      presentation="time"
                      hourCycle="h12"
                      show-default-buttons="true"
                      [value]="fetchActualDateTimeIn"
                      id="actual-time-in-picker"
                      (ionChange)="updateActualTime(visitScheduleConstants.actualTimeIn, actualTimeInSelection.value)"
                      mode="ios"
                      color="blumine"
                      cancelText="{{ 'BUTTONS.CANCEL_UPPER' | translate }}"
                      doneText="{{ 'BUTTONS.DONE_UPPER' | translate }}"
                    >
                    </ion-datetime>
                  </ng-template>
                </ion-modal>
              </ion-col>
              <ion-col size="1" *ngIf="visitData?.timeInLatLong" class="show-icon-center" (click)="showGoogleMapWithMarker(visitData?.timeInLatLong)">
                <ion-icon size="large" name="location"></ion-icon>
              </ion-col>
            </ion-row>
            <ion-row class="row" *ngIf="visitData?.timeInAddress">
              <ion-col size="5"></ion-col>
              <ion-col size="7">
                <ion-textarea
                  [value]="visitData?.timeInAddress"
                  class="common-input set-disable-input"
                  id="visit-time-in-address"
                  readonly="true"
                  [rows]="dynamicRows"
                >
                </ion-textarea>
              </ion-col>
            </ion-row>
            <ion-row class="row" [hidden]="createVisitForm.controls.staffPartnerVisitStatus.value !== staffStatus[4].value">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.ACTUAL_TIME_OUT' | translate }}*</ion-label>
              </ion-col>
              <ion-col [size]="visitData?.timeOutLatLong ? 6 : 7">
                <ion-input
                  type="text"
                  formControlName="actualTimeOut"
                  id="actualTimeOut"
                  class="common-input"
                  placeholder="{{ timePlaceholder }}"
                  [readonly]="!canEditVisitFields()"
                  [ngClass]="{
                    'validation-error-border':
                      (formControl.actualTimeOut.invalid || !formControl.actualTimeOut.value || !actualDateTimeValidator) &&
                      createVisitForm.controls.staffPartnerVisitStatus.value === staffStatus[4].value,
                    'permission-readonly': !canEditVisitFields()
                  }"
                  [required]="createVisitForm.controls.staffPartnerVisitStatus.value === staffStatus[4].value"
                  autocapitalize="on"
                >
                  <ion-icon name="time-outline" class="icon-size" slot="start"></ion-icon>
                </ion-input>
                <ion-modal trigger="actualTimeOut" id="time-modal" mode="ios">
                  <ng-template>
                    <ion-datetime
                      #actualTimeOutSelection
                      presentation="time"
                      hourCycle="h12"
                      show-default-buttons="true"
                      (ionChange)="updateActualTime(visitScheduleConstants.actualTimeOut, actualTimeOutSelection.value)"
                      id="actual-time-out-picker"
                      mode="ios"
                      color="blumine"
                      [value]="fetchActualDateTimeOut"
                      cancelText="{{ 'BUTTONS.CANCEL_UPPER' | translate }}"
                      doneText="{{ 'BUTTONS.DONE_UPPER' | translate }}"
                    >
                    </ion-datetime>
                  </ng-template>
                </ion-modal>
                <p
                  class="validation-error-text"
                  *ngIf="
                    createVisitForm.controls.staffPartnerVisitStatus.value === staffStatus[4].value &&
                    !actualDateTimeValidator &&
                    actualDateTimeFilled
                  "
                >
                  {{ 'VALIDATION_MESSAGES.ACTUAL_END_TIME_GREATER' | translate }}
                </p>
              </ion-col>
              <ion-col
                size="1"
                *ngIf="visitData?.timeOutLatLong"
                class="show-icon-center"
                (click)="showGoogleMapWithMarker(visitData?.timeOutLatLong)"
              >
                <ion-icon size="large" name="location"></ion-icon>
              </ion-col>
            </ion-row>
            <ion-row class="row" *ngIf="visitData?.timeOutAddress">
              <ion-col size="5"></ion-col>
              <ion-col size="7">
                <ion-textarea
                  [value]="visitData?.timeOutAddress"
                  class="common-input set-disable-input"
                  id="visit-time-out-address"
                  readonly="true"
                  [rows]="dynamicRows"
                >
                </ion-textarea>
              </ion-col>
            </ion-row>
            
            <!-- Total Mileage Field -->
            <ion-row class="row" *ngIf="createVisitForm.controls.staffPartnerVisitStatus.value === staffStatus[4].value && enableTotalMileageField">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.TOTAL_MILEAGE' | translate }}*</ion-label>
              </ion-col>
              <ion-col size="7">
                <ion-input
                  appNumericOnly
                  maxlength="5"
                  formControlName="totalMileage"
                  id="totalMileage"
                  class="common-input"
                  placeholder="Enter mileage"
                  [readonly]="!canEditVisitFields()"
                  [ngClass]="{
                    'validation-error-border':
                      (formControl.totalMileage.invalid || !formControl.totalMileage.value) &&
                      createVisitForm.controls.staffPartnerVisitStatus.value === staffStatus[4].value &&
                      enableTotalMileageField,
                    'permission-readonly': !canEditVisitFields()
                  }"
                  [required]="createVisitForm.controls.staffPartnerVisitStatus.value === staffStatus[4].value && enableTotalMileageField"
                  autocapitalize="on"
                >
                </ion-input>
              </ion-col>
            </ion-row>

            <!-- Total Drive Time Field -->
            <ion-row class="row" *ngIf="createVisitForm.controls.staffPartnerVisitStatus.value === staffStatus[4].value && enableDriveTimeField">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.TOTAL_DRIVE_TIME' | translate }}*</ion-label>
              </ion-col>
              <ion-col size="7">
                <div class="drive-time-container" style="display: flex;">
                  <ion-select
                    formControlName="driveTimeHours"
                    interface="popover"
                    class="drive-time-select common-input"
                    placeholder="HH"
                    (ionChange)="updateDriveTime()"
                    [disabled]="!canEditVisitFields()"
                    [ngClass]="{
                      'validation-error-border':
                        createVisitForm.controls.staffPartnerVisitStatus.value === staffStatus[4].value &&
                        createVisitForm.controls.totalDriveTime.value === '' &&
                        enableDriveTimeField,
                      'permission-disabled': !canEditVisitFields()
                    }"
                  >
                    <ion-select-option *ngFor="let hour of driveTimeHours" [value]="hour">{{ hour }}</ion-select-option>
                  </ion-select>
                  
                  <ion-select
                    formControlName="driveTimeMinutes"
                    interface="popover"
                    class="drive-time-select common-input"
                    placeholder="MM"
                    (ionChange)="updateDriveTime()"
                    [disabled]="!canEditVisitFields()"
                    [ngClass]="{
                      'validation-error-border':
                        createVisitForm.controls.staffPartnerVisitStatus.value === staffStatus[4].value &&
                        createVisitForm.controls.totalDriveTime.value === '' &&
                        enableDriveTimeField,
                      'permission-disabled': !canEditVisitFields()
                    }"
                  >
                    <ion-select-option *ngFor="let minute of driveTimeMinutes" [value]="minute">{{ minute }}</ion-select-option>
                  </ion-select>
                </div>
              </ion-col>
            </ion-row>
            <ion-row class="row">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.PATIENT_VISIT_STATUS' | translate }}</ion-label>
              </ion-col>
              <ion-col size="7">
                <ion-select
                  name="patientVisitStatus"
                  formControlName="patientVisitStatus"
                  class="common-input"
                  mode="md"
                  id="patient-visit-status"
                  (ionChange)="removeValidationPatientStatusChange()"
                  [interfaceOptions]="{ header: 'LABELS.PATIENT_VISIT_STATUS' | translate }"
                >
                  <ion-select-option
                    *ngFor="let option of patientVisitStatusData; let i = index"
                    [value]="option.value"
                    id="patient-visit-status-{{ i }}"
                    [disabled]="option.disabled"
                  >
                    {{ option.text | translate }}
                  </ion-select-option>
                </ion-select>
              </ion-col>
            </ion-row>
            <ion-row
              class="row"
              *ngIf="
                createVisitForm.controls.patientVisitStatus.value === patientVisitStatusData[3].value ||
                createVisitForm.controls.patientVisitStatus.value === patientVisitStatusData[2].value
              "
            >
              <ion-col size="5" *ngIf="createVisitForm.controls.patientVisitStatus.value === patientVisitStatusData[3].value">
                <ion-label>{{ 'LABELS.REASON_FOR_CANCEL' | translate }}*</ion-label>
              </ion-col>
              <ion-col size="5" *ngIf="createVisitForm.controls.patientVisitStatus.value === patientVisitStatusData[2].value">
                <ion-label>{{ 'LABELS.REASON_FOR_PATIENT_DECLINE' | translate }}*</ion-label>
              </ion-col>
              <ion-col size="7">
                <ion-input
                  formControlName="patientReasonForCancel"
                  type="text"
                  class="common-input"
                  [ngClass]="{
                    'validation-error-border':
                      formControl.patientReasonForCancel.invalid &&
                      (createVisitForm.controls.patientVisitStatus.value === patientVisitStatusData[3].value ||
                        createVisitForm.controls.patientVisitStatus.value === patientVisitStatusData[2].value)
                  }"
                  [required]="
                    createVisitForm.controls.patientVisitStatus.value === patientVisitStatusData[3].value ||
                    createVisitForm.controls.patientVisitStatus.value === patientVisitStatusData[2].value
                  "
                  id="txt-patient-decline-reason"
                  autocapitalize="on"
                >
                </ion-input>
                <p
                  class="validation-error-text"
                  *ngIf="
                    formControl.patientReasonForCancel.invalid &&
                    createVisitForm.controls.patientVisitStatus.value === patientVisitStatusData[3].value
                  "
                >
                  {{ 'VALIDATION_MESSAGES.VISIT_CANCEL_REASON' | translate }}
                </p>
                <p
                  class="validation-error-text"
                  *ngIf="
                    formControl.patientReasonForCancel.invalid &&
                    createVisitForm.controls.patientVisitStatus.value === patientVisitStatusData[2].value
                  "
                >
                  {{ 'VALIDATION_MESSAGES.ENTER_REASON_FOR_PATIENT_DECLINE' | translate }}
                </p>
              </ion-col>
            </ion-row>
          </ion-list>
        </ion-accordion>
        <ion-accordion id="additional-details-accordion" value="additional-details-accordion">
          <ion-item slot="header" class="accordion-head" [ngClass]="{ 'validation-error-header': hasAdditionalDetailsErrors() }">
            {{ 'LABELS.ADDITIONAL_DETAILS' | translate }}
          </ion-item>
          <ion-list slot="content" class="content">
            <ion-row class="row">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.ADDITIONAL_DETAILS' | translate }}</ion-label>
              </ion-col>
              <ion-col size="7">
                <ion-textarea
                  formControlName="additionalDetails"
                  type="text"
                  class="common-input"
                  rows="4"
                  id="additional-details"
                  autocapitalize="on"
                >
                </ion-textarea>
              </ion-col>
            </ion-row>
            <ion-row class="row">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.ATTACH_FILE' | translate }}</ion-label>
              </ion-col>
              <ion-col size="7">
                <div class="fileUpload btn">
                  <span class="uploadbtn">{{ 'LABELS.ATTACHMENT' | translate }}</span>
                  <ion-input
                    type="file"
                    #docInput
                    class="upload"
                    formControlName="fileUpload"
                    accept=".pdf ,.doc, .docx, .word, .xl, .xls, .xlsx, .odt, .jpg,.JPG, .jpeg, .JPEG, .png, .PNG"
                    (change)="selectFile($event)"
                    multiple
                    id="file-upload"
                    (click)="docInput.value = null"
                  >
                  </ion-input>
                </div>
                <p class="validation-error-text" *ngIf="fileError">
                  {{ 'ERROR_MESSAGES.INVALID_FILE_FORMAT' | translate }}
                </p>
                <p class="validation-error-text" *ngIf="fileSizeError">
                  {{ fileSizeErrorMessage | translate }}
                </p>
                <ion-row class="row" class="chlip-top">
                  <ion-chip outline color="medium" *ngFor="let item of visitFiles; let i = index">
                    <ion-icon name="document-outline" class="chlip-icon"></ion-icon>
                    <ion-label class="wrap-text chlip-text" id="attach-file-{{ i }}">{{ item.name }}</ion-label>
                    <ion-icon name="close-circle" (click)="removeFile(item)" id="remove-file-{{ i }}" class="chlip-icon"> </ion-icon>
                  </ion-chip>
                </ion-row>
                <ion-row class="row" class="chlip-top">
                  <ion-chip outline color="medium" *ngFor="let item of savedFiles; let i = index">
                    <ion-icon name="document-outline" class="chlip-icon"></ion-icon>
                    <ion-label class="wrap-text chlip-text" id="attach-uploaded-file-{{ i }}">
                      <a (click)="presentImageViewerModal(item, i)" id="attach-uploaded-file-link-{{ i }}" href="javascript: void(0);">
                        {{ item.formattedFileName }}
                      </a>
                    </ion-label>
                    <ion-icon name="close-circle" (click)="removeUploadedFile(i)" id="remove-uploaded-file-{{ i }}" class="chlip-icon"></ion-icon>
                  </ion-chip>
                </ion-row>
              </ion-col>
            </ion-row>
            <ion-row class="row">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.SEND_PATIENT_NOTIFICATION' | translate }}</ion-label>
              </ion-col>
              <ion-col size="7">
                <ion-toggle color="success" formControlName="sendPatientNotification" mode="ios" id="send-patient-notification"></ion-toggle>
              </ion-col>
            </ion-row>
            <ion-row class="row" *ngIf="createVisitForm.value.sendPatientNotification">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.REQUEST_PATIENT_CONFIRMATION' | translate }}</ion-label>
              </ion-col>
              <ion-col size="7">
                <ion-toggle color="success" formControlName="requestPatientConfirmation" mode="ios" id="send-patient-confirmation"></ion-toggle>
              </ion-col>
            </ion-row>
            <ion-row class="row" *ngIf="createVisitForm.value.requestPatientConfirmation && createVisitForm.controls.staffPartnerVisitStatus.value !== staffStatus[2].value">
              <ion-col size="7" offset="5">
                <ion-radio-group formControlName="confirmationBeforeStaffConfirm">
                  <ion-item>
                    <ion-label>{{ 'LABELS.NOTIFY_AFTER_STAFF_CONFIRMATION' | translate }}</ion-label>
                    <ion-radio color="success" slot="start" value=0 checked></ion-radio>
                  </ion-item>
                  <ion-item>
                    <ion-label>{{ 'LABELS.NOTIFY_BEFORE_STAFF_CONFIRMATION' | translate }}</ion-label>
                    <ion-radio color="success" slot="start" value=1></ion-radio>
                  </ion-item>
                </ion-radio-group>
              </ion-col>
            </ion-row>
            <ion-row class="row" *ngIf="createVisitForm.value.sendPatientNotification">
              <ion-col size="5">
                <ion-label>{{ 'LABELS.SET_PATIENT_REMINDER' | translate }}</ion-label>
              </ion-col>
              <ion-col size="7">
                <ion-toggle color="success" formControlName="setPatientReminder" mode="ios" id="send-patient-reminder"></ion-toggle>
              </ion-col>
            </ion-row>
          </ion-list>
        </ion-accordion>
      </ion-accordion-group>
    </form>
    <div class="button-group">
      <ion-button
        *ngIf="!visitKey"
        expand="block"
        color="de-york"
        class="ion-text-capitalize"
        (click)="handleAction('save')"
        [disabled]="isReviewCompleted"
        id="btn-save"
      >
        {{ 'BUTTONS.SEND' | translate }}
      </ion-button>
      <ion-button
        *ngIf="visitKey"
        expand="block"
        color="de-york"
        class="ion-text-capitalize"
        (click)="handleAction('update')"
        [disabled]="!shouldAllowVisitEditing()"
        id="btn-update"
      >
        {{ 'BUTTONS.UPDATE' | translate }}
      </ion-button>
      <ion-button
        expand="block"
        class="btn-align ion-text-capitalize"
        (click)="handleAction('save-as-draft')"
        [disabled]="!shouldAllowVisitEditing()"
        id="btn-save-as-draft"
        *ngIf="!visitKey || checkDraftVisit"
      >
        {{ 'BUTTONS.SAVE_AS_DRAFT' | translate }}
      </ion-button>
      <ion-button expand="block" class="btn-align ion-text-capitalize" (click)="cancel()" id="btn-cancel">
        {{ 'BUTTONS.CANCEL' | translate }}
      </ion-button>
    </div>
  </div>
</ion-content>
<app-footer></app-footer>
