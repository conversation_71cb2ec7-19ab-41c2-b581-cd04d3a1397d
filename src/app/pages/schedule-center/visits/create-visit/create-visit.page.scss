.create-visit {
    top: 4%;
    font-size: 14px;

    .permission-readonly {
        background-color: #f5f5f5 !important;
        color: #666 !important;
        cursor: not-allowed !important;
        opacity: 0.7;

        &::placeholder {
            color: #999 !important;
        }
    }

    .permission-disabled {
        background-color: #f5f5f5 !important;
        color: #666 !important;
        opacity: 0.6;
        pointer-events: none;

        ion-select-option {
            color: #999 !important;
        }
    }

    .permission-denied-section {
        background-color: #ffebee !important;
        border: 1px solid #f44336 !important;
        border-radius: 4px;
        padding: 8px;
        opacity: 0.7;

        ion-label {
            color: #d32f2f !important;
        }
    }

    .permission-allowed-section {
        background-color: #e8f5e8 !important;
        border: 1px solid #4caf50 !important;
        border-radius: 4px;
        padding: 8px;
    }

    .permission-debug {
        background: #f0f0f0;
        border: 1px solid #ccc;
        border-radius: 5px;
        padding: 10px;
        margin: 10px 0;
        font-size: 12px;

        h4 {
            margin: 0 0 8px 0;
            color: #333;
        }

        p {
            margin: 2px 0;

            strong {
                color: #555;
            }
        }

        .permission-status {
            &.allowed {
                color: #4caf50;
                font-weight: bold;
            }

            &.denied {
                color: #f44336;
                font-weight: bold;
            }
        }
    }

    .accordion-content {
        margin-bottom: 25%;
    }

    .content {
        margin-left: 2%;
        margin-right: 2%;
    }

    .common-flag-sec {
        padding-top: 8px;
    }

    .sc-ion-textarea-md-h {
        --padding-top: 0px;
        --padding-end: 0;
        --padding-bottom: 0px;
    }

    .disable-filter {
        pointer-events: none;
        opacity: 0.8;
    }

    .accordion-head {
        font-weight: 550;
    }

    ion-label {
        font-size: 14px;
    }

    .custom-placeholder {
        display: inline-block;
        opacity: 0.5;
        margin-top: 6px;
    }

    .common-flag-sec .arrow-img {
        margin-left: 40px;
    }

    .common-flag-sec .flag-img {
        font-size: 20px;
        margin-left: 9px;
        margin-top: 8px;
    }

    .auto-resize {
        height: 41px;
        min-height: 41px;
    }

    .validation-error-text {
        font-size: 12px;
        color: #ea4335;
    }

    .validation-error-border {
        border-color: #ea4335;
    }

    .validation-error-header {
        color: #ea4335;
    }

    ion-col.flag-col {
        flex-grow: 0;
    }

    .btn-sm {
        font-size: 12px;
    }

    .common-input[disabled] {
        opacity: 0.6;
        background: #f5f6f9;
    }

    ion-button[disabled] {
        opacity: 0.6;
        background: transparent;
    }

    ion-input span {
        margin-left: 8px;
        margin-top: 0px;
    }

    .icon-size {
        font-size: 20px;
        margin-left: 5px;
        color: var(--ion-color-skin-secondary-bright);
    }

    .fileUpload {
        position: relative;
        overflow: hidden;
    }

    .fileUpload ion-input.upload {
        position: absolute;
        top: 0;
        right: 0;
        margin: 0;
        padding: 0;
        font-size: 20px;
        cursor: pointer;
        opacity: 0;
        filter: alpha(opacity=0);
    }

    .uploadbtn {
        color: #ffffff;
        width: 120px;
        height: 40px;
        font-size: 1em;
        font-weight: 501;
        line-height: 2.6em;
        text-align: center;
        display: inline-block;
        transition: 150ms;
        background: url(/assets/images/clip.png) no-repeat scroll left center #2f96b4;
        background-position-x: left;
        background-position-y: center;
        background-position-x: left;
        background-position-y: center;
        background-position: 5px;
        border: 1px solid rgba(0, 0, 0, 0.13);
        padding-left: 10px;
    }

    .disable {
        opacity: 0.8;
        background: #d0d0d059;
    }

    .button-group {
        margin-top: 25px;
        margin-left: 8px;
        margin-right: 8px;
    }

    .btn-align {
        margin-top: 8px;
    }

    .trash-icon {
        font-size: 28px;
    }

    .modify-content {
        margin-bottom: 3%;
        margin-top: 4%;
        .modify-row {
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;
            justify-content: space-between;
            align-items: center;
        }
    }

    ion-select {
        --placeholder-opacity: 0.5;
    }

    .trash-disabled {
        opacity: 0.4;
    }
}

ion-content {
    --ion-background-color: white;
}

ion-item {
    --ion-background-color: #fafafa;
}

::ng-deep .sc-ion-textarea-md {
    overflow: hidden !important;
    height: auto !important;
    min-height: 25px !important;
}

ion-datetime {
    color: black;
}

#custom-modal {
    --height: 350px;
    --width: 300px;
}

#time-modal {
    --height: 250px;
    --width: 240px;
}

ion-select::part(icon) {
    color: var(--ion-color-skin-secondary-bright);
    position: absolute;
    right: 10px;
    opacity: inherit;
    font-size: 12px;
}

.wrap-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chlip-text {
    font-size: 12px !important;
    width: 80%;
}

.chlip-top {
    margin-top: 10px;
}

.button-group {
    #btn-cancel {
        --background: var(--ion-color-button-cancel);
        --color: #555;
    }

    #btn-save-as-draft {
        --background: var(--ion-color-fountain-blue);
    }
}

::ng-deep {
    .native-input[disabled].sc-ion-input-md {
        opacity: 0.8;
    }

    .native-input[disabled].sc-ion-input-md:not(.cloned-input) {
        opacity: 0.8;
    }

    .alert-radio-group.sc-ion-alert-md {
        max-height: 450px;
    }

    .sc-ion-alert-md-h {
        --max-width: 300px;
        --min-width: 280px;
    }

    .sc-ion-alert-ios-h {
        --min-width: 80%;
    }

    .button-clear {
        --color: #000;
        --background-hover: transparent;
    }

    .alert-radio-icon.sc-ion-alert-md {
        left: 20px;
    }

    .alert-radio-label.sc-ion-alert-md {
        padding-left: 29px;
        font-size: 14px;
        padding-inline-end: 20px;
    }

    .select-disabled {
        opacity: 0.8;
    }
}

[aria-disabled="true"],
.set-disable-input {
    pointer-events: none;
    background-color: #d0d0d059;
}

.form-field-right {
    float: right;
    font-size: 14px;
    cursor: pointer;
    text-align: left;
    padding: 5px 10px;
}

.disable-cursor {
    caret-color: transparent;
}

.disabled-color {
    background-color: #d0d0d059;
}

.disabled {
    pointer-events: none;
    cursor: default;
    opacity: 0.6;
    color: #828282 !important;
}

#multiple-date {
    padding: 0 10px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.delete-series-modal {
    ion-radio {
        width: 25px;
        height: 25px;
    }

    ion-radio::part(container) {
        border-radius: 50%;
        border: 2px solid #ddd;
    }

    ion-radio::part(mark) {
        background: none;
        transition: none;
        transform: none;
        border-radius: 0;
    }

    ion-radio.radio-checked::part(container) {
        background: var(--ion-color-primary);
        border-color: transparent;
    }

    ion-radio.radio-checked::part(mark) {
        width: 6px;
        height: 10px;

        border-width: 0px 2px 2px 0px;
        border-style: solid;
        border-color: #fff;

        transform: rotate(45deg);
    }
}
