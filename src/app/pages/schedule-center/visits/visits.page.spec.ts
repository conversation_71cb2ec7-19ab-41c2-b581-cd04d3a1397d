import { ComponentFixture, fakeAsync, TestBed } from '@angular/core/testing';
import { IonicModule, ModalController, PopoverController } from '@ionic/angular';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { TranslateModule } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { Router, RouterModule } from '@angular/router';
import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { UntypedFormBuilder } from '@angular/forms';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { TestConstants } from 'src/app/constants/test-constants';
import { of, throwError } from 'rxjs';
import { VisitScheduleService } from 'src/app/services/visit-schedule-service/visit-schedule.service';
import { PersistentService } from 'src/app/services/persistent-service/persistent.service';
import { SQLite } from '@ionic-native/sqlite/ngx';
import { NativeStorage } from '@ionic-native/native-storage/ngx';
import { TimeZones } from 'src/app/interfaces/schedule-center';
import { VisitsPage } from './visits.page';

// Mock timezone data for testing

// Mock timezone data for testing
const mockTimeZones: TimeZones = [
  { offset: '-480', city: 'America/Los_Angeles', name: 'Pacific Time', isDst: '0', current_offset: '-480' },
  { offset: '-300', city: 'America/New_York', name: 'Eastern Time', isDst: '0', current_offset: '-300' },
  { offset: '330', city: 'Asia/Kolkata', name: 'Indian Standard Time', isDst: '0', current_offset: '330' },
  { offset: '600', city: 'Australia/Sydney', name: 'Australian Eastern Standard Time', isDst: '0', current_offset: '600' }
];

describe('VisitsPage', () => {
  let component: VisitsPage;
  let fixture: ComponentFixture<VisitsPage>;
  let sharedService: SharedService;
  let router: Router;
  const modalSpy = jasmine.createSpyObj('Modal', ['onDidDismiss', 'present']);
  let modalController: ModalController;
  let popoverController: PopoverController;
  const { popoverSpy } = TestConstants;
  let scheduleCenterService: VisitScheduleService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [VisitsPage],
      imports: [IonicModule.forRoot(), HttpClientModule, HttpClientTestingModule, TranslateModule.forRoot(), RouterModule.forRoot([])],
      providers: [
        SharedService,
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined },
        UntypedFormBuilder,
        PopoverController,
        ModalController,
        PersistentService,
        SQLite,
        NativeStorage
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    modalSpy.onDidDismiss.and.resolveTo({ data: 'fakeData' });
    popoverController = TestBed.inject(PopoverController);
    router = TestBed.inject(Router);
    spyOn(router, 'navigate').and.stub();
    sharedService = TestBed.inject(SharedService);
    Object.defineProperty(sharedService, 'userData', { value: TestConstants.userData });
    scheduleCenterService = TestBed.inject(VisitScheduleService);
    fixture = TestBed.createComponent(VisitsPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  afterEach(() => {
    if (fixture.nativeElement && 'remove' in fixture.nativeElement) {
      (fixture.nativeElement as HTMLElement).remove();
    }
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call getVisits function', () => {
    const mockResponse = {
      data: [
        {
          id: 1,
          start: new Date('2022-01-01T00:00:00'),
          end: new Date('2022-01-01T01:00:00'),
          siteName: 'Test Site',
          staffVisitStatus: 'Confirmed',
          status: 'Confirmed',
          patientVisitStatus: 'Confirmed',
          visitStatus: 'Confirmed',
          visitKey: '123',
          staffName: 'John Smith',
          eventVisitTitle: 'Test Visit',
          patientName: 'Jane Doe',
          title: 'Test Visit',
          color: '#f00'
        }
      ]
    };
    spyOn(scheduleCenterService, 'getVisits').and.returnValue(of(mockResponse));
    component.getVisits();
    expect(component.getVisits).toBeTruthy();
  });
  it('should call getVisits function when error case', fakeAsync(() => {
    spyOn(scheduleCenterService, 'getVisits').and.returnValue(throwError({}));
    component.getVisits();
    expect(component.getVisits).toBeTruthy();
  }));
  it('should calendarInit function', () => {
    const mockParam = {
      operation: 'dates-set',
      param: {
        startIntervalTime: '10:00:00',
        endIntervalTime: '11:00:00'
      }
    };
    component.calendarInit(mockParam);
    expect(component.calendarInit).toBeTruthy();
  });
  it('calendarInit function check event click', () => {
    spyOn(popoverController, 'create').and.returnValue(Promise.resolve(popoverSpy.present));

    const mockParam = {
      operation: 'event-click',
      param: {
        startIntervalTime: '10:00:00',
        endIntervalTime: '11:00:00'
      }
    };
    component.calendarInit(mockParam);
    expect(component.calendarInit).toBeTruthy();
  });

  it('should call goToVisit function', () => {
    component.goToVisit();
    expect(component.goToVisit).toBeTruthy();
  });

  it('should call ionViewWillLeave', () => {
    component.ionViewWillLeave();
    expect(component.ionViewWillLeave).toBeTruthy();
  });

  it('should call ionViewWillEnter', () => {
    component.pageRender = true;
    component.ionViewWillEnter();
    expect(component.ionViewWillEnter).toBeTruthy();
  });

  it('should call getFilterControlSelected', () => {
    component.getFilterControlSelected();
    expect(component.getFilterControlSelected).toBeTruthy();
  });

  it('should return a string of comma-separated id values for selected rows', () => {
    const selectedData = [
      { id: 1, selected: true },
      { id: 2, selected: false },
      { id: 3, selected: true }
    ];
    component.checkSeleced(selectedData);
    expect(component.checkSeleced).toBeTruthy();
  });

  it('should execute showFilter function', () => {
    component.showFilter();
    expect(component.showFilter).toBeTruthy();
  });

  it('should call userChange with MY Visit', () => {
    component.userChange('myVisit');
    expect(component.userChange).toBeDefined();
  });

  it('should call userChange with All Visits', () => {
    component.userChange('ALL_VISITS');
    expect(component.userChange).toBeDefined();
  });

  it('should call checkPersistentFiltersApplied and set filter data correctly', () => {
    const filterData = {
      mySites: [{ id: 1, selected: true }],
      clinicianId: '123',
      staffClinicianName: 'John Doe',
      types: [{ id: 1, selected: true }],
      title: 'Test Title',
      schedules: [{ id: 1, selected: true }],
      patientID: '456',
      patientName: 'Jane Doe',
      visitTypeLocation: { id: 1 },
      visitChair: { id: 1 }
    };
    spyOn(component['persistentService'], 'getPersistentData').and.returnValue(filterData);
    spyOn(component, 'getFilterData').and.callThrough();
    spyOn(component, 'setFilterData').and.callThrough();
    spyOn(component, 'setVisitsParam').and.callThrough();
    spyOn(component, 'checkForFilteredFields').and.callThrough();

    component.checkPersistentFiltersApplied();

    expect(component.getFilterData).toHaveBeenCalled();
    expect(component.setFilterData).toHaveBeenCalledWith(filterData);
    expect(component.setVisitsParam).toHaveBeenCalled();
    expect(component.checkForFilteredFields).toHaveBeenCalledWith(filterData);
    expect(component.showFilterNotification).toBe(true);
  });

  it('should call checkPersistentFiltersApplied and not set filter data if no filters applied', () => {
    const filterData = {};
    spyOn(component['persistentService'], 'getPersistentData').and.returnValue(filterData);
    spyOn(component, 'getFilterData').and.callThrough();
    spyOn(component, 'setFilterData').and.callThrough();
    spyOn(component, 'setVisitsParam').and.callThrough();
    spyOn(component, 'checkForFilteredFields').and.callThrough();

    component.checkPersistentFiltersApplied();

    expect(component.getFilterData).toHaveBeenCalled();
    expect(component.setFilterData).toHaveBeenCalledWith(filterData);
    expect(component.setVisitsParam).toHaveBeenCalled();
    expect(component.checkForFilteredFields).toHaveBeenCalledWith(filterData);
    expect(component.showFilterNotification).toBe(false);
  });

  describe('getClinicianNames', () => {
    it('should return joined display names if visitStaffData is present and non-empty', () => {
      const event = {
        visitStaffData: [{ displayName: 'Alice' }, { displayName: 'Bob' }, { displayName: '' }, { displayName: null }],
        staffName: 'Fallback Name'
      };
      expect(component.getClinicianNames(event)).toBe('Alice, Bob');
    });

    it('should return staffName if visitStaffData is empty', () => {
      const event = {
        visitStaffData: [],
        staffName: 'Fallback Name'
      };
      expect(component.getClinicianNames(event)).toBe('Fallback Name');
    });

    it('should return staffName if visitStaffData is not an array', () => {
      const event = {
        visitStaffData: null,
        staffName: 'Fallback Name'
      };
      expect(component.getClinicianNames(event)).toBe('Fallback Name');
    });

    it('should return staffName if visitStaffData is undefined', () => {
      const event = {
        staffName: 'Fallback Name'
      };
      expect(component.getClinicianNames(event)).toBe('Fallback Name');
    });
  });

  describe('Device Timezone Detection', () => {
    let persistentService: PersistentService;

    beforeEach(() => {
      persistentService = TestBed.inject(PersistentService);
    });

    describe('Constructor timezone initialization', () => {
      it('should use persistent timezone when available', () => {
        const persistentTimezone = 'Asia/Kolkata';
        spyOn(persistentService, 'getPersistentData').and.returnValue(persistentTimezone);

        // Create new component instance to test constructor
        const newFixture = TestBed.createComponent(VisitsPage);
        const newComponent = newFixture.componentInstance;

        expect(newComponent.visitsTimezone).toBe(persistentTimezone);
      });

      it('should use user default timezone when persistent data is not available', () => {
        const userDefaultTimezone = 'Australia/Sydney';
        spyOn(persistentService, 'getPersistentData').and.returnValue(null);

        // Mock user data with default timezone
        const mockUserData = { ...TestConstants.userData, userVisitDefaultTimezone: userDefaultTimezone };
        Object.defineProperty(sharedService, 'userData', { value: mockUserData, writable: true });

        // Create new component instance to test constructor
        const newFixture = TestBed.createComponent(VisitsPage);
        const newComponent = newFixture.componentInstance;

        expect(newComponent.visitsTimezone).toBe(userDefaultTimezone);
      });

      it('should use site timezone when neither persistent nor user default is available', () => {
        const siteTimezone = 'America/Los_Angeles';

        // Create a fresh TestBed configuration for this test
        TestBed.resetTestingModule();
        TestBed.configureTestingModule({
          declarations: [VisitsPage],
          imports: [IonicModule.forRoot(), HttpClientModule, HttpClientTestingModule, TranslateModule.forRoot(), RouterModule.forRoot([])],
          providers: [
            SharedService,
            NgxPermissionsService,
            NgxPermissionsStore,
            Idle,
            IdleExpiry,
            Keepalive,
            { provide: USE_PERMISSIONS_STORE, useValue: undefined },
            UntypedFormBuilder,
            PopoverController,
            ModalController,
            PersistentService,
            SQLite,
            NativeStorage
          ],
          schemas: [CUSTOM_ELEMENTS_SCHEMA]
        }).compileComponents();

        const testPersistentService = TestBed.inject(PersistentService);
        const testSharedService = TestBed.inject(SharedService);

        spyOn(testPersistentService, 'getPersistentData').and.returnValue(null);
        spyOn(testSharedService, 'getSiteConfigValue').and.returnValue(siteTimezone);
        spyOn(testSharedService, 'getConfigValue').and.returnValue(siteTimezone);

        // Mock user data with no default timezone
        const mockUserData = { ...TestConstants.userData, userVisitDefaultTimezone: null };
        Object.defineProperty(testSharedService, 'userData', { value: mockUserData, writable: true });

        // Create new component instance to test constructor
        const newFixture = TestBed.createComponent(VisitsPage);
        const newComponent = newFixture.componentInstance;

        expect(newComponent.visitsTimezone).toBe(siteTimezone);
      });
    });

    describe('loadTimeZones', () => {
      it('should load timezones and set device timezone when no persistent data exists', () => {
        // Mock persistent service to return no data
        spyOn(persistentService, 'getPersistentData').and.returnValue(null);

        // Mock user data with no default timezone
        component.userData = { ...TestConstants.userData, userVisitDefaultTimezone: null };

        // Mock getDeviceTimezone to return a specific timezone
        spyOn(component as any, 'getDeviceTimezone').and.returnValue('America/New_York');

        // Mock sharedService.getAllTimeZones to return mock data
        spyOn(sharedService, 'getAllTimeZones').and.returnValue(of(mockTimeZones));

        component.loadTimeZones();

        expect(component.timeZones).toEqual(mockTimeZones);
        expect(component.visitsTimezone).toBe('America/New_York');
      });

      it('should not override persistent timezone data with device timezone', () => {
        const persistentTimezone = 'Asia/Kolkata';

        // Mock persistent service to return existing data
        spyOn(persistentService, 'getPersistentData').and.returnValue(persistentTimezone);

        // Mock sharedService.getAllTimeZones
        spyOn(sharedService, 'getAllTimeZones').and.returnValue(of(mockTimeZones));

        const originalTimezone = component.visitsTimezone;
        component.loadTimeZones();

        expect(component.visitsTimezone).toBe(originalTimezone);
      });

      it('should not override user default timezone with device timezone', () => {
        const userDefaultTimezone = 'Australia/Sydney';

        // Mock persistent service to return no data
        spyOn(persistentService, 'getPersistentData').and.returnValue(null);

        // Mock user data with default timezone
        component.userData = { ...TestConstants.userData, userVisitDefaultTimezone: userDefaultTimezone };

        // Mock sharedService.getAllTimeZones
        spyOn(sharedService, 'getAllTimeZones').and.returnValue(of(mockTimeZones));

        const originalTimezone = component.visitsTimezone;
        component.loadTimeZones();

        expect(component.visitsTimezone).toBe(originalTimezone);
      });

      it('should not set device timezone when device timezone detection fails', () => {
        // Mock persistent service to return no data
        spyOn(persistentService, 'getPersistentData').and.returnValue(null);
        component.userData = { ...TestConstants.userData, userVisitDefaultTimezone: null };

        // Mock getDeviceTimezone to return null (detection failed)
        spyOn(component as any, 'getDeviceTimezone').and.returnValue(null);

        // Mock sharedService.getAllTimeZones
        spyOn(sharedService, 'getAllTimeZones').and.returnValue(of(mockTimeZones));

        const originalTimezone = component.visitsTimezone;
        component.loadTimeZones();

        expect(component.visitsTimezone).toBe(originalTimezone);
      });

      it('should not set device timezone when device timezone detection returns empty string', () => {
        // Mock persistent service to return no data
        spyOn(persistentService, 'getPersistentData').and.returnValue(null);
        component.userData = { ...TestConstants.userData, userVisitDefaultTimezone: null };

        // Mock getDeviceTimezone to return empty string
        spyOn(component as any, 'getDeviceTimezone').and.returnValue('');

        // Mock sharedService.getAllTimeZones
        spyOn(sharedService, 'getAllTimeZones').and.returnValue(of(mockTimeZones));

        const originalTimezone = component.visitsTimezone;
        component.loadTimeZones();

        expect(component.visitsTimezone).toBe(originalTimezone);
      });

      it('should handle error when loading timezones fails', () => {
        spyOn(sharedService, 'getAllTimeZones').and.returnValue(throwError(() => new Error('Network error')));

        component.loadTimeZones();

        expect(component.timeZones).toEqual([]);
      });

      it('should trigger change detection on successful timezone load', () => {
        spyOn(component['cdr'], 'detectChanges');
        spyOn(sharedService, 'getAllTimeZones').and.returnValue(of(mockTimeZones));

        component.loadTimeZones();

        expect(component['cdr'].detectChanges).toHaveBeenCalled();
      });

      it('should trigger change detection on timezone load error', () => {
        spyOn(component['cdr'], 'detectChanges');
        spyOn(sharedService, 'getAllTimeZones').and.returnValue(throwError(() => new Error('Network error')));

        component.loadTimeZones();

        expect(component['cdr'].detectChanges).toHaveBeenCalled();
      });
    });

    describe('getDeviceTimezone', () => {
      it('should return matching timezone city when device timezone is found in list', () => {
        // Mock Intl.DateTimeFormat
        const mockIntl = {
          DateTimeFormat: jasmine.createSpy('DateTimeFormat').and.returnValue({
            resolvedOptions: jasmine.createSpy('resolvedOptions').and.returnValue({ timeZone: 'America/Los_Angeles' })
          })
        };
        Object.defineProperty(global, 'Intl', { value: mockIntl, writable: true });

        // Mock getCurrentOffset to return matching offset
        spyOn(component as any, 'getCurrentOffset').and.returnValue(-480);

        component.timeZones = mockTimeZones;
        const result = component['getDeviceTimezone']();

        expect(result).toBe('America/Los_Angeles');
      });

      it('should return null when device timezone is not found in available timezones', () => {
        // Mock Intl.DateTimeFormat to return timezone not in our list
        const mockIntl = {
          DateTimeFormat: jasmine.createSpy('DateTimeFormat').and.returnValue({
            resolvedOptions: jasmine.createSpy('resolvedOptions').and.returnValue({ timeZone: 'Europe/London' })
          })
        };
        Object.defineProperty(global, 'Intl', { value: mockIntl, writable: true });

        // Mock getCurrentOffset to return different offsets for device vs available timezones
        spyOn(component as any, 'getCurrentOffset').and.callFake((tz: string) => {
          if (tz === 'Europe/London') return 0; // GMT offset for device timezone
          return -480; // Different offset for all available timezones
        });

        component.timeZones = mockTimeZones;
        const result = component['getDeviceTimezone']();

        expect(result).toBeNull();
      });

      it('should return null when Intl.DateTimeFormat throws error', () => {
        // Mock Intl.DateTimeFormat to throw error
        const mockIntl = {
          DateTimeFormat: jasmine.createSpy('DateTimeFormat').and.throwError('DateTimeFormat not supported')
        };
        Object.defineProperty(global, 'Intl', { value: mockIntl, writable: true });

        component.timeZones = mockTimeZones;
        const result = component['getDeviceTimezone']();

        expect(result).toBeNull();
      });

      it('should return null when timeZones is not loaded', () => {
        component.timeZones = null;
        const result = component['getDeviceTimezone']();

        expect(result).toBeNull();
      });

      it('should return null when timeZones is empty', () => {
        component.timeZones = [];
        const result = component['getDeviceTimezone']();

        expect(result).toBeNull();
      });
    });

    describe('onTimezoneChange', () => {
      it('should save timezone to persistent storage and reload visits', () => {
        spyOn(persistentService, 'setPersistentData');
        spyOn(component, 'getVisits');

        component.visitsTimezone = 'America/Los_Angeles';
        component.onTimezoneChange();

        expect(persistentService.setPersistentData).toHaveBeenCalledWith('visitsTimezone', 'America/Los_Angeles');
        expect(component.getVisits).toHaveBeenCalled();
      });
    });

    describe('Integration Tests', () => {
      it('should have device timezone detection methods available', () => {
        expect(typeof component['getDeviceTimezone']).toBe('function');
        expect(typeof component['getCurrentOffset']).toBe('function');
      });

      it('should handle timezone loading error gracefully', () => {
        spyOn(sharedService, 'getAllTimeZones').and.returnValue(throwError(() => new Error('Network error')));

        expect(() => component.loadTimeZones()).not.toThrow();
      });

      it('should handle empty timezone response', () => {
        spyOn(sharedService, 'getAllTimeZones').and.returnValue(of([]));
        spyOn(component['cdr'], 'detectChanges');

        component.loadTimeZones();

        // When empty array is returned, the condition fails and timeZones is not set
        expect(component['cdr'].detectChanges).not.toHaveBeenCalled();
      });

      it('should handle null timezone response', () => {
        spyOn(sharedService, 'getAllTimeZones').and.returnValue(of(null));
        spyOn(component['cdr'], 'detectChanges');

        component.loadTimeZones();

        expect(component['cdr'].detectChanges).not.toHaveBeenCalled();
      });

      it('should handle undefined timezone response', () => {
        spyOn(sharedService, 'getAllTimeZones').and.returnValue(of(undefined));
        spyOn(component['cdr'], 'detectChanges');

        component.loadTimeZones();

        expect(component['cdr'].detectChanges).not.toHaveBeenCalled();
      });
    });

    describe('Edge Cases', () => {
      it('should handle device timezone detection when timeZones is not loaded', () => {
        component.timeZones = null;
        const result = component['getDeviceTimezone']();
        expect(result).toBeNull();
      });

      it('should handle device timezone detection when timeZones is empty array', () => {
        component.timeZones = [];
        const result = component['getDeviceTimezone']();
        expect(result).toBeNull();
      });

      it('should handle loadTimeZones when device timezone is empty string', () => {
        spyOn(persistentService, 'getPersistentData').and.returnValue(null);
        component.userData = { ...TestConstants.userData, userVisitDefaultTimezone: null };
        spyOn(component as any, 'getDeviceTimezone').and.returnValue('');
        spyOn(sharedService, 'getAllTimeZones').and.returnValue(of(mockTimeZones));

        const originalTimezone = component.visitsTimezone;
        component.loadTimeZones();

        expect(component.visitsTimezone).toBe(originalTimezone);
      });

      it('should handle loadTimeZones when device timezone is whitespace', () => {
        spyOn(persistentService, 'getPersistentData').and.returnValue(null);
        component.userData = { ...TestConstants.userData, userVisitDefaultTimezone: null };
        spyOn(component as any, 'getDeviceTimezone').and.returnValue('   ');
        spyOn(sharedService, 'getAllTimeZones').and.returnValue(of(mockTimeZones));

        component.loadTimeZones();

        expect(component.visitsTimezone).toBe('   ');
      });
    });

    describe('updateVisitsDateRange', () => {
      it('should update visits start and end dates with timezone conversion', () => {
        const mockDateSelection = {
          startIntervalTime: '2024-01-01',
          endIntervalTime: '2024-01-31'
        };
        component.visitsTimezone = 'America/New_York';

        component.updateVisitsDateRange(mockDateSelection);

        expect(component.visitsParam.startDate).toBeDefined();
        expect(component.visitsParam.endDate).toBeDefined();
      });

      it('should handle null calendar date selection in onTimezoneChange', () => {
        component.calendarDateSelection = null;
        spyOn(component, 'updateVisitsDateRange');
        spyOn(component, 'getVisits');

        component.onTimezoneChange();

        expect(component.updateVisitsDateRange).not.toHaveBeenCalled();
        expect(component.getVisits).toHaveBeenCalled();
      });

      it('should call updateVisitsDateRange when calendar date selection exists in onTimezoneChange', () => {
        const mockDateSelection = {
          startIntervalTime: '2024-01-01',
          endIntervalTime: '2024-01-31'
        };
        component.calendarDateSelection = mockDateSelection;
        spyOn(component, 'updateVisitsDateRange');
        spyOn(component, 'getVisits');

        component.onTimezoneChange();

        expect(component.updateVisitsDateRange).toHaveBeenCalledWith(mockDateSelection);
        expect(component.getVisits).toHaveBeenCalled();
      });
    });
  });
});
