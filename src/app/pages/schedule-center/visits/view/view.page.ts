/* eslint-disable @typescript-eslint/no-explicit-any */
import { PageRoutes } from 'src/app/constants/page-routes';
import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { AlertController, ModalController } from '@ionic/angular';
import * as moment from 'moment';
import { ChatModalComponent } from 'src/app/pages/message-center/chat-modal/chat-modal.component';
import { AssetSource, Constants, UserGroup } from 'src/app/constants/constants';
import { FileAttachments, IvisitConfirmation, IvisitFormData, IFormData, TimeZones } from 'src/app/interfaces/schedule-center';
import { CommonService } from 'src/app/services/common-service/common.service';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { VisitScheduleService } from 'src/app/services/visit-schedule-service/visit-schedule.service';
import {
  compareDateTimeWithIsBefore,
  compareWithIsbefore,
  convertDateTimeToCustomFormat,
  convertDateTimeToIsoFormat,
  deepParseJSON,
  formatDate,
  formatTime,
  isBlank,
  isPresent
} from 'src/app/utils/utils';
import { VisitScheduleConstants, VisitType } from 'src/app/constants/visit-schedule-constants';
import { InAppBrowser } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { AdvancedViewerComponent } from 'src/app/components/advanced-viewer/advanced-viewer.component';
import { isVirtual } from 'src/app/utils/storage-utils';
import { MapViewComponent } from 'src/app/components/map-view/map-view.component';
import { HttpClient } from '@angular/common/http';
import { catchError, map } from 'rxjs/operators';
import { of } from 'rxjs';
import { environment } from '../../../../../environments/environment';

@Component({
  selector: 'app-view',
  templateUrl: './view.page.html',
  styleUrls: ['./view.page.scss']
})
export class ViewPage implements OnInit {
  viewForm: UntypedFormGroup;
  timeZones: TimeZones = [];
  patientVisit = Constants.patientVisitStatus;
  visitKey: string;
  requestParams;
  dateFormat = Constants.dateFormat;
  minDate: string;
  staffUser = false;
  hideVisitKey = false;
  accept = true;
  showConfirmButton = false;
  actualDateTimeView = false;
  currentDateTime = new Date();
  actualDateValidator = false;
  viewButtonGroup = false;
  staffVisitStatus = Constants.staffVisitStatus;
  home = Constants.home;
  homeVisitType: boolean;
  staffNotConfirmed = false;
  userData: any;
  loggedStaff = false;
  loggedPatient = false;
  loggedCaregiver = false;
  checkRecurrenceVisit: boolean;
  editType = Constants.editType.single;
  patientCurrrentStatus: number;
  visitCompleted = false;
  statusStaff = Constants.staffVisitStatus;
  maxYear: string;
  isDraft: boolean;
  attachedFiles: [FileAttachments];
  weekly = Constants.weekly;
  monthly = Constants.monthly;
  none = Constants.none;
  formData: IFormData;
  VisitType = VisitType;
  isCalendarViewDisabled = false;
  selectedVisitLocation: any = {};
  selectedVisitChair: any = {};
  selectedTherapyType: any = {};
  selectedDosageAppointment: any = {};
  isVirtual = false;
  isScheduleVisit: boolean;
  enableTherapyFields = false;
  enableTotalMileageField = false;
  enableDriveTimeField = false;
  addressValidationCache: { [address: string]: boolean } = {};
  constructor(
    private readonly formBuilder: UntypedFormBuilder,
    public sharedService: SharedService,
    private route: ActivatedRoute,
    public router: Router,
    public alertController: AlertController,
    private readonly common: CommonService,
    private browser: InAppBrowser,
    private visitScheduleService: VisitScheduleService,
    private readonly modalController: ModalController,
    private readonly http: HttpClient,
    private readonly changeDetectorRef: ChangeDetectorRef
  ) {}
  ngOnInit(): void {
    this.isVirtual = isVirtual();
    this.userData = this.sharedService.userData;
    this.staffUser = Number(this.sharedService?.userData?.group) !== UserGroup.PATIENT;
    this.maxYear = this.sharedService.setCalendarPickerMaxYear();
    this.route.paramMap.subscribe((paramMap) => {
      const visitKey = paramMap.get('visitKey');
      this.requestParams = this.router.getCurrentNavigation()?.extras?.state?.data;
      this.isCalendarViewDisabled = !!this.requestParams?.disableCalendarView;
      if (visitKey) {
        this.visitKey = visitKey;
        this.getTimeZones();
        this.getVisitById();
      } else {
        this.router.navigate([PageRoutes.scheduleCenterVisits]);
      }
    });
  }
  navigateToCalendarView(): void {
    if (!this.sharedService.userData.appLessSession && !isVirtual()) {
      this.router.navigate([PageRoutes.scheduleCenterVisits]);
    } else {
      this.viewForm = undefined;
      this.getVisitById();
    }
  }
  setPatientTimeZone(): void {
    if (this.formData && this.timeZones) {
      this.formData.patientTimeZone = this.timeZones.find((item) => item?.city === this.formData?.locationTimeZone)?.name;
      this.viewForm.get('patientTimeZone').patchValue(this.formData.patientTimeZone);
    }
  }
  getTimeZones(): void {
    if (isPresent(this.sharedService.timeZones)) {
      this.timeZones = this.sharedService.timeZones;
      this.setPatientTimeZone();
    } else {
      this.sharedService.getAllTimeZones().subscribe((timezones: TimeZones) => {
        if (timezones && timezones.length) {
          this.timeZones = timezones;
          this.sharedService.timeZones = timezones;
        }
        this.setPatientTimeZone();
      });
    }
  }

  manageData(): void {
    if (this.viewForm) {
      let updateFormData: IvisitFormData = {
        actualTimeIn:
          this.viewForm.value.staffStatus === Constants.staffVisitStatus[4].value && this.viewForm.value?.actualStartDate
            ? convertDateTimeToIsoFormat(
              formatDate(this.viewForm.value.actualStartDate, Constants.dateFormat.ymd),
              formatTime(this.viewForm.value.actualStartTime, this.dateFormat.h2ma, this.dateFormat.hhmm0)
            )
            : '',
        actualTimeOut:
          this.viewForm.value.staffStatus === Constants.staffVisitStatus[4].value && this.viewForm.value?.actualStartDate
            ? convertDateTimeToIsoFormat(
              formatDate(this.viewForm.value.actualStartDate, Constants.dateFormat.ymd),
              formatTime(this.viewForm.value.actualEndTime, this.dateFormat.h2ma, this.dateFormat.hhmm0)
            )
            : '',
        additionalDetails: this.viewForm.value.additionalDetails,
        assignedTo: [this.viewForm.value.assignedToUserId],
        countryCode: this.viewForm.value.countryCode,
        countryIsoCode: this.viewForm.value.countryIsoCode,
        duration: this.viewForm.value.duration,
        editType: this.editType,
        endAt: this.viewForm.value.endDate
          ? convertDateTimeToIsoFormat(
            formatDate(this.viewForm.value.endDate, Constants.dateFormat.ymd),
            formatTime(this.viewForm.value.endEventTime, this.dateFormat.h2ma, this.dateFormat.hhmm0)
          )
          : '',
        isBillable: this.viewForm.value.isBillable,
        isSubcontracted: this.viewForm.value.subContractedStatus,
        locationTimezone: this.viewForm.value.locationTimeZone,
        organization: this.viewForm.value.organizationName,
        patientConfirmation: String(this.viewForm.value.patientStatus),
        patientId: this.viewForm.value.patientId,
        phoneNumber: this.viewForm.value.phoneNumber,
        repeatDays: this.viewForm.value.repeatDays ? this.viewForm.value.repeatDays : '',
        repeatInterval: this.viewForm.value.repeatInterval ? this.viewForm.value.repeatInterval : '',
        repeatMonthType: this.viewForm.value.repeatMonthType ? this.viewForm.value.repeatMonthType : '',
        repeatType: this.viewForm.value.repeatType,
        siteId: this.viewForm.value.siteId,
        staffConfirmation: String(this.viewForm.value.staffStatus),
        startAt: convertDateTimeToIsoFormat(
          formatDate(this.viewForm.value.startDate, Constants.dateFormat.ymd),
          formatTime(this.viewForm.value.startEventTime, this.dateFormat.h2ma, this.dateFormat.hhmm0)
        ),
        until:
          this.viewForm.value.repeatType === Constants.none
            ? ''
            : convertDateTimeToIsoFormat(
              formatDate(this.viewForm.value.untilDate, Constants.dateFormat.ymd),
              formatTime(this.viewForm.value.endEventTime, this.dateFormat.h2ma, this.dateFormat.hhmm0)
            ),
        visitAddress: this.viewForm.value.visitAddress,
        visitStatus: this.viewForm.value.visitStatusId,
        visitTitle: this.viewForm.value.eventVisitTitle,
        visitType: this.viewForm.value.visitType,
        visitTypeLocation: this.viewForm.value.visitTypeLocation,
        visitChair: this.viewForm.value.visitChair,
        visitationDetails: this.viewForm.value.visitationDetails,
        staffReasonForCancel: this.viewForm.value.staffReasonForCancel ? this.viewForm.value.staffReasonForCancel : '',
        patientReasonForCancel: this.viewForm.value.patientReasonForCancel ? this.viewForm.value.patientReasonForCancel : ''
      };

      // add previous day if it is single visit update
      if (this.editType === Constants.editType.single) {
        updateFormData = {
          ...updateFormData,
          previousEndAt: this.viewForm.value.endAt
            ? convertDateTimeToIsoFormat(formatDate(this.viewForm.value.endAt, Constants.dateFormat.ymd), this.viewForm.value.endTime)
            : '',
          previousStartAt: this.viewForm.value.startAt
            ? convertDateTimeToIsoFormat(formatDate(this.viewForm.value.startAt, Constants.dateFormat.ymd), this.viewForm.value.startTime)
            : ''
        };
      }

      this.visitScheduleService.manageVisitData(this.visitKey, updateFormData, true).subscribe((response) => {
        if (response && response.success) {
          this.common.showMessage(this.common.getTranslateData('SUCCESS_MESSAGES.VISIT_SAVE_SUCCESS'), { id: 'visit', autoDismissTimeOut: 1000 });
          setTimeout(() => {
            if (isPresent(response?.data?.visitKey) && response.data.visitKey !== this.visitKey) {
              this.visitKey = response.data.visitKey;
            }
            this.navigateToCalendarView();
          }, 2000);
        } else {
          this.common.showMessage(this.common.getTranslateData('ERROR_MESSAGES.VISIT_UPDATE_FAILED'));
        }
      });
    }
  }
  /**
   * Determines if the field is disabled.
   * The field is disabled if any of the following conditions are met:
   * - The showConfirmButton property is true.
   * - The loggedStaff property is falsy.
   * - The visitCompleted property is true.
   * @returns A boolean value indicating whether the field is disabled.
   */
  get isDisabledField(): boolean {
    return this.showConfirmButton || !this.loggedStaff || this.visitCompleted || !this.isVirtual;
  }

  /**
   * Formats time string from HH:MM:SS to HH:MM format
   * @param timeString Time string in HH:MM:SS format
   * @returns Formatted time string in HH:MM format
   */
  formatDriveTime(timeString: string): string {
    if (!timeString) return '';

    // If already in HH:MM format or other format, return as is
    if (!timeString.includes(':')) return timeString;

    // Split the time string by colon
    const timeParts = timeString.split(':');

    // If we have at least hours and minutes, return them
    if (timeParts.length >= 2) {
      return `${timeParts[0]}:${timeParts[1]}`;
    }

    // Return original if can't format
    return timeString;
  }

  private determineShowConfirmButton(visitData: any): boolean {
    if (this.isDraft) {
      return false;
    }

    const patientVisitStatusId = Number(visitData.patientVisitStatusId);
    const requestPatientConfirmation = Number(visitData.requestPatientConfirmation);

    if (this.loggedStaff && this.staffNotConfirmed) {
      return true;
    }

    if (
      (this.loggedPatient || this.loggedCaregiver) &&
      requestPatientConfirmation === 1 &&
      patientVisitStatusId === Constants.patientVisitStatus[0].value
    ) {
      return true;
    }

    return false;
  }

  getVisitById(): void {
    this.sharedService.isLoading = true;
    if (this.visitKey) {
      this.visitScheduleService.getVisits('', this.visitKey).subscribe(
        (result) => {
          if (result.data) {
            // Check if therapy fields should be shown
            this.enableTherapyFields = this.sharedService.getConfigValue('visit_enable_therapy_fields') === '1';
            // Check if mileage field should be shown and required
            this.enableTotalMileageField = this.sharedService.getConfigValue('visit_enable_total_mileage_field') === '1';
            // Check if drive time field should be shown and required
            this.enableDriveTimeField = this.sharedService.getConfigValue('visit_enable_drive_time_field') === '1';
            
            if (!isBlank(result.data.attachment)) {
              this.attachedFiles = result.data.attachment;
              this.attachedFiles.forEach((item) => {
                item['formattedFileName'] = item.name.substring(Constants.uploadedVisitFileNameDefaultSubstringLength);
              });
            }
            let data = {
              ...result.data,
              visitTypeLocation: !isBlank(result.data.visitTypeLocation) ? result.data.visitTypeLocation : '',
              visitChair: !isBlank(result.data.visitChair) ? result.data.visitChair : '',
              therapyType: !isBlank(result.data.therapyType) ? result.data.therapyType : '',
              dosageAppointment: !isBlank(result.data.dosageAppointment) ? result.data.dosageAppointment : '',
              locationtimeZone: result.data.locationTimeZone,
              totalMileage: !isBlank(result.data.totalMileage) ? result.data.totalMileage : '',
              totalDriveTime: !isBlank(result.data.totalDriveTime) ? this.formatDriveTime(result.data.totalDriveTime) : '',
              mobileNumber: result.data.phoneNumber ? `${this.sharedService.setCountryCode(result.data.countryCode)} ${result.data.phoneNumber}` : '',
              startDate: formatDate(convertDateTimeToCustomFormat(result.data?.startAt, result.data?.startTime), this.dateFormat.mdy),
              startEventTime: formatDate(convertDateTimeToCustomFormat(result.data?.startAt, result.data?.startTime), this.dateFormat.h2ma),
              endDate: formatDate(convertDateTimeToCustomFormat(result.data?.endAt, result.data?.endTime), this.dateFormat.mdy),
              endEventTime: formatDate(convertDateTimeToCustomFormat(result.data?.endAt, result.data?.endTime), this.dateFormat.h2ma),
              actualStartDate: result.data.actualTimeIn
                ? formatDate(
                  convertDateTimeToCustomFormat(
                    formatDate(result.data.actualTimeIn, Constants.dateFormat.ymd),
                    formatDate(result.data.actualStartTime, Constants.dateFormat.hhmm0)
                  ),
                  this.dateFormat.mdy
                )
                : '',
              actualStartTime: result.data.actualStartTime
                ? formatDate(
                  convertDateTimeToCustomFormat(
                    formatDate(result.data.actualStartTime, Constants.dateFormat.ymd),
                    formatDate(result.data.actualStartTime, Constants.dateFormat.hhmm0)
                  ),
                  this.dateFormat.h2ma
                )
                : '',
              actualEndTime: result.data.actualEndTime
                ? formatDate(
                  convertDateTimeToCustomFormat(
                    formatDate(result.data.actualEndTime, Constants.dateFormat.ymd),
                    formatDate(result.data.actualEndTime, Constants.dateFormat.hhmm0)
                  ),
                  this.dateFormat.h2ma
                )
                : '',
              patientStatus: Number(result.data.patientVisitStatusId),
              staffStatus: Number(result.data.staffVisitStatusId),
              billable: this.sharedService.isConfigTrue(result.data.isBillable) ? Constants.yes : Constants.no,
              subcontracted: this.sharedService.isConfigTrue(result.data.subContractedStatus),
              patientNotification: this.sharedService.isConfigTrue(result.data.sendPatientNotification),
              endOn: result.data.untilDate
                ? formatDate(convertDateTimeToCustomFormat(result.data.untilDate, result.data.endTime), this.dateFormat.mdy)
                : '',
              recurrenceStart: `${formatDate(
                convertDateTimeToCustomFormat(result.data.startAt, result.data.startTime),
                this.dateFormat.mdy
              )}  ${formatDate(convertDateTimeToCustomFormat(result.data.startAt, result.data.startTime), this.dateFormat.h2ma)}`,
              recurrenceEnd: `${formatDate(convertDateTimeToCustomFormat(result.data.endAt, result.data.endTime), this.dateFormat.mdy)}  ${formatDate(
                convertDateTimeToCustomFormat(result.data.endAt, result.data.endTime),
                this.dateFormat.h2ma
              )}`,
              monthType: result.data.repeatMonthType
                ? result.data.repeatMonthType === Constants.monthlySchedule[0].value
                  ? Constants.monthlySchedule[0].name
                  : Constants.monthlySchedule[1].name
                : '',
              repeat: result.data.repeatType
                ? result.data.repeatType === Constants.repeatSchedules[0].value
                  ? this.common.getTranslateData(Constants.repeatSchedules[0].name)
                  : result.data.repeatType === Constants.repeatSchedules[1].value
                    ? this.common.getTranslateData(Constants.repeatSchedules[1].name)
                    : this.common.getTranslateData(Constants.repeatSchedules[2].name)
                : '',
              patientTimeZone: ''
            };

            if (!isBlank(result.data.visitTypeLocation)) {
              this.visitScheduleService.getVisitTypeLocation(result.data.visitTypeLocation).subscribe((data) => {
                this.selectedVisitLocation = data;
              });
            }
            if (!isBlank(result.data.visitChair)) {
              this.visitScheduleService.getVisitChair(result.data.visitChair).subscribe((data) => {
                this.selectedVisitChair = data;
              });
            }
            
            if (this.enableTherapyFields) {
              // Fetch therapy type data if available
              if (!isBlank(result.data.therapyType) && result.data.therapyType !== '0') {
                this.visitScheduleService.getTherapyType(result.data.therapyType).subscribe((data) => {
                  this.selectedTherapyType = data;
                });
              }

              // Fetch dosage appointment data if available
              if (!isBlank(result.data.dosageAppointment) && result.data.dosageAppointment !== '0') {
                this.visitScheduleService.getDosageAppointment(result.data.dosageAppointment).subscribe((data) => {
                  this.selectedDosageAppointment = data;
                });
              }
            }
            const calendarData = this.requestParams?.visitCalendarData;
            if (calendarData && calendarData?.start && calendarData?.startTime && calendarData?.end && calendarData?.endTime) {
              data = {
                ...data,
                assignedToUsers: isPresent(result?.data?.assignedToUserId)
                  ? deepParseJSON(result?.data?.assignedToUserId).map((item) => item?.userid?.toString())
                  : [],
                locationTimeZone: calendarData.timeZone ?? data.locationTimeZone,
                startDate: formatDate(convertDateTimeToCustomFormat(calendarData.start, calendarData.startTime), this.dateFormat.mdy),
                startEventTime: formatDate(convertDateTimeToCustomFormat(calendarData.start, calendarData.startTime), this.dateFormat.h2ma),
                endDate: formatDate(convertDateTimeToCustomFormat(calendarData.end, calendarData.endTime), this.dateFormat.mdy),
                endEventTime: formatDate(convertDateTimeToCustomFormat(calendarData.end, calendarData.endTime), this.dateFormat.h2ma)
              };
            }
            this.formData = data;
            this.viewForm = this.formBuilder.group(data);

            this.getTimeZones();

            // actual date time view
            if (
              Number(result.data.staffVisitStatusId) === Constants.staffVisitStatus[4].value &&
              Number(result.data.patientVisitStatusId) === Constants.patientVisitStatus[4].value
            ) {
              this.actualDateTimeView = true;
              this.visitCompleted = true;
            }

            // show save button based on status
            if (
              Number(result.data.staffVisitStatusId) === Constants.staffVisitStatus[4].value ||
              Number(result.data.staffVisitStatusId) === Constants.staffVisitStatus[3].value ||
              Number(result.data.staffVisitStatusId) === Constants.staffVisitStatus[5].value ||
              Number(result.data.patientVisitStatusId) === Constants.patientVisitStatus[2].value ||
              Number(result.data.patientVisitStatusId) === Constants.patientVisitStatus[3].value
            ) {
              this.visitCompleted = true;
            }

            // filter staff status if staff  is not confirmed
            if (Number(result.data.staffVisitStatusId) === Constants.staffVisitStatus[1].value) {
              this.staffNotConfirmed = true;
            } else if (Number(result.data.staffVisitStatusId) === Constants.staffVisitStatus[3].value) {
              this.staffNotConfirmed = false;
              this.staffVisitStatus = Constants.staffVisitStatus;
              if (this.staffUser) {
                this.viewForm.patchValue({
                  staffStatus: Number(result.data.staffVisitStatusId)
                });
              }
            } else {
              if (Number(result.data.staffVisitStatusId) === Constants.staffVisitStatus[0].value && this.staffUser) {
                this.viewForm.patchValue({
                  staffStatus: Number(result.data.staffVisitStatusId)
                });
              } else {
                this.staffVisitStatus = Constants.staffVisitStatus.filter((x) => x.value !== Constants.staffVisitStatus[0].value);
              }
              this.staffNotConfirmed = false;
              this.patientVisit = this.patientVisit.map((item: any) => {
                if (item.value !== Constants.patientVisitStatus[4].value) {
                  item.disabled = false;
                }
                return item;
              });
            }
            this.setCompletedOptionAccess();
            // check home type
            this.homeVisitType = result.data.visitTypeName === this.home;
            // check user
            this.loggedStaff = this.userData.userId === result.data.staffId;
            this.loggedPatient = this.userData.userId === result.data.patientId;
            if (
              isPresent(result.data?.patientArray) &&
              result.data?.patientArray[0] &&
              result.data?.patientArray[0]?.caregiver_userid === this.userData.userId
            ) {
              this.loggedCaregiver = true;
            }

            this.currentDateTime = formatDate(
              convertDateTimeToCustomFormat(formatDate(new Date(), Constants.dateFormat.ymd), formatDate(new Date(), Constants.dateFormat.hhmm0)),
              this.dateFormat.ymd
            );
            // set patient current data
            this.patientCurrrentStatus = Number(result.data.patientVisitStatusId);

            this.checkRecurrenceVisit = result.data.repeatType !== Constants.none;
            this.isDraft = result.data.visitStatusId === VisitScheduleConstants.isDraft;
            this.isScheduleVisit = result.data.visitStatusId === VisitScheduleConstants.isSchedule;

            this.showConfirmButton = this.determineShowConfirmButton(result.data);

            this.sharedService.isLoading = false;
            if (
              !(
                Number(result.data.staffVisitStatusId) === Constants.staffVisitStatus[2].value &&
                Number(result.data.patientVisitStatusId) === Constants.patientVisitStatus[1].value
              )
            ) {
              this.staffVisitStatus = this.staffVisitStatus.map((x) => {
                x.disabled = x.value !== Constants.staffVisitStatus[0].value && x.value !== Constants.staffVisitStatus[3].value;
                return x;
              });
            }
            let weeks: string;
            const selectedWeek = result.data.repeatDays ? result.data.repeatDays.split(',') : [];
            if (selectedWeek && selectedWeek.length) {
              selectedWeek.forEach((value) => {
                weeks = Constants.weeklySchedule
                  .filter((obj) => obj.value === value)
                  .map((item) => item.name)
                  .join(',');
              });
              this.formData = { ...this.formData, weekly: weeks };
            }
          }
        },
        (error) => {
          this.sharedService.errorHandler(error);
          this.common.showToast({ message: this.common.getTranslateData('ERROR_MESSAGES.TECHNICAL_DIFFICULTY_TRY_AGAIN'), color: 'warning' });
          this.router.navigate([PageRoutes.scheduleCenterVisits]);
        });
    }
  }
  setCompletedOptionAccess(): void {
    const enableComplete = compareDateTimeWithIsBefore(
      [this.viewForm.value.endDate, this.viewForm.value.endEventTime].join(' '),
      '',
      this.viewForm.value.locationTimeZone
    );
    this.staffVisitStatus = this.staffVisitStatus.map((item) => {
      if (item.value === Constants.staffVisitCompleted) {
        item.disabled = enableComplete;
      }
      return item;
    });
  }
  cancel() {
    this.navigateToCalendarView();
  }

  setStartDate(data?: any): void {
    if (data) {
      const date = formatDate(data, this.dateFormat.mdy);
      this.viewForm.patchValue({
        startDate: date,
        endDate: date
      });
      this.setCompletedOptionAccess();
    }
  }
  setEndDate(data?: any): void {
    if (data) {
      const date = formatDate(data, this.dateFormat.mdy);
      this.viewForm.patchValue({
        endDate: date
      });
      this.setCompletedOptionAccess();
    }
  }
  setStartTime(data?: any): void {
    if (data) {
      const time = [this.viewForm.value.endDate, data].join(' ');
      const startTime = formatDate(time, this.dateFormat.h2ma);
      const end = moment(time).add(1, 'hour');
      const endDate = formatDate(end, this.dateFormat.mdy);
      const endTime = formatDate(end, this.dateFormat.h2ma);
      this.viewForm.patchValue({
        startEventTime: startTime,
        endEventTime: endTime,
        endDate
      });
      this.setCompletedOptionAccess();
    }
  }
  setEndTime(data?: any): void {
    if (data) {
      this.viewForm.controls.endEventTime.reset();
      const end = formatDate([this.viewForm.value.endDate, data].join(' '), this.dateFormat.h2ma);
      this.viewForm.patchValue({
        endEventTime: end
      });
      if (!this.compareEndDateTimeWithStart()) {
        this.viewForm.controls.endEventTime.setErrors({ incorrect: true });
      }
      this.setCompletedOptionAccess();
    }
  }

  compareEndDateTimeWithStart(): boolean {
    return compareWithIsbefore(
      [this.viewForm.value.startDate, this.viewForm.value.startEventTime].join(' '),
      [this.viewForm.value.endDate, this.viewForm.value.endEventTime].join(' ')
    );
  }

  setActualStartDate(data?: any): void {
    if (data) {
      const date = formatDate(data, this.dateFormat.mdy);
      this.viewForm.patchValue({
        actualStartDate: date
      });
    }
    this.actualDateValidation();
  }
  setActualStartTime(data?: any): void {
    if (data) {
      const actualStartTime = formatDate(
        [
          this.viewForm.value.actualStartDate ? this.viewForm.value.actualStartDate : formatDate(this.currentDateTime, this.dateFormat.YYYYMMDD),
          data
        ].join(' '),
        this.dateFormat.h2ma
      );
      this.viewForm.patchValue({ actualStartTime });
    }
    this.actualDateValidation();
  }
  setActualEndTime(data?: any): void {
    if (data) {
      const actualEndTime = formatDate(
        [
          this.viewForm.value.actualStartDate ? this.viewForm.value.actualStartDate : formatDate(this.currentDateTime, this.dateFormat.YYYYMMDD),
          data
        ].join(' '),
        this.dateFormat.h2ma
      );
      this.viewForm.patchValue({
        actualEndTime
      });
    }
    this.actualDateValidation();
  }
  get disableSaveButton(): boolean {
    return !this.viewForm.valid || (this.staffUser ? !this.viewForm.value.staffReasonForCancel : !this.viewForm.value.patientReasonForCancel);
  }
  changeDateFormat(value: string, format: string): string {
    if (!value) {
      value = this.currentDateTime.toString();
    }
    return formatDate(value, format);
  }
  fetchStartDateTime(): string {
    return formatDate([this.viewForm.value.startDate, this.viewForm.value.startEventTime].join(' '), this.dateFormat.hhmm0);
  }
  fetchEndDateTime(): string {
    return formatDate([this.viewForm.value.endDate, this.viewForm.value.endEventTime].join(' '), this.dateFormat.hhmm0);
  }
  fetchActualStartTime(): string {
    if (this.viewForm.value.actualStartDate && this.viewForm.value.actualStartTime) {
      return formatDate([this.viewForm.value.actualStartDate, this.viewForm.value.actualStartTime].join(' '), this.dateFormat.hhmm0);
    }
    return formatDate(this.currentDateTime, this.dateFormat.hhmm0);
  }
  fetchActualEndDateTime(): string {
    if (this.viewForm.value.actualStartDate && this.viewForm.value.actualEndTime) {
      return formatDate([this.viewForm.value.actualStartDate, this.viewForm.value.actualEndTime].join(' '), this.dateFormat.hhmm0);
    }
    return formatDate(this.currentDateTime, this.dateFormat.hhmm0);
  }

  visitConfirmation(action) {
    let paramData: IvisitConfirmation = { action, editType: this.editType };

    if (
      (this.viewForm.value.staffReasonForCancel && this.editType === Constants.editType.single) ||
      (action === Constants.confirm && this.editType === Constants.editType.single && this.staffUser)
    ) {
      paramData = {
        ...paramData,
        endAt: convertDateTimeToIsoFormat(
          formatDate(this.viewForm.value.endDate, Constants.dateFormat.ymd),
          formatTime(this.viewForm.value.endEventTime, this.dateFormat.h2ma, this.dateFormat.hhmm0)
        ),
        startAt: convertDateTimeToIsoFormat(
          formatDate(this.viewForm.value.startDate, Constants.dateFormat.ymd),
          formatTime(this.viewForm.value.startEventTime, this.dateFormat.h2ma, this.dateFormat.hhmm0)
        )
      };
      if (this.viewForm.value.staffReasonForCancel) {
        paramData = {
          ...paramData,
          staffReasonForCancel: this.viewForm.value.staffReasonForCancel
        };
      }
    }
    if (this.viewForm.value.staffReasonForCancel && this.editType === Constants.editType.series) {
      paramData = {
        ...paramData,
        staffReasonForCancel: this.viewForm.value.staffReasonForCancel
      };
    }

    if (this.viewForm.value.patientReasonForCancel && this.editType === Constants.editType.series) {
      paramData = {
        ...paramData,
        patientReasonForCancel: this.viewForm.value.patientReasonForCancel
      };
    }

    if (
      (this.viewForm.value.patientReasonForCancel && this.editType === Constants.editType.single) ||
      (action === Constants.confirm && this.editType === Constants.editType.single && !this.staffUser)
    ) {
      paramData = {
        ...paramData,
        endAt: convertDateTimeToIsoFormat(
          formatDate(this.viewForm.value.endDate, Constants.dateFormat.ymd),
          formatTime(this.viewForm.value.endEventTime, this.dateFormat.h2ma, this.dateFormat.hhmm0)
        ),
        startAt: convertDateTimeToIsoFormat(
          formatDate(this.viewForm.value.startDate, Constants.dateFormat.ymd),
          formatTime(this.viewForm.value.startEventTime, this.dateFormat.h2ma, this.dateFormat.hhmm0)
        )
      };
      if (this.viewForm.value.patientReasonForCancel) {
        paramData = {
          ...paramData,
          patientReasonForCancel: this.viewForm.value.patientReasonForCancel
        };
      }
    }
    this.visitScheduleService.manageVisitData(this.visitKey, paramData, true).subscribe((response) => {
      if (response && response.success) {
        const message = action === Constants.confirm ? 'SUCCESS_MESSAGES.VISIT_CONFIRMED_SUCCESS' : 'SUCCESS_MESSAGES.VISIT_DECLINED_SUCCESS';
        this.common.showMessage(this.common.getTranslateData(message), { id: 'visit', autoDismissTimeOut: 1000 });
        if (isPresent(response?.data?.visitKey) && response.data.visitKey !== this.visitKey) {
          this.visitKey = response.data.visitKey;
        }
        this.navigateToCalendarView();
        this.modalController.dismiss();
      } else {
        const message = action === Constants.confirm ? 'ERROR_MESSAGES.VISIT_CONFIRMATIN_FAILED' : 'ERROR_MESSAGES.VISIT_DECLINED_FAILED';
        this.common.showMessage(this.common.getTranslateData(message));
      }
    });
  }
  async confirmVisit() {
    let buttons;
    let cssClass;
    let message;
    if (this.checkRecurrenceVisit && !this.isCalendarViewDisabled) {
      buttons = [
        { text: 'BUTTONS.CANCEL', confirm: false, class: 'warn-btn alert-cancel' },
        { text: 'BUTTONS.SINGLE', confirm: true, class: 'warn-btn alert-ok', value: Constants.editType.single },
        { text: 'BUTTONS.ALL', confirm: true, class: 'warn-btn alert-ok', value: Constants.editType.series }
      ];
      cssClass = 'custom-alert';
      message = this.viewForm.value.staffReasonForCancel ? 'MESSAGES.RECURRENCE_VISIT_DECLINE' : 'MESSAGES.RECURRENCE_VISIT_ACCEPT';
    } else {
      buttons = [
        { text: 'BUTTONS.CANCEL', confirm: false, class: 'warn-btn alert-cancel' },
        { text: 'BUTTONS.OK', confirm: true, class: 'warn-btn alert-ok' }
      ];
      cssClass = 'visit-alert';
      message =
        this.viewForm.value.staffReasonForCancel || this.viewForm.value.patientReasonForCancel ? 'MESSAGES.VISIT_DECLINE' : 'MESSAGES.VISIT_ACCEPT';
    }
    this.common
      .showAlert({
        message,
        header: 'MESSAGES.ARE_YOU_SURE',
        buttons,
        mode: 'ios',
        cssClass
      })
      .then((confirmation) => {
        if (confirmation === false) {
          this.alertController.dismiss();
        } else {
          const action =
            this.viewForm.value.staffReasonForCancel || this.viewForm.value.patientReasonForCancel ? Constants.decline : Constants.confirm;
          this.editType = confirmation === true ? Constants.editType.series : confirmation;
          this.visitConfirmation(action);
        }
      });
  }

  actualDateValidation() {
    if (
      moment(`${this.viewForm.value.actualStartDate} ${this.viewForm.value.actualStartTime}`).isSameOrAfter(
        `${this.viewForm.value.startDate} ${this.viewForm.value.startEventTime}`
      ) &&
      moment(`${this.viewForm.value.actualStartDate} ${this.viewForm.value.actualEndTime}`).isAfter(
        `${this.viewForm.value.actualStartDate} ${this.viewForm.value.actualStartTime}`
      )
    ) {
      this.actualDateValidator = false;
    } else {
      this.actualDateValidator = true;
    }
  }

  updateVisit(): void {
    let buttons;
    let cssClass;
    let message;
    if (this.checkRecurrenceVisit && !this.isCalendarViewDisabled) {
      buttons = [
        { text: 'BUTTONS.CANCEL', confirm: false, class: 'warn-btn alert-cancel' },
        { text: 'BUTTONS.SINGLE', confirm: true, class: 'warn-btn alert-ok', value: Constants.editType.single },
        { text: 'BUTTONS.ALL', confirm: true, class: 'warn-btn alert-ok', value: Constants.editType.series }
      ];
      cssClass = 'custom-alert';
      message = 'MESSAGES.VISIT_MODIFY_REPEATED_EVENTS_CONFIRM';
    } else {
      buttons = [
        { text: 'BUTTONS.CANCEL', confirm: false, class: 'warn-btn alert-cancel' },
        { text: 'BUTTONS.OK', confirm: true, class: 'warn-btn alert-ok' }
      ];
      cssClass = 'visit-alert';
      message = 'LABELS.DO_YOU_WANT_TO_SAVE';
    }

    this.common
      .showAlert({
        message,
        header: 'MESSAGES.ARE_YOU_SURE',
        buttons,
        mode: 'ios',
        cssClass
      })
      .then((confirmation) => {
        if (confirmation === true) {
          this.editType = Constants.editType.series;
          this.manageData();
        } else if (confirmation === false) {
          this.common.closeAllAlert();
        } else {
          this.editType = confirmation;
          this.manageData();
        }
      });
  }

  staffStatusChange() {
    if (this.viewForm.value.staffStatus === Constants.staffVisitStatus[4].value) {
      this.patientVisit = this.patientVisit.map((x) => ({
        ...x,
        disabled: true
      }));
      this.viewForm.patchValue({
        patientStatus: Constants.patientVisitStatus[4].value,
        actualStartDate: ''
      });
      this.actualDateTimeView = true;
      this.actualDateValidation();
    } else if (this.viewForm.value.staffStatus === Constants.staffVisitStatus[2].value) {
      this.patientVisit = this.patientVisit.map((item) => {
        if (item.value !== Constants.patientVisitStatus[4].value) {
          item.disabled = false;
        }
        return item;
      });
      this.viewForm.patchValue({
        patientStatus: this.patientCurrrentStatus
      });
      this.actualDateTimeView = false;
    } else if (this.viewForm.value.staffStatus === Constants.staffVisitStatus[5].value) {
      this.patientVisit = Constants.patientVisitStatus.map((item: any) => {
        item.disabled = item.value !== Constants.patientVisitStatus[0].value;
        return item;
      });
      this.viewForm.patchValue({
        patientStatus: this.patientCurrrentStatus
      });
      this.actualDateTimeView = false;
    } else if (this.viewForm.value.staffStatus === Constants.staffVisitStatus[1].value) {
      this.patientVisit = Constants.patientVisitStatus.map((item: any) => {
        item.disabled = item.value !== Constants.patientVisitStatus[0].value;
        return item;
      });
    }
    if (this.viewForm.value.staffReasonForCancel !== Constants.staffVisitStatus[5].value) {
      this.viewForm.controls['staffReasonForCancel'].reset();
    }
    if (this.actualDateTimeView && this.staffUser && this.viewForm.value.staffStatus !== Constants.staffVisitStatus[4].value) {
      this.viewForm.controls['actualStartDate'].reset();
      this.viewForm.controls['actualStartTime'].reset();
      this.viewForm.controls['actualEndTime'].reset();
    }
  }
  dismiss() {
    this.viewForm.patchValue({
      staffReasonForCancel: ''
    });
    this.modalController.dismiss();
  }
  openChatModal(): void {
    this.common
      .showAlert({
        header: 'MESSAGES.ARE_YOU_SURE',
        message: this.common.getTranslateDataWithParam('MESSAGES.VISIT_CHAT_WITH_USER', {
          userRole: Constants.userPatient,
          userName: this.viewForm.value.patientName
        })
      })
      .then((data) => {
        if (data) {
          this.visitScheduleService.getChatForVisitPaitent(this.viewForm.value.visitKey).subscribe((response) => {
            if (response.success && isPresent(response.data.chatroomId)) {
              this.presentChatModal(response.data.chatroomId);
            }
          });
        }
      });
  }
  async presentChatModal(chatroomId: string): Promise<void> {
    const chatModal = await this.modalController.create({
      component: ChatModalComponent,
      backdropDismiss: false,
      showBackdrop: true,
      id: 'chat-modal',
      animated: true,
      componentProps: {
        chatroomId: Number(chatroomId),
        modalId: 'chat-modal'
      }
    });
    chatModal.present();
  }
  async presentImageViewerModal(item, index): Promise<void> {
    const componentProps = this.sharedService.fetchImageViewerModalProps(item, index);
    if (componentProps) {
      if (componentProps.type === Constants.documentTypes.pdf) {
        this.sharedService.isLoading = true;
        this.sharedService.presentPdfFromLink({ url: componentProps.url, source: AssetSource.SCHEDULE_CENTER }).then((data: any) => {
          this.sharedService.isLoading = false;
          if (data.status && isPresent(data.url)) {
            if (this.sharedService.platform.is('ios') && this.sharedService.platform.is('capacitor')) {
              this.browser.create(data.url, '_blank', this.sharedService.inAppBrowserOptions());
            } else {
              this.presentAdvancedViewerModal({ url: data.url, type: Constants.documentTypes.pdf, component: AdvancedViewerComponent });
            }
          } else if (this.sharedService.platform.is('capacitor')) {
            this.browser.create(componentProps.url, '_system');
          }
        });
      } else {
        this.presentAdvancedViewerModal({ ...componentProps, type: Constants.documentTypes.pdf, component: AdvancedViewerComponent });
      }
    }
  }
  async presentAdvancedViewerModal(componentProps) {
    const modal = await this.modalController.create({
      component: AdvancedViewerComponent,
      componentProps
    });
    return modal.present();
  }

  async showGoogleMapWithMarker(latlong: string): Promise<void> {
    if (latlong) {
      const getLatLong = latlong.split(',');
      const modal = await this.modalController.create({
        component: MapViewComponent,
        componentProps: { mapLocation: [{ lat: Number(getLatLong[0]), lng: Number(getLatLong[1]) }] }
      });
      await modal.present();
    }
  }

  openAddressInMaps(address: string): void {
    if (!address || address.trim() === '') {
      return;
    }

    const encodedAddress = encodeURIComponent(address.trim());
    const googleMapsUrl = `https://maps.google.com/?q=${encodedAddress}`;
    if (this.sharedService.platformValue === 'ios') {
      const appleMapsUrl = `maps://maps.apple.com/?q=${encodedAddress}`;
      this.openMapsApplication(encodedAddress, appleMapsUrl);
    } else if (this.sharedService.platformValue === 'android') {
      const googleMapsIntent = `geo:0,0?q=${encodedAddress}`;
      this.openMapsApplication(encodedAddress, googleMapsIntent);
    } else {
      this.openMapsApplication(encodedAddress, googleMapsUrl);
    }
  }

  openMapsApplication(address: string, mapsUrl) {
    const googleMapsUrl = `https://maps.google.com/?q=${address}`;
    try {
      window.open(mapsUrl, '_system');
    } catch {
      window.open(googleMapsUrl, '_system');
    }
  }

  validateAddressWithGoogle(address: string): Promise<boolean> {
    if (!address?.trim()) {
      return Promise.resolve(false);
  }

    const url = `https://addressvalidation.googleapis.com/v1:validateAddress?key=${environment.mapAPIKey}`;
    const requestBody = {
      address: {
        addressLines: [address.trim()]
      }
    };

    return new Promise((resolve) => {
      this.http
        .post(url, requestBody)
        .pipe(
          map((response: any) => {
            const verdict = response.result?.verdict;
            return verdict?.hasUnconfirmedComponents === false && verdict?.hasReplacedComponents === false;
          }),
          catchError(() => of(address.trim().length >= 10))
        )
        .subscribe(resolve);
    });
  }

  isAddressValid(address: string): boolean {
    if (!address || address.trim() === '') {
      return false;
    }

    return true;
  }
}
