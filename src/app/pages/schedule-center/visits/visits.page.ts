import { VisitScheduleConstants, VisitType } from 'src/app/constants/visit-schedule-constants';
import { Permissions } from 'src/app/constants/permissions';
import { PermissionService } from 'src/app/services/permission-service/permission.service';
import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { Constants } from 'src/app/constants/constants';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { NavController, PopoverController } from '@ionic/angular';
import { ModalController } from '@ionic/angular';
import { convertFromTzToTz, convertTimeZoneDateTimeToUTCIso, formatDate, isBlank } from 'src/app/utils/utils';
import * as moment from 'moment-timezone';
import { VisitScheduleService } from 'src/app/services/visit-schedule-service/visit-schedule.service';
import { Config } from 'src/app/constants/config';
import { IcalendarData, IcalendarParam, TimeZones } from 'src/app/interfaces/schedule-center';
import { CommonService } from 'src/app/services/common-service/common.service';
import { PersistentService } from 'src/app/services/persistent-service/persistent.service';
import { VisitFilterPage } from './visit-filter/visit-filter.page';
import { ViewModifyEventPage } from './view-modify-event/view-modify-event.page';

@Component({
  selector: 'app-visits',
  templateUrl: './visits.page.html',
  styleUrls: ['./visits.page.scss']
})
export class VisitsPage implements OnInit {
  public items: any[];
  datesSet = Constants.datesSet;
  userData: any;
  visitsParam: IcalendarParam;
  type: string;
  visitStatusValue = Constants.statusAll;
  mySites = [];
  visitationTypes = [];
  title = '';
  schedules = Constants.schedules;
  patientID = '';
  patientName = '';
  siteTimeZone: string;
  timeZoneName: string;
  pageRender = false;
  clinicianId: string;
  clinicianName: string;
  visitPageName: string;
  headerTitle: string;
  myVisits = Constants.myVisit;
  selectedVisit = Constants.myVisit;
  allVisit = Constants.allVisit;
  userPrivilege = false;
  patientPermission = false;
  createOrModifyVisitPermission = false;
  visitTypeLocation;
  visitChair;
  isFilterApplied = false;
  showFilterNotification = false;
  colorCodingCategory = '1';
  visitsTimezone: string;
  timeZones: TimeZones;
  calendarDateSelection: { startIntervalTime: string; endIntervalTime: string } | null = null;
  constructor(
    private sharedService: SharedService,
    private popoverCtrl: PopoverController,
    private modalController: ModalController,
    private navController: NavController,
    private visitScheduleService: VisitScheduleService,
    private permissionService: PermissionService,
    private commonService: CommonService,
    private readonly persistentService: PersistentService,
    private readonly cdr: ChangeDetectorRef
  ) {
    this.userData = this.sharedService.userData;
    this.colorCodingCategory = this.sharedService.getConfigValue(Config.visitColorCodingCategory);
    this.visitsParam = {
      calendarType: Constants.myVisit,
      startDate: '',
      endDate: '',
      colorCodingCategory: this.colorCodingCategory
    };
    this.loadTimeZones();
    this.siteTimeZone = !isBlank(this.userData.mySiteinfo)
      ? this.sharedService.getSiteConfigValue(Config.timezoneName)
      : this.sharedService.getConfigValue(Config.tenantTimezoneName);
    this.timeZoneName = !isBlank(this.userData.mySiteinfo)
      ? this.sharedService.getSiteConfigValue(Config.timezoneNameValue)
      : this.sharedService.getConfigValue(Config.tenantTimezoneNameValue);
    this.visitsTimezone = this.persistentService.getPersistentData('visitsTimezone') || this.userData.userVisitDefaultTimezone || this.siteTimeZone;
  }

  ngOnInit() {
    this.visitPageName = VisitScheduleConstants.viewVisit;
    this.headerTitle = 'TITLES.MY_VISITS';
    if (String(this.sharedService?.userData?.group) === '3') {
      this.headerTitle = 'TITLES.PLAN_CARE_CALENDAR';
      this.patientPermission = true;
    }
    this.createOrModifyVisitPermission =
      this.permissionService.userHasPermission(Permissions.allowStaffToScheduleForThemselves) ||
      this.permissionService.userHasPermission(Permissions.manageVisitSchedule);

    this.updateConfigPermissions();
    this.sharedService.configValuesUpdated.subscribe(() => {
      this.updateConfigPermissions();
    });
    this.checkPersistentFiltersApplied();
  }

  updateConfigPermissions(): void {
    this.userPrivilege = this.permissionService.userHasPermission(Permissions.viewAllVisitSchedule) ? true : false;
  }

  userChange(value) {
    this.selectedVisit = value;
    if (this.selectedVisit === Constants.myVisit) {
      this.visitsParam.calendarType = Constants.myVisit;
      this.headerTitle = 'TITLES.MY_VISITS';
    } else {
      this.visitsParam.calendarType = Constants.allVisit;
      this.headerTitle = 'TITLES.ALL_VISITS';
    }
    this.checkPersistentFiltersApplied();
    this.getVisits();
  }

  /** getVisits() for get all visits*/
  getVisits() {
    this.sharedService.isLoading = true;
    this.commonService.notifySearchFilterApplied(this.showFilterNotification);
    this.visitScheduleService.getVisits(this.visitsParam).subscribe(
      (item) => {
        this.showFilterNotification = false;
        this.items = [];
        if (this.isFilterApplied && isBlank(item.data)) {
          const message = this.commonService.getTranslateData('MESSAGES.FILTER_DATA_MESSAGE');
          this.commonService.showMessage(message);
        }
        if (!isBlank(item.data)) {
          this.items = item.data.map((x): IcalendarData => {
            return {
              backgroundColor: x.backgroundColor,
              textColor: x.textColor,
              borderColor: x.textColor,
              start: convertFromTzToTz(
                formatDate(x.start, Constants.dateFormat.ymd),
                formatDate(x.start, Constants.dateFormat.hhmm0),
                x.locationTimeZone,
                this.visitsTimezone
              ),
              end: convertFromTzToTz(
                formatDate(x.end, Constants.dateFormat.ymd),
                formatDate(x.end, Constants.dateFormat.hhmm0),
                x.locationTimeZone,
                this.visitsTimezone
              ),
              startDate: x.startDate,
              endDate: x.endDate,
              id: `${x.id}_${x.startDate}_${x.endDate}`,
              siteName: x.siteName,
              staffVisitStatus: x.staffVisitStatus,
              status: x.status,
              patientVisitStatus: x.patientVisitStatus,
              visitStatus: x.visitStatus,
              visitKey: x.visitKey,
              staffName: x.staffName,
              visitTitle: x.eventVisitTitle,
              eventId: x.id,
              patientName: x.patientName,
              title: x.title,
              timeZone: this.visitsTimezone,
              createdBy: x.createdBy,
              repeatType: x.repeatType,
              visitationDetails: x.visitationDetails,
              visitTherapyName: x.visitTherapyName,
              visitTherapy: x.visitTherapy,
              chairName: x.chairName,
              visitLocationName: x.visitLocationName,
              clinicianNames: this.getClinicianNames(x),
              visitType: x.visitType,
              visitStaffData: x.visitStaffData
            };
          });
        }
        this.sharedService.isLoading = false;
      },
      (error) => {
        this.sharedService.isLoading = false;
        this.commonService.showToast({
          message: error?.error || this.commonService.getTranslateData('ERROR_MESSAGES.SOMETHING_WENT_WRONG'),
          color: 'danger'
        });
        this.showFilterNotification = false;
      }
    );
  }

  onColorCodingCategoryChange() {
    this.visitsParam = {
      ...this.visitsParam,
      colorCodingCategory: this.colorCodingCategory
    };
    this.getVisits();
  }

  loadTimeZones() {
    this.sharedService.getAllTimeZones().subscribe({
      next: (timezones: TimeZones) => {
        if (timezones && timezones.length) {
          this.timeZones = timezones;
          // Set device timezone if not already set from persistent data or user default
          if (!this.persistentService.getPersistentData('visitsTimezone') && !this.userData.userVisitDefaultTimezone) {
            const deviceTimezone = this.getDeviceTimezone();
            if (deviceTimezone) {
              this.visitsTimezone = deviceTimezone;
            }
          }
          this.cdr.detectChanges(); // Trigger change detection
        }
      },
      error: (error) => {
        this.timeZones = [];
        this.cdr.detectChanges(); // Trigger change detection
      }
    });
  }

  onTimezoneChange() {
    this.persistentService.setPersistentData('visitsTimezone', this.visitsTimezone);
    if (this.calendarDateSelection) {
      this.updateVisitsDateRange(this.calendarDateSelection);
    }
    this.getVisits();
  }

  /**
   * Detects device timezone and matches it with available timezones by offset
   */
  private getDeviceTimezone(): string | null {
    try {
      const deviceTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      const currentOffset = this.getCurrentOffset(deviceTimezone);

      if (typeof currentOffset === 'number' && this.timeZones && this.timeZones.length) {
        // Find matching timezone in the available list by offset
        const matchingTimezone = this.timeZones.find((timezone) => {
          try {
            const timezoneOffset = this.getCurrentOffset(timezone.city);
            return timezoneOffset === currentOffset;
          } catch {
            return false;
          }
        });

        if (matchingTimezone) {
          return matchingTimezone.city;
        }
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error detecting device timezone:', error);
    }

    return null;
  }

  private getCurrentOffset(tz: string): number {
    return moment.tz(tz).utcOffset();
  }

  /** Initial calendar event binding*/
  calendarInit(data: any): void {
    if (data.operation === this.datesSet) {
      this.calendarDateSelection = data.param;
      this.updateVisitsDateRange(this.calendarDateSelection);
      this.getVisits();
    } else if (data.operation === Constants.eventClick) {
      this.popoverCtrl
        .create({
          component: ViewModifyEventPage,
          cssClass: 'event-details-popover',
          componentProps: {
            visitDetails: data,
            createModifyOwnVisitPermission: this.permissionService.userHasPermission(Permissions.allowStaffToScheduleForThemselves),
            allowRoleVisitPermission: this.permissionService.userHasPermission(Permissions.manageVisitSchedule)
          },
          translucent: true
        })
        .then((popoverElement) => {
          popoverElement.present();
        });
    } else if (data.operation === 'dateClick') {
      this.navController.navigateForward('schedule-center/visits/create-visit', {
        state: {
          selectedDateTime: data.dateTimeParam
        }
      });
    }
  }

  updateVisitsDateRange(calendarDateSelection: { startIntervalTime: string; endIntervalTime: string }): void {
    this.visitsParam.startDate = convertTimeZoneDateTimeToUTCIso(calendarDateSelection.startIntervalTime, '00:00:00', this.visitsTimezone);
    this.visitsParam.endDate = convertTimeZoneDateTimeToUTCIso(calendarDateSelection.endIntervalTime, '23:59:59', this.visitsTimezone);
  }

  /** rediecting to create visit */
  goToVisit() {
    this.navController.navigateForward('schedule-center/visits/create-visit');
  }

  async showFilter() {
    const modal = await this.modalController.create({
      component: VisitFilterPage,
      cssClass: 'common-advanced-select',
      componentProps: {
        mySites: this.mySites,
        clinicianId: this.clinicianId,
        clinicianName: this.clinicianName,
        visitationTypes: this.visitationTypes,
        title: this.title,
        schedules: this.schedules,
        patientID: this.patientID ? this.patientID : '',
        patientName: this.patientName,
        selectedVisitLocation: this.visitTypeLocation,
        selectedVisitChair: this.visitChair,
        calendarType: this.visitsParam.calendarType
      }
    });
    modal.onDidDismiss().then((modelData) => {
      if (!isBlank(modelData.data)) {
        this.isFilterApplied = true;
        this.mySites = modelData.data.mySites;
        this.clinicianId = modelData.data.clinicianId;
        this.clinicianName = modelData.data.clinicianName;
        this.visitationTypes = modelData.data.types;
        this.title = modelData.data.title;
        this.schedules = modelData.data.schedules;
        this.patientID = modelData.data.patientID;
        this.patientName = modelData.data.patientName;
        this.visitTypeLocation = modelData.data.visitTypeLocation;
        this.visitChair = modelData.data.visitChair;
        this.getFilterControlSelected();
      } else {
        this.isFilterApplied = false;
      }
    });
    return await modal.present();
  }

  checkSeleced(selectedData) {
    return selectedData
      .filter((row) => row.selected)
      .map((item) => item.id)
      .join(',');
  }

  //filter based calendar data bind
  getFilterControlSelected() {
    const selectedSiteId = !isBlank(this.mySites) ? this.checkSeleced(this.mySites) : '';
    const selectedschedulesId = !isBlank(this.schedules) ? this.checkSeleced(this.schedules) : '';
    const selectedStaffClinician = this.clinicianId ? this.clinicianId : '';
    const selectedVisitationTypesId = !isBlank(this.visitationTypes) ? this.checkSeleced(this.visitationTypes) : '';

    if (selectedVisitationTypesId.toString() === VisitType.Facility || selectedVisitationTypesId.toString() === VisitType.Home) {
      this.visitChair = {};
      this.visitTypeLocation = {};
    }
    this.visitsParam = {
      ...this.visitsParam,
      status: selectedschedulesId ? selectedschedulesId : '',
      type: selectedVisitationTypesId ? selectedVisitationTypesId : '',
      title: this.title,
      siteId: selectedSiteId ? selectedSiteId : '',
      staffId: selectedStaffClinician ? selectedStaffClinician : '',
      patientId: this.patientID ? this.patientID : '',
      visitTypeLocation: Array.isArray(this.visitTypeLocation) ? this.visitTypeLocation?.map((item) => item.id).join(',') : '',
      visitChair: Array.isArray(this.visitChair) ? this.visitChair?.map((item) => item.id).join(',') : ''
    };
    this.getVisits();
  }

  ionViewWillEnter() {
    if (this.pageRender) {
      this.getFilterControlSelected();
    }
    if (!this.isFilterApplied) {
      this.mySites = JSON.parse(JSON.stringify(this.sharedService.getSites()));
    }
  }

  ionViewWillLeave() {
    this.pageRender = true;
    if (!this.isFilterApplied) {
      this.mySites = [];
      this.clinicianId = '';
      this.clinicianName = '';
      this.visitationTypes = [];
      this.title = '';
      this.schedules = Constants.schedules;
      this.patientID = '';
      this.patientName = '';
      this.visitTypeLocation = {};
      this.visitChair = {};
    }
  }

  /**
   * Checks if persistent filters are applied and updates the filter data accordingly.
   * This method performs the following actions:
   * 1. Retrieves the current filter data.
   * 2. Sets the filter data.
   * 3. Updates the visits parameters.
   * 4. Determines if any filter fields are applied and shows a filter notification if necessary.
   * @returns {void}
   */
  checkPersistentFiltersApplied(): void {
    const filterData = this.getFilterData();
    this.setFilterData(filterData);
    this.setVisitsParam();
    this.showFilterNotification = this.checkForFilteredFields(filterData);
  }

  /**
   * Retrieves filter data based on the current calendar type.
   * This method determines the appropriate storage key based on whether the
   * calendar type is 'myVisit' or 'allVisit', and then fetches the persistent
   * data associated with that key
   * @returns {any} The persistent data associated with the determined storage key.
   */
  getFilterData(): any {
    const storeKey =
      this.visitsParam.calendarType === Constants.myVisit ? Constants.storageKeys.myVisitFilterKeys : Constants.storageKeys.allVisitFilterKeys;
    return this.persistentService.getPersistentData(storeKey);
  }
  /** Check for at least one of the filter items does contain a valid value
   * @param filterData - filter data
   * @returns boolean
   */
  checkForFilteredFields(filterData: any): boolean {
    return (
      !isBlank(filterData) &&
      (!isBlank(filterData.patientID) ||
        filterData?.types?.some((type) => type.selected) ||
        filterData?.mySites?.some((site) => site.selected) ||
        !isBlank(filterData?.clinicianId) ||
        filterData?.schedules?.some((schedule) => schedule.selected) ||
        !isBlank(filterData?.title) ||
        !isBlank(filterData?.visitTypeLocation) ||
        !isBlank(filterData?.visitChair))
    );
  }

  /**
   * Sets the filter data for the visits page.
   * @param filterData - An object containing filter data.
   * @returns {void}
   */
  setFilterData(filterData: any): void {
    this.mySites = filterData?.mySites || JSON.parse(JSON.stringify(this.sharedService.getSites()));
    this.clinicianId = filterData?.clinicianId || '';
    this.clinicianName = filterData?.staffClinicianName || '';
    this.visitationTypes = filterData?.types || '';
    this.title = filterData?.title || '';
    this.schedules = filterData?.schedules || Constants.schedules;
    this.patientID = filterData?.patientID || '';
    this.patientName = filterData?.patientName || '';
    this.visitTypeLocation = filterData?.visitTypeLocation || {};
    this.visitChair = filterData?.visitChair || {};
  }

  /**
   * Sets the parameters for visits based on the current state of the component.
   * This method updates the `visitsParam` object with various properties derived from the component's state.
   * It includes status, type, title, siteId, staffId, patientId, visitTypeLocation, and visitChair.
   * The method checks if certain properties are not blank and assigns values accordingly.
   * @returns {void}
   */
  setVisitsParam(): void {
    this.visitsParam = {
      ...this.visitsParam,
      status: !isBlank(this.schedules) ? this.checkSeleced(this.schedules) : '',
      type: !isBlank(this.visitationTypes) ? this.checkSeleced(this.visitationTypes) : '',
      title: this.title,
      siteId: !isBlank(this.mySites) ? this.checkSeleced(this.mySites) : '',
      staffId: this.clinicianId,
      patientId: this.patientID,
      visitTypeLocation: this.visitTypeLocation?.id ? this.visitTypeLocation?.id : '',
      visitChair: this.visitChair?.id ? this.visitChair?.id : ''
    };
  }
  getClinicianNames(event) {
    const clinicianNames =
      Array.isArray(event.visitStaffData) && event.visitStaffData.length > 0
        ? event.visitStaffData
            .map((s) => s.displayName)
            .filter(Boolean)
            .join(', ')
        : event.staffName;
    return clinicianNames;
  }
}
