<app-header [headerTitle]="headerTitle"></app-header>
<div class="row">
  <ion-row class="segment-top">
    <ion-segment
      [value]="selectedVisit"
      mode="ios"
      (ionChange)="userChange(segment.value)"
      id="user-segment"
      #segment
      *ngIf="userPrivilege && !patientPermission"
    >
      <ion-segment-button [value]="myVisits" id="my-visits-button">
        <ion-label>{{ 'LABELS.MY_VISIT_SCHEDULE' | translate }}</ion-label>
      </ion-segment-button>
      <ion-segment-button [value]="allVisit" id="all-visits-button">
        <ion-label>{{ 'LABELS.ALL_VISIT_SCHEDULES' | translate }}</ion-label>
      </ion-segment-button>
    </ion-segment>
  </ion-row>
  <div class="calendarFilters" *ngIf="!patientPermission">
    <ion-row>
      <ion-col size="6">
        <ion-button (click)="showFilter()" color="de-york" class="ion-float-left ion-text-capitalize" id="show-filter">
          <ion-icon name="options-sharp"></ion-icon>
          <span class="filterbutton">{{ 'BUTTONS.FILTER' | translate }}</span>
        </ion-button>
      </ion-col>
      <ion-col *ngIf="createOrModifyVisitPermission" size="6">
        <ion-button (click)="goToVisit()" color="de-york" class="ion-float-right ion-text-capitalize" id="go-to-visit">
          <span class="filterbutton">{{ 'BUTTONS.CREATE_VISIT' | translate }}</span>
        </ion-button>
      </ion-col>
    </ion-row>
  </div>
  <div class="calendar">
    <app-calendar
      [item]="items"
      (parentFunction)="calendarInit($event)"
      [visitPage]="visitPageName"
      [visitsTimezone]="visitsTimezone"
      [visitsPreference]="selectedVisit"
      id="calendar"
    >
    </app-calendar>
  </div>
  <div class="time-zone-footer">
    <div class="dropdown-wrapper">
      <ion-text class="dropdown-label" color="black">{{ 'LABELS.VISITS_SHOWN_IN' | translate }}:&nbsp;</ion-text>
      <ion-select
        id="timezone"
        [(ngModel)]="visitsTimezone"
        *ngIf="timeZones"
        class="form-field-right common-input"
        mode="md"
        (ionChange)="onTimezoneChange()"
        [interfaceOptions]="{ header: 'LABELS.VISITS_SHOWN_IN' | translate }"
      >
        <ion-select-option *ngFor="let option of timeZones" [value]="option?.city">{{ option?.name | translate }} </ion-select-option>
      </ion-select>
    </div>
    <div class="dropdown-wrapper">
      <ion-text class="dropdown-label" color="black">{{ 'LABELS.COLOR_CODING_BY' | translate }}:&nbsp;</ion-text>
      <ion-select
        id="color-coding-select"
        [(ngModel)]="colorCodingCategory"
        class="form-field-right common-input"
        mode="md"
        (ionChange)="onColorCodingCategoryChange()"
        [interfaceOptions]="{ header: 'LABELS.COLOR_CODING_BY' | translate }"
      >
        <ion-select-option value="1">Default (Visit Status)</ion-select-option>
        <ion-select-option value="2">Therapy Type</ion-select-option>
        <ion-select-option value="3">Staff/Partner/Resources</ion-select-option>
      </ion-select>
    </div>
  </div>
</div>
<app-footer backButtonLink="home"></app-footer>
