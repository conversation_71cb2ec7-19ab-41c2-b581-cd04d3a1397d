import { TranslateModule } from '@ngx-translate/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AlertController, IonicModule, PopoverController } from '@ionic/angular';

import { ViewModifyEventPage } from './view-modify-event.page';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { CommonService } from 'src/app/services/common-service/common.service';
import { PermissionService } from 'src/app/services/permission-service/permission.service';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { Keepalive } from '@ng-idle/keepalive';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { TestConstants } from 'src/app/constants/test-constants';
import { Constants } from 'src/app/constants/constants';
import { VisitScheduleConstants } from 'src/app/constants/visit-schedule-constants';

describe('ViewModifyEventPage', () => {
  let component: ViewModifyEventPage;
  let fixture: ComponentFixture<ViewModifyEventPage>;
  let popoverController: PopoverController;
  let router: Router;
  let sharedService: SharedService;
  let permissionService: PermissionService;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [ViewModifyEventPage],
      imports: [IonicModule.forRoot(), TranslateModule.forRoot(), RouterModule.forRoot([]), HttpClientModule, HttpClientTestingModule],
      providers: [
        PopoverController,
        SharedService,
        CommonService,
        PermissionService,
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        AlertController,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    popoverController = TestBed.inject(PopoverController);
    spyOn(popoverController, 'dismiss').and.stub();
    router = TestBed.inject(Router);
    spyOn(router, 'navigate').and.stub();
    sharedService = TestBed.inject(SharedService);
    sharedService.userData = TestConstants.userData;
    permissionService = TestBed.inject(PermissionService);
    fixture = TestBed.createComponent(ViewModifyEventPage);
    component = fixture.componentInstance;
    component.visitDetails = {
      eventId: '1',
      end: '2022-12-07T02:00:00',
      start: '2022-12-07T01:00:00',
      visitKey: '35a71ea8-ecda-42f4-bbad-f84e111bc0e7',
      timeZone: 'Asia/Kolkata',
      visitStaffData: [
        { userId: '123', name: 'Test Staff' },
        { userId: '456', name: 'Another Staff' }
      ],
      createdBy: '123'
    };
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  it('execute view visit', () => {
    component.onVisitAction('view');
    expect(component.onVisitAction).toBeTruthy();
  });
  it('execute modify visit', () => {
    component.onVisitAction('create-visit');
    expect(component.onVisitAction).toBeTruthy();
  });
  it('execute closePopover', () => {
    component.closePopover();
    expect(component.closePopover).toBeTruthy();
  });
  it('should call ionViewWillEnter', () => {
    component.ionViewWillEnter();
    expect(component.isLoginUserIdAndCreatedVisitIdSame).toBeDefined();
  });

  it('should call showModifyButton', () => {
    component.showModifyButton();
    expect(component.showModifyButton).toBeDefined();
  });

  it('should call showModifyButton allowRoleVisitPermission= true createModifyOwnVisitPermission=false', () => {
    component.allowRoleVisitPermission = true;
    component.createModifyOwnVisitPermission = true;
    component.showModifyButton();
    expect(component.isShowModifyButton).toBe(true);
  });

  it('should call showModifyButton allowRoleVisitPermission= false createModifyOwnVisitPermission=false', () => {
    component.allowRoleVisitPermission = false;
    component.createModifyOwnVisitPermission = false;
    component.showModifyButton();
    expect(component.isShowModifyButton).toBe(false);
  });

  it('should call showModifyButton allowRoleVisitPermission= false createModifyOwnVisitPermission=true isLoginUserIdAndCreatedVisitIdSame=true', () => {
    component.allowRoleVisitPermission = false;
    component.createModifyOwnVisitPermission = true;
    component.isLoginUserIdAndCreatedVisitIdSame = true;
    component.showModifyButton();
    expect(component.isShowModifyButton).toBe(true);
  });

  it('should call showModifyButton allowRoleVisitPermission= true createModifyOwnVisitPermission=false isLoginUserIdAndCreatedVisitIdSame=true', () => {
    component.allowRoleVisitPermission = true;
    component.createModifyOwnVisitPermission = false;
    component.isLoginUserIdAndCreatedVisitIdSame = true;
    component.showModifyButton();
    expect(component.isShowModifyButton).toBe(false);
  });

  it('should call showModifyButton allowRoleVisitPermission= true createModifyOwnVisitPermission=false isLoginUserIdAndCreatedVisitIdSame=false', () => {
    component.allowRoleVisitPermission = true;
    component.createModifyOwnVisitPermission = false;
    component.isLoginUserIdAndCreatedVisitIdSame = false;
    component.showModifyButton();
    expect(component.isShowModifyButton).toBe(true);
  });

  // Tests for new permission functionality - CHP-20453
  describe('CHP-20453: Allow edit of Completed Visits Permission', () => {
    beforeEach(() => {
      // Set up permission service spy
      spyOn(permissionService, 'userHasPermission').and.callFake((permission: string) => {
        switch (permission) {
          case 'allowEditCompletedVisits':
            return component.allowEditCompletedVisitsPermission;
          default:
            return false;
        }
      });
    });

    it('should check if visit is completed correctly', () => {
      component.visitDetails = {
        staffVisitStatus: Constants.staffVisitStatus[4].text,
        patientVisitStatus: Constants.patientVisitStatus[4].text,
        staffVisitStatusId: Constants.staffVisitStatus[4].value,
        patientVisitStatusId: Constants.patientVisitStatus[4].value
      };
      expect(component.isVisitCompleted()).toBe(true);

      component.visitDetails = {
        staffVisitStatus: Constants.staffVisitStatus[2].text,
        patientVisitStatus: Constants.patientVisitStatus[2].text
      };
      expect(component.isVisitCompleted()).toBe(false);
    });

    it('should check if visit is review completed correctly', () => {
      component.visitDetails = {
        visitStatusId: VisitScheduleConstants.reviewCompleted
      };
      expect(component.isVisitReviewCompleted()).toBe(true);

      component.visitDetails = {
        visitStatusId: '1'
      };
      expect(component.isVisitReviewCompleted()).toBe(false);
    });

    it('should allow editing completed visits when user has permission and create/modify permission', () => {
      component.allowEditCompletedVisitsPermission = true;
      component.createModifyOwnVisitPermission = true;
      component.allowRoleVisitPermission = false;

      expect(component.canEditCompletedVisits()).toBe(true);
    });

    it('should not allow editing completed visits when user lacks the new permission', () => {
      component.allowEditCompletedVisitsPermission = false;
      component.createModifyOwnVisitPermission = true;
      component.allowRoleVisitPermission = true;

      expect(component.canEditCompletedVisits()).toBe(false);
    });

    it('should not show modify button for review completed visits regardless of permissions', () => {
      component.allowEditCompletedVisitsPermission = true;
      component.createModifyOwnVisitPermission = true;
      component.allowRoleVisitPermission = true;
      component.visitDetails = {
        visitStatusId: VisitScheduleConstants.reviewCompleted
      };

      expect(component.shouldShowModifyButton()).toBe(false);
    });

    it('should show modify button for completed visits when user has the new permission', () => {
      component.allowEditCompletedVisitsPermission = true;
      component.createModifyOwnVisitPermission = true;
      component.allowRoleVisitPermission = true;
      component.isLoginUserIdAndCreatedVisitIdSame = true;
      component.visitDetails = {
        staffVisitStatus: Constants.staffVisitStatus[4].text,
        patientVisitStatus: Constants.patientVisitStatus[4].text,
        visitStatusId: '1' // Not review completed
      };

      expect(component.shouldShowModifyButton()).toBe(true);
    });

    it('should not show modify button for completed visits when user lacks the new permission', () => {
      component.allowEditCompletedVisitsPermission = false;
      component.createModifyOwnVisitPermission = true;
      component.allowRoleVisitPermission = true;
      component.isLoginUserIdAndCreatedVisitIdSame = true;
      component.visitDetails = {
        staffVisitStatus: Constants.staffVisitStatus[4].text,
        patientVisitStatus: Constants.patientVisitStatus[4].text,
        visitStatusId: '1'
      };

      expect(component.shouldShowModifyButton()).toBe(false);
    });

    it('should show modify button for non-completed visits regardless of new permission', () => {
      component.allowEditCompletedVisitsPermission = false;
      component.createModifyOwnVisitPermission = true;
      component.allowRoleVisitPermission = false;
      component.isLoginUserIdAndCreatedVisitIdSame = true;
      component.visitDetails = {
        staffVisitStatus: Constants.staffVisitStatus[2].text, // Confirmed, not completed
        patientVisitStatus: Constants.patientVisitStatus[2].text,
        visitStatusId: '1'
      };

      expect(component.shouldShowModifyButton()).toBe(true);
    });
  });
});
