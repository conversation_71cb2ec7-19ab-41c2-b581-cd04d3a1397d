import { Component } from '@angular/core';
import { NavigationExtras, Router } from '@angular/router';
import { PopoverController } from '@ionic/angular';
import { Constants, VisitScheduleStatus } from 'src/app/constants/constants';
import { PageRoutes } from 'src/app/constants/page-routes';
import { Permissions } from 'src/app/constants/permissions';
import { VisitScheduleConstants } from 'src/app/constants/visit-schedule-constants';
import { PermissionService } from 'src/app/services/permission-service/permission.service';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { formatDate } from 'src/app/utils/utils';

@Component({
  selector: 'app-view-modify-event',
  templateUrl: './view-modify-event.page.html',
  styleUrls: ['./view-modify-event.page.scss']
})
export class ViewModifyEventPage {
  visitDetails: any;
  siteTimeZone: string;
  visitScheduleStatus = VisitScheduleStatus;
  createModifyOwnVisitPermission: boolean;
  allowRoleVisitPermission: boolean;
  allowEditCompletedVisitsPermission: boolean;
  isLoginUserIdAndCreatedVisitIdSame: boolean;
  isLoginUserIdAndAssignedVisitIdSame: boolean;
  isShowModifyButton: boolean;
  Constants = Constants;
  isStaff = false;
  constructor(
    private popover: PopoverController,
    private router: Router,
    public sharedService: SharedService,
    private permissionService: PermissionService
  ) { }

  ionViewWillEnter() {
    this.isStaff = Number(this.sharedService.userData.group) === Constants.staffGroupId;
    this.isLoginUserIdAndCreatedVisitIdSame = Number(this.visitDetails.createdBy) === Number(this.sharedService.userData.userId);

    // Use permissions passed from parent component if available, otherwise fallback to permission service
    // This maintains consistency with mobile repository implementation
    if (this.allowEditCompletedVisitsPermission === undefined) {
      this.allowEditCompletedVisitsPermission = this.permissionService.userHasPermission(Permissions.allowEditCompletedVisits);
    }
    if (this.createModifyOwnVisitPermission === undefined) {
      this.createModifyOwnVisitPermission = this.permissionService.userHasPermission(Permissions.allowStaffToScheduleForThemselves);
    }
    if (this.allowRoleVisitPermission === undefined) {
      this.allowRoleVisitPermission = this.permissionService.userHasPermission(Permissions.manageVisitSchedule);
    }
    this.visitDetails.visitStaffData.forEach((staff) => {
      if (Number(staff.userId) === Number(this.sharedService.userData.userId)) {
        this.isLoginUserIdAndAssignedVisitIdSame = true;
      }
    });

    this.showModifyButton();
  }

  isVisitCompleted(): boolean {
    const staffStatusCompleted = this.visitDetails?.staffVisitStatus === Constants.staffVisitStatus[4].text ||
                                 Number(this.visitDetails?.staffVisitStatusId) === Constants.staffVisitStatus[4].value;
    const patientStatusCompleted = this.visitDetails?.patientVisitStatus === Constants.patientVisitStatus[4].text ||
                                   Number(this.visitDetails?.patientVisitStatusId) === Constants.patientVisitStatus[4].value;
    return staffStatusCompleted && patientStatusCompleted;
  }

  isVisitReviewCompleted(): boolean {
    return this.visitDetails?.visitStatusId === VisitScheduleConstants.reviewCompleted ||
           this.visitDetails?.VisitStatus === VisitScheduleStatus.REVIEW_COMPLETED;
  }

  canEditCompletedVisits(): boolean {
    if (!this.allowEditCompletedVisitsPermission) {
      return false;
    }

    const hasCreateModifyPermission = this.createModifyOwnVisitPermission || this.allowRoleVisitPermission;

    return hasCreateModifyPermission;
  }

  shouldShowModifyButton(): boolean {
    if (this.isVisitReviewCompleted()) {
      return false;
    }

    let hasBasicPermission = false;
    if (this.allowRoleVisitPermission && this.createModifyOwnVisitPermission) {
      hasBasicPermission = true;
    } else if (this.allowRoleVisitPermission) {
      if (this.createModifyOwnVisitPermission && (this.isLoginUserIdAndCreatedVisitIdSame || this.isLoginUserIdAndAssignedVisitIdSame)) {
        hasBasicPermission = true;
      } else if (!this.createModifyOwnVisitPermission && !(this.isLoginUserIdAndCreatedVisitIdSame || this.isLoginUserIdAndAssignedVisitIdSame)) {
        hasBasicPermission = true;
      }
    } else if (this.createModifyOwnVisitPermission && (this.isLoginUserIdAndCreatedVisitIdSame || this.isLoginUserIdAndAssignedVisitIdSame)) {
      hasBasicPermission = true;
    } else if (this.isLoginUserIdAndAssignedVisitIdSame) {
      hasBasicPermission = true;
    }

    if (!hasBasicPermission) {
      return false;
    }
    if (!this.isVisitCompleted()) {
      return true;
    }
    return this.canEditCompletedVisits();
  }

  showModifyButton() {
    this.isShowModifyButton = this.shouldShowModifyButton();
  }
  onVisitAction(action: string, editType?) {
    this.visitDetails = {
      ...this.visitDetails,
      start: formatDate(this.visitDetails.start, Constants.dateFormat.ymd),
      end: formatDate(this.visitDetails.end, Constants.dateFormat.ymd),
      startTime: formatDate(this.visitDetails.start, Constants.dateFormat.hhmm0),
      endTime: formatDate(this.visitDetails.end, Constants.dateFormat.hhmm0)
    };

    this.popover.dismiss();
    if (action === 'view') {
      const navigationExtras = {
        state: {
          data: {
            visitKey: this.visitDetails.visitKey,
            visitCalendarData: this.visitDetails,
            disableCalendarView: false
          }
        }
      };
      this.router.navigate([`${PageRoutes.scheduleCenterVisitsView}/${this.visitDetails.visitKey}`], navigationExtras);
    } else {
      const navigationExtras: NavigationExtras = {
        state: {
          data: this.visitDetails,
          editType
        }
      };
      this.router.navigate([PageRoutes.createVisit, this.visitDetails.visitKey], navigationExtras);
    }
  }
  closePopover() {
    this.popover.dismiss();
  }
}
