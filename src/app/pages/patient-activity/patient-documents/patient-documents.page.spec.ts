import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { Apollo } from 'apollo-angular';
import { TranslateModule } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { RouterModule } from '@angular/router';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule, ModalController } from '@ionic/angular';
import { InAppBrowser } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { PatientDocumentsPage } from 'src/app/pages/patient-activity/patient-documents/patient-documents.page';
import { TestConstants } from 'src/app/constants/test-constants';
import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { of, throwError } from 'rxjs';

describe('PatientDocumentsPage', () => {
  let component: PatientDocumentsPage;
  let sharedService: SharedService;
  let graphqlService: GraphqlService;
  let fixture: ComponentFixture<PatientDocumentsPage>;
  let modalController: ModalController;
  const { modalSpy } = TestConstants;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [PatientDocumentsPage],
      imports: [IonicModule.forRoot(), RouterModule.forRoot([]), HttpClientModule, HttpClientTestingModule, TranslateModule.forRoot()],
      providers: [
        Apollo,
        NgxPermissionsService,
        NgxPermissionsStore,
        SharedService,
        Idle,
        IdleExpiry,
        Keepalive,
        ModalController,
        SharedService,
        InAppBrowser,
        GraphqlService,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ]
    }).compileComponents();
    sharedService = TestBed.inject(SharedService);
    graphqlService = TestBed.inject(GraphqlService);
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    modalSpy.onDidDismiss.and.resolveTo({ data: 'fakeData' });
    fixture = TestBed.createComponent(PatientDocumentsPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });
  afterEach(() => {
    if (fixture.nativeElement && 'remove' in fixture.nativeElement) {
      (fixture.nativeElement as HTMLElement).remove();
    }
  });
  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call viewDocument', () => {
    const itemData = {
      description: 'test',
      createdAt: '25-05-2022',
      modifiedAt: '25-05-2022',
      docNotes: 'Testing',
      docCategory: '',
      fileName: 'demo.pdf',
      docFileUrl: '',
      docSource: '',
      __typename: ''
    };
    component.viewDocument(itemData);
    fixture.detectChanges();
    expect(component.viewDocument).toBeTruthy();
  });

  it('should call clearData', () => {
    component.clearData();
    expect(component.clearData).toBeTruthy();
  });

  it('should call searchData', () => {
    component.searchData();
    expect(component.searchData).toBeTruthy();
  });

  it('execute presentAdvancedViewerModal', () => {
    component.presentAdvancedViewerModal({});
    expect(component.presentAdvancedViewerModal).toBeTruthy();
  });
  it('execute viewDocument', () => {
    spyOn(sharedService, 'presentPdfFromLink').and.resolveTo({ status: true, url: 'test' });
    component.viewDocument({
      description: '',
      createdAt: '',
      modifiedAt: '',
      docNotes: '',
      docCategory: '',
      fileName: '',
      docFileUrl: '',
      docSource: '',
      __typename: ''
    });
    modalController.dismiss();
    expect(component.viewDocument).toBeTruthy();
  });

  it('should call loadPatientDocuments with data', () => {
    const mockResponse = {
      loading: false,
      data: {
        getSessionTenant: {
          patientDocuments: {
            data: [
              { id: 1, name: 'Document 1' },
              { id: 2, name: 'Document 2' }
            ]
          }
        }
      },
      networkStatus: 1,
      stale: false
    };

    spyOn(graphqlService, 'patientDocuments').and.returnValue(of(mockResponse));
    component.loadPatientDocuments();
    expect(component.documentsData.length).toBe(2);
    expect(sharedService.isLoading).toBeFalse();
  });

  it('should call loadPatientDocuments without data', () => {
    const mockResponse = {
      loading: false,
      data: {
        getSessionTenant: {
          patientDocuments: {
            data: []
          }
        }
      },
      networkStatus: 1,
      stale: false
    };

    spyOn(graphqlService, 'patientDocuments').and.returnValue(of(mockResponse));
    component.loadPatientDocuments();
    expect(component.documentsData.length).toBe(0);
    expect(sharedService.isLoading).toBeFalse();
  });

  it('should call loadPatientDocuments : throw error', () => {
    spyOn(graphqlService, 'patientDocuments').and.returnValue(throwError(''));
    component.loadPatientDocuments();
    expect(component.documentsData.length).toBe(0);
    expect(sharedService.isLoading).toBeFalse();
  });
});
