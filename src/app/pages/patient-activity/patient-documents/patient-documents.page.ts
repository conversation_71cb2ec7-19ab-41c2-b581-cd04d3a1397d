import { AssetSource, Constants } from 'src/app/constants/constants';
import { isPresent } from 'src/app/utils/utils';
import { InAppBrowser } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { ModalController } from '@ionic/angular';
import { PatientDocuments, DocumentDataList } from './../../../interfaces/patient-activity';
import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { ActivatedRoute } from '@angular/router';
import { Component, OnInit } from '@angular/core';
import { AdvancedViewerComponent } from 'src/app/components/advanced-viewer/advanced-viewer.component';

@Component({
  selector: 'app-patient-documents',
  templateUrl: './patient-documents.page.html',
  styleUrls: ['./patient-documents.page.scss']
})
export class PatientDocumentsPage implements OnInit {
  patientID;
  paramsData = {
    sessionToken: '',
    patientId: 0,
    startRow: 0,
    endRow: 10,
    searchText: ''
  };
  documentsData = [];
  isSearch: boolean = false;
  constructor(
    private route: ActivatedRoute,
    private sharedService: SharedService,
    private graphqlService: GraphqlService,
    private browser: InAppBrowser,
    private modalController: ModalController
  ) {
    this.route.paramMap.subscribe((paramMap) => {
      this.patientID = paramMap.get('id');
      this.paramsData.sessionToken = this.sharedService?.userData?.authenticationToken;
      this.paramsData.patientId = +this.patientID;
    });
  }

  ngOnInit() {
    this.loadPatientDocuments();
  }

  loadPatientDocuments() {
    this.sharedService.isLoading = true;
    this.graphqlService?.patientDocuments(this.paramsData)?.subscribe(
      (response: PatientDocuments) => {
        this.sharedService.isLoading = false;
        if (response.data.getSessionTenant.patientDocuments.data.length) {
          this.documentsData = response.data.getSessionTenant.patientDocuments.data;
        } else {
          this.documentsData = [];
        }
      },
      () => {
        this.sharedService.isLoading = false;
      }
    );
  }

  async viewDocument(item: DocumentDataList) {
    this.sharedService.isLoading = true;
    this.sharedService.presentPdfFromLink({ url: item.docFileUrl.replace('?type=pdf', ''), source: AssetSource.CMIS }).then((data: any) => {
      this.sharedService.isLoading = false;
      if (data.status && isPresent(data.url)) {
        if (this.sharedService.platform.is('ios') && this.sharedService.platform.is('capacitor')) {
          this.browser.create(data.url, '_blank', this.sharedService.inAppBrowserOptions());
        } else {
          this.presentAdvancedViewerModal({ url: data.url, type: Constants.documentTypes.pdf, component: AdvancedViewerComponent });
        }
      } else if (this.sharedService.platform.is('capacitor')) {
        this.browser.create(item.docFileUrl, '_system');
      }
    });
  }
  async presentAdvancedViewerModal(componentProps) {
    const modal = await this.modalController.create({
      component: AdvancedViewerComponent,
      componentProps
    });
    return modal.present();
  }

  clearData() {
    (this.paramsData.startRow = 0), (this.paramsData.endRow = 10);
    this.isSearch = !this.isSearch;
    this.paramsData.searchText = '';
    this.loadPatientDocuments();
  }

  searchData() {
    (this.paramsData.startRow = 0), (this.paramsData.endRow = 10);
    this.loadPatientDocuments();
  }
}
