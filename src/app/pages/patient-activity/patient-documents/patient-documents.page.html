<app-header headerTitle="LABELS.DOCUMENTS"></app-header>

<ion-content class="patientDocuments">
    <ng-container *ngIf="documentsData?.length !== 0">
        <ion-item lines="none" *ngIf="!isSearch" class="set-left-padding">
            <ion-label class="details-title">{{'TITLES.SELECT_DOCUMENT_VIEW_DETAILS' | translate}}</ion-label>
            <ion-icon slot="end" name="search" id="search-icon" color="de-york" (click)="isSearch =! isSearch">
            </ion-icon>
        </ion-item>
        <ion-item *ngIf="isSearch" lines="none" class="set-left-padding">
            <ion-icon slot="start" name="close" (click)="clearData()" color="de-york" class="removeSpace"
                id="clear-data"></ion-icon>
            <ion-searchbar [placeholder]="'PLACEHOLDERS.SEARCH' | translate" [debounce]="250" mode="ios" name="search"
                [(ngModel)]="paramsData.searchText" id="search-box"></ion-searchbar>
            <ion-icon slot="end" name="search" (click)="searchData()" id="search-data" color="de-york"></ion-icon>
        </ion-item>
    </ng-container>

    <ion-row *ngIf="documentsData?.length === 0">
        <ion-col size="12" class="ion-text-center">
            <ion-text color="de-york">
                {{'MESSAGES.NO_DATA_AVAILABLE' | translate}}
            </ion-text>
        </ion-col>
    </ion-row>

    <ion-accordion-group>
        <ion-accordion [value]="'accordion-'+i" *ngFor="let item of documentsData; let i=index">
            <ion-item slot="header">
                <ion-label>{{item?.description}}
                    <p>{{item?.modifiedAt | date:'MM/dd/yyyy'}}</p>
                </ion-label>
            </ion-item>
            <ion-list slot="content" class="ion-padding-start">
                <ion-row>
                    <ion-col size="4">{{'LABELS.DESC' | translate}}:</ion-col>
                    <ion-col size="8">{{item?.description}}</ion-col>
                </ion-row>
                <ion-row>
                    <ion-col size="4">{{'LABELS.FILE_NAME' | translate}}:</ion-col>
                    <ion-col size="8">{{item?.fileName}}</ion-col>
                </ion-row>
                <ion-row>
                    <ion-col size="4">{{'LABELS.CATEGORY' | translate}}:</ion-col>
                    <ion-col size="8">{{item?.docCategory}}</ion-col>
                </ion-row>
                <ion-row>
                    <ion-col size="4">{{'LABELS.SOURCE' | translate}}:</ion-col>
                    <ion-col size="8">{{item?.docSource}}</ion-col>
                </ion-row>
                <ion-row>
                    <ion-col size="4">{{'LABELS.LAST_MODIFIED' | translate}}:</ion-col>
                    <ion-col size="8">{{item?.modifiedAt | date:'MM/dd/yyyy'}}</ion-col>
                </ion-row>
                <ion-row>
                    <ion-col size="12" class="ion-text-center" (click)="viewDocument(item)" id="view-doc">
                        <ion-img src="assets/images/doc-types/pdf.png" class="setImageWidth" alt="view-doc">
                        </ion-img>
                    </ion-col>
                    <ion-col size="12" class="ion-text-center">
                        <ion-text color="de-york">
                            {{'MESSAGES.TAP_ON_VIEW_PDF' | translate}}
                        </ion-text>
                    </ion-col>
                </ion-row>
            </ion-list>
        </ion-accordion>
    </ion-accordion-group>

</ion-content>
<app-footer></app-footer>