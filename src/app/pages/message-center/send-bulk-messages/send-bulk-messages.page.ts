import { BulkMessageResponse } from 'src/app/interfaces/messages';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ActionSheetController, ModalController } from '@ionic/angular';
import { RecipientsListComponent } from '../recipients-list/recipients-list.component';
import { UntypedFormGroup, Validators, UntypedFormBuilder } from '@angular/forms';
import { HttpService } from 'src/app/services/http-service/http.service';
import { APIs } from 'src/app/constants/apis';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { CommonService } from 'src/app/services/common-service/common.service';
import { Constants, MessagePriority } from 'src/app/constants/constants';
import { isBlank } from 'src/app/utils/utils';
import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { SendBulkMessageService } from 'src/app/services/send-bulk-message-service/send-bulk-message.service';
import { SocketService } from 'src/app/services/socket-service/socket.service';
import { Activity } from 'src/app/constants/activity';

@Component({
  selector: 'app-send-bulk-messages',
  templateUrl: './send-bulk-messages.page.html',
  styleUrls: ['./send-bulk-messages.page.scss']
})
export class SendBulkMessagesPage implements OnInit {
  messageSendForm: UntypedFormGroup;
  isBroadcast: boolean;
  choosedRecipientIds = [];
  recipientTitle: string;
  title: string;
  chooseRecipients: string;
  selectedRecipients: any;
  recipientId = [];
  broadcastIds = [];
  recipientName = [];
  userDetails: any;
  platformValue: any;
  file: any = [];
  fileFormat = true;
  fileSize = true;
  imgdata: any = [];
  allowedFileTypes: any;
  sizeErrorMessage: any;
  filesAttached = {
    pdf: 0,
    document: 0,
    image: 0,
    audio: 0,
    video: 0
  };
  // TODO : Polling need to confirm

  selectFile: void;
  sentRecipients: any;
  messageFormData: any;
  selctedUserRoles = [];
  selectedRolesPatient = [];
  selectedSiteIds: any = [];
  priorityFilterValue = MessagePriority.NORMAL;
  constructor(
    private readonly route: ActivatedRoute,
    private readonly modalController: ModalController,
    public actionSheetController: ActionSheetController,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly httpService: HttpService,
    public readonly sharedService: SharedService,
    private readonly common: CommonService,
    private readonly graphqlService: GraphqlService,
    private readonly bulkMessageService: SendBulkMessageService,
    private readonly socketService: SocketService
  ) {
    this.route.paramMap.subscribe((paramMap) => {
      this.isBroadcast = paramMap.get('type') === 'broadcast';
      this.title = this.isBroadcast ? 'TITLES.BROADCAST' : 'TITLES.MASKED';
      this.recipientTitle = this.common.getTranslateData('TITLES.CHOOSE_RECIPIENTS');
      this.chooseRecipients = this.recipientTitle;
      this.choosedRecipientIds = [];
    });
    this.messageSendForm = this.formBuilder.group({
      message: ['', Validators.required],
      subject: ['', Validators.required]
    });
    // TODO : Polling need to confirm
  }

  ngOnInit(): void {
    this.userDetails = this.sharedService.userData;
    this.platformValue = this.sharedService.platformValue;
    if (this.userDetails.mySites) {
      const getAllSitesIDs = this.userDetails.mySites.map((item: any) => item.id);
      this.selectedSiteIds = getAllSitesIDs;
    }
  }

  presentActionSheetPriority(): void {
    this.sharedService.presentActionSheet({ ActionType: 'priority' }, (priority: number) => {
      this.priorityFilterValue = priority;
    });
  }

  async presentRecipientsModal(): Promise<void> {
    const modal = await this.modalController.create({
      component: RecipientsListComponent,
      componentProps: {
        isBroadcast: this.isBroadcast,
        choosedRecipientIds: this.selectedRecipients
      }
    });
    modal.onDidDismiss().then(({ data }) => {
      if (data) {
        this.selectedRecipients = data.data ? data.data : '';
        if (this.selectedRecipients) {
          let mergedArray = [];
          let nameKey = 'tag_name';
          let idKey = 'id';
          if (!this.isBroadcast) {
            this.selectedRecipients.map((item) => (mergedArray = [...mergedArray, ...item.values]));
            nameKey = 'displayname';
            idKey = 'userid';
            mergedArray = mergedArray.filter((item) => item.checked);
            this.selectedRolesPatient = mergedArray.map((item) => item[idKey]);
          } else {
            mergedArray = this.selectedRecipients;
          }
          mergedArray = mergedArray.filter((item) => item.checked);
          this.recipientId = mergedArray.map((item) => item);
          if (this.recipientId.length) {
            this.recipientName = mergedArray.map((item) => item[nameKey]);
            this.chooseRecipients = this.recipientName.join(', ');
          } else {
            this.chooseRecipients = this.recipientTitle;
          }
        }
      }
    });
    return await modal.present();
  }
  sendMessage(): void {
    const messageFormData = this.messageSendForm.value;
    if (this.isBroadcast) {
      this.recipientId.forEach((rec) => {
        rec.citus_role_id
          ? this.selctedUserRoles.push({ id: String(rec.id) })
          : this.broadcastIds.push({ id: String(rec.id) });
      });
      if (this.validateMessageForm()) {
        const payload = {
          priorityId: this.priorityFilterValue,
          message: messageFormData.message,
          language: navigator.language,
          selectedRoles: this.selctedUserRoles,
          selectedTags: this.broadcastIds,
          siteIds: this.selectedSiteIds.toString(),
          attachmentStr: ''
        };
        if (!isBlank(this.broadcastIds) || !isBlank(this.selctedUserRoles)) {
          if (this.file.length) {
            this.fileUpload(this.isBroadcast);
          } else {
            this.messageFormData = messageFormData;
            this.sendBulkMessage(payload, 'broadcast');
          }
        }
      }
    } else {
      if (this.validateMessageForm()) {
        const payload = {
          maskedMessage: {
            priorityId: this.priorityFilterValue,
            messageSubject: messageFormData.subject,
            message: messageFormData.message,
            selectedRolesPatient: this.selectedRolesPatient,
            attachmentStr: ''
          },
          platform: this.platformValue,
          language: navigator.language
        };
        const ids = [];
        this.selectedRolesPatient.forEach((value) => {
          ids.push(`${value}`);
        });
        if (this.file.length) {
          this.fileUpload(this.isBroadcast);
        } else {
          this.messageFormData = messageFormData;
          this.maskedMessageConfirmation(payload, messageFormData);
        }
      }
    }
  }
  sendBulkMessage(payload, messageType: string) {
    const ids = [];
    this.selectedRolesPatient.forEach((value) => {
      ids.push(`${value}`);
    });
    this.httpService.doPost({ endpoint: messageType === 'broadcast' ? APIs.broadcastMessage : APIs.maskedMessage, payload, loader: true }).subscribe(
      (result: BulkMessageResponse) => {
        if (result) {
          this.file = [];
          this.imgdata = [];
          this.selectedRecipients.map(
            (item) => ((item.checked = false), item.values ? item.values.map((users) => (users.checked = false)) : '')
          );
          this.messageSendForm.reset();
          this.chooseRecipients = this.recipientTitle;
          this.broadcastIds = [];
          this.selctedUserRoles = [];
          this.selectedRolesPatient = [];
          this.recipientId = [];
          this.sentRecipients = result.data.reduce((acc, val, index) => {
            if (index === 0 || acc.findIndex((e) => e.userid === val.userid) === -1) {
              acc.push(val);
            }
            return acc;
          }, []);
          if (messageType === 'broadcast') {
            const broadcastSuccessMessage = this.common.getTranslateData('SUCCESS_MESSAGES.SUCCESS_BROADCAST_MESSAGE');
            this.common.showMessage(broadcastSuccessMessage);
            this.sharedService.trackActivity({
              type: Activity.communication,
              name: Activity.messageBroadcast,
              des: {
                data: {
                  displayName: this.userDetails.displayName,
                  recipients: JSON.stringify(result)
                },
                desConstant: Activity.messageBroadcastDes
              }
            });
          } else if (messageType === 'masked') {
            const maskedSuccessMessage = this.common.getTranslateData('SUCCESS_MESSAGES.SUCCESS_MASKED_MESSAGE');
            this.common.showMessage(maskedSuccessMessage);
            this.sharedService.trackActivity({
              type: Activity.communication,
              name: Activity.maskedMessage,
              des: {
                data: {
                  chatroomId: this.sentRecipients[0].chatroom_id,
                  displayName: this.userDetails.displayName,
                  recipientList: ids
                },
                desConstant: Activity.maskedMessageDes
              }
            });
          }
          let configurationNotEnabled = false;
          if (!isBlank(this.sentRecipients) && this.sentRecipients[0]?.configurationNotEnabled === 1) {
            configurationNotEnabled = true;
          }
          if (this.sentRecipients.length > 0 && !configurationNotEnabled) {
            // TODO : Polling need to confirm
            if (
              this.sharedService.messageList &&
              this.sentRecipients[0]?.chatroom_id &&
              this.sentRecipients[0]?.message_id
            ) {
              this.sharedService.addNewChatFromAPI(
                this.sentRecipients[0].chatroom_id.toString(),
                messageType === 'masked' ? this.sentRecipients[0].message_id : null
              );
            }
            this.setPushMessage(this.sentRecipients, this.messageFormData, messageType);
          }
          this.priorityFilterValue = MessagePriority.NORMAL;
        }
      },
      (error) => {
        this.sharedService.errorHandler(error);
      }
    );
  }
  metaDataSet(uploadedFiles: any, file: any, callBack: any): any {
    const metaData = [];
    if (uploadedFiles.length) {
      uploadedFiles[0].forEach((e, i) => {
        metaData[i] = {
          attributes: {
            data: [
              {
                userId: this.userDetails.userCmisId,
                actualUserId: this.userDetails.userId,
                owner: this.userDetails.userCmisId,
                userType: Constants.messageUserType,
                objectType: Constants.objectType,
                fileType: e.format,
                displayName: file[i].e.name,
                parentFolderId: Constants.parentFolderId,
                isDeleted: false,
                createdOn: new Date().getTime(),
                modifiedOn: new Date().getTime(),
                chatRoomId: Constants.defaultChatRoomId,
                chatType: Constants.messageChatType,
                sourceType: Constants.fileSourceType,
                fileOriginalName: e.name,
                broadcastMessage: ''
              }
            ]
          }
        };
      });
    }
    callBack(metaData);
  }
  fileUpload(checkBroadcast: boolean): any {
    const messageFormData = this.messageSendForm.value;
    const ids = [];
    this.selectedRolesPatient.forEach((value) => {
      ids.push(`${value}`);
    });
    if (checkBroadcast) {
      const payload = {
        message: messageFormData.message,
        language: navigator.language,
        selectedRoles: this.selctedUserRoles,
        selectedTags: this.broadcastIds,
        priorityId: this.priorityFilterValue,
        siteIds: this.selectedSiteIds.toString(),
        attachmentStr: ''
      };
      this.postFileUpload(payload, messageFormData);
    } else {
      const payload = {
        maskedMessage: {
          priorityId: this.priorityFilterValue,
          messageSubject: messageFormData.subject,
          message: messageFormData.message,
          selectedRolesPatient: this.selectedRolesPatient,
          attachmentStr: ''
        },
        createdBy: this.userDetails.userId,
        platform: this.platformValue,
        tenantId: this.userDetails.tenantId,
        language: navigator.language
      };
      this.maskedMessageConfirmation(payload, messageFormData);
    }
  }
  maskedMessageConfirmation(payload: any, messageFormData: any): void {
    this.common
      .showAlert({
        message: 'MESSAGES.MASKED_MESSAGE_CONFIRMATION',
        header: 'MESSAGES.ARE_YOU_SURE'
      })
      .then((confirmation) => {
        if (confirmation) {
          if (this.file.length) {
            this.postFileUpload(payload, messageFormData);
          } else {
            this.sendBulkMessage(payload, 'masked');
          }
        }
      });
  }
  postFileUpload(payload: any, messageFormData: any): any {
    this.sharedService.isLoading = true;
    this.httpService
      .fileUpload({
        endpoint: `${APIs.messageFileUpload}`,
        payload: this.file,
        field: 'file[]',
        id: this.sharedService?.userData.userId, // No need to pass Id since API handle this
        page: 'message',
        multiple: true,
        loader: false
      })
      .subscribe(
        (res) => {
          if (res[0].success) {
            let attachmentStr = '';
            // Process each uploaded file
            res.forEach((e, idx) => {
              const fileUrl = e.fileViewAPI || this.bulkMessageService.constructFileUrl(e.view, e.msg);
              const mimeType = this.bulkMessageService.getMimeTypeFromView(e.view, e.msg);
              this.imgdata.push({
                details: e,
                id: e.id,
                name: e.msg,
                fileUrl,
                mimeType,
                format: e.view
              });
              // Construct file URL based on file type using the service method

              // Infer MIME type from view property using service method

              // Generate file tag for the attachment
              this.bulkMessageService.fileFormatTypeTofileTag(e, fileUrl, e.msg, mimeType, (result) => {
                attachmentStr += result.message;

                // If all files have been processed, send the message with attachments
                if (idx === res.length - 1) {
                  this.sendMessageWithAttachment(attachmentStr, payload, this.isBroadcast ? 'broadcast' : 'masked');
                }
              });
            });
          } else {
            this.sharedService.isLoading = false;
          }
        },
        () => {
          this.sharedService.isLoading = false;
        }
      );
  }
  setPushMessage(users, messageFormData, messageType: string): void {
    let pushMessage = '';
    this.socketService.emitEvent('userPollingtoServer', users);
    pushMessage = this.bulkMessageService.pushMessage(this.filesAttached);
    if (messageFormData.message) {
      pushMessage = `${messageFormData.message}. ${pushMessage}`;
    } else {
      pushMessage = pushMessage;
    }
    this.pushNotification(users, messageFormData, messageType);
  }
  pushNotification(users: any, messageFormData: any, messageType: string): any {
    const maskedchatRoomId = users[0].chatroom_id ? users[0].chatroom_id : 0;
    const deeplinking = {
      state: Constants.deepLinkingStates.groupChat,
      priorityId: this.priorityFilterValue,
      stateParams: {
        targetID: maskedchatRoomId,
        targetName: Constants.pushNotificationGroupChatTarget
      },
      activeMessage: {
        sent: new Date().getTime() / 1000,
        messageType:
        messageType === 'broadcast' ? Constants.messageListTypes.broadcast : Constants.messageListTypes.masked,
        baseId: 0,
        userid: this.userDetails.userId,
        fromName: `"${this.userDetails.displayName}"`,
        // TODO : need to clarify groupid, grpname from polling api res
        message_group_id: Constants.messageNoGroup,
        chatroomid: maskedchatRoomId,
        title: messageFormData.message,
        createdby: this.userDetails.userId,
        selectedTenantId: ''
      }
    };
    if (this.broadcastIds || this.selctedUserRoles) {
      deeplinking.activeMessage.selectedTenantId = this.userDetails.tenantId;
    }
    const notificationData = {
      sourceId: Constants.sourceId.message.toString(),
      sourceCategoryId: Constants.sourceCategoryId.messageSendNotification
    };
    this.sharedService.sentPushNotification(
      users,
      '0',
      this.common.getTranslateData('MESSAGES.NOTIFICATION_MESSAGE'),
      '',
      deeplinking,
      '',
      notificationData
    );
    this.file = [];
  }
  pushMessage(attachedFiles: any): string {
    let pushMessage = '';
    pushMessage = this.bulkMessageService.pushMessage(attachedFiles);
    return pushMessage;
  }
  validateMessageForm(): boolean {
    if (this.recipientId.length === 0) {
      const messageAlertMessage = this.common.getTranslateData('VALIDATION_MESSAGES.REQUIRED_RECEPIENT');
      this.common.showMessage(messageAlertMessage);
      return false;
    } else if (!this.isBroadcast && isBlank(this.messageSendForm.value.subject)) {
      const messageAlertMessage = this.common.getTranslateData('VALIDATION_MESSAGES.REQUIRED_SUBJECT');
      this.common.showMessage(messageAlertMessage);
      return false;
    } else if (isBlank(this.messageSendForm.value.message)) {
      const messageAlertMessage = this.common.getTranslateData('VALIDATION_MESSAGES.REQUIRED_MESSAGE');
      this.common.showMessage(messageAlertMessage);
      return false;
    }
    return true;
  }
  onSelectFile(data: any): void {
    this.allowedFileTypes = Constants.allowedBulkMessageFileTypes;

    if (data.target.files.length > 0) {
      if (this.file.length < Constants.maxFileLength) {
        this.selectFile = Array.from(data.target.files).forEach((e: any) => {
          const fileName = e.name;
          if (e.size > Constants.maxFileUploadSize) {
            const errorMessageWithFileSize = this.sharedService.localConfig.messages.broadcastLargeFile.replace(
              /{{filename}}/g,
              fileName
            );

            this.common.showMessage(errorMessageWithFileSize);
          } else {
            const getFileExt = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
            const pos = this.allowedFileTypes.indexOf(getFileExt);
            if (pos < 0) {
              const errorMessageWithFileType =
                this.sharedService.localConfig.messages.broadcastUnsupportedFiles.replace(/{{filename}}/g, fileName);
              this.fileFormat = false;

              this.common.showMessage(errorMessageWithFileType);
            } else {
              this.fileFormat = true;
              this.fileSize = true;
              this.file.length < Constants.maxFileLength
                ? this.file.push({
                  e
                })
                : '';
            }
          }
        });
      } else {
        const fileLengthErrorMessage = this.common.getTranslateDataWithParam('ERROR_MESSAGES.MAX_FILE_LENGTH', {
          maxFileLength: Constants.maxFileLength
        });
        this.common.showMessage(fileLengthErrorMessage);
      }
    }
  }
  removeFile(i: number): void {
    this.file.splice(i, 1);
  }

  filterSitesData(data: []): void {
    this.selectedSiteIds = data;
  }

  initialSiteData(data: []): void {
    this.selectedSiteIds = data;
  }

  /**
   * Sends message with attachments
   * @param attachmentStr The attachment string to include in the message
   * @param payload The payload object to be sent
   * @param messageFormData The form data containing message content
   * @param messageType The type of message (broadcast or masked)
   */
  sendMessageWithAttachment(attachmentStr: string, payload, messageType: string): void {
    const isBroadcast = messageType === 'broadcast';
    const updatedPayload = { ...payload };
    if (isBroadcast) {
      updatedPayload.attachmentStr = attachmentStr;
    } else {
      updatedPayload.maskedMessage = updatedPayload.maskedMessage || {};
      updatedPayload.maskedMessage.attachmentStr = attachmentStr;
      updatedPayload.createdBy = undefined;
    }
    this.sendBulkMessage(updatedPayload, messageType);
  }
}
