import { Compo<PERSON>, OnInit, <PERSON><PERSON>hil<PERSON>, <PERSON>ement<PERSON><PERSON>, HostListener } from '@angular/core';
import { ActionSheetController, ModalController, NavController, Platform } from '@ionic/angular';
import { CommonService } from 'src/app/services/common-service/common.service';
import { ActivatedRoute, Router, NavigationExtras } from '@angular/router';
import { MultipleFileSelectionComponent } from 'src/app/pages/document-center/multiple-file-selection/multiple-file-selection.component';
import {
  deepCopyJSON,
  deepParseJSON,
  getFileReader,
  isHTMLTagAvailable,
  isNil,
  isPresent,
  removeUndefinedKeysJSON,
  formatDate,
  isBlank,
  getCurrentDate,
  isObject
} from 'src/app/utils/utils';
import { FilingCenterPage } from 'src/app/pages/document-center/filing-center/filing-center.page';
import { DocumentCenterRecipientsComponent } from 'src/app/pages/document-center/document-center-recipients/document-center-recipients.component';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { Constants, FolderType, IntegrationType, UserGroup } from 'src/app/constants/constants';
import { HttpService } from 'src/app/services/http-service/http.service';
import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { APIs } from 'src/app/constants/apis';
import { Config } from 'src/app/constants/config';
import { SocketService } from 'src/app/services/socket-service/socket.service';
import { Activity } from 'src/app/constants/activity';
import { Signature } from 'src/app/constants/signature';
import { environment } from 'src/environments/environment';
import { Urls } from 'src/app/constants/urls';
import { ConfigValues } from 'src/assets/config/config';
import { Socket } from 'src/app/constants/socket';
import { PatientInformation, PatientInformationForPrepopulation } from 'src/app/interfaces/document-request-signature';
import { CompletedDocumentRecipientsComponent } from 'src/app/components/completed-document-recipients/completed-document-recipients.component';
import { AdmissionComponent } from 'src/app/components/admission/admission.component';

@Component({
  selector: 'app-select-document',
  templateUrl: './select-document.page.html',
  styleUrls: ['./select-document.page.scss']
})
export class SelectDocumentPage implements OnInit {
  @ViewChild('docInput') docInputViewChild: ElementRef;
  @ViewChild('cameraInput') cameraInputViewChild: ElementRef;
  docInputElement: HTMLInputElement;
  cameraInputElement: HTMLInputElement;
  folderId: number;
  folderName: string;
  stage: string;
  folder: any;
  selectedDocument: any;
  documentType: any;
  chooseRecipients: any;
  todayDate: any;
  selectedRecipient: any;
  displayText: any;
  userData: any;
  selectedDocForm: UntypedFormGroup;
  recipientDisplayText = this.commonService.getTranslateData('TITLES.CHOOSE_RECIPIENTS');
  patientDisplayText = this.commonService.getTranslateData('TITLES.CHOOSE_ASSOCIATED_PATIENT');
  admissionDisplayText = this.commonService.getTranslateData('TITLES.CHOOSE_ADMISSION');
  associateDisplayText = this.commonService.getTranslateData('TITLES.CHOOSE_DELEGATED_STAFF');
  recipientsCompletedDocument = this.commonService.getTranslateData('TITLES.CHOOSE_RECIPIENTS_OF_COMPLETED_DOCUMENTS');
  tagname: any;
  documentDisplayText: any;
  mrn: any;
  interactionMethodValues: any;
  galleryDocs: any = [];
  allowedFileTypes: any;
  galleryFile: any = [];
  getFileExt: string;
  fileSize: boolean;
  fileFormat: boolean;
  multipleFileUpload: any = [];
  isShowDocumentBox = true;
  files: any = [];
  tempFileName: any;
  singleDocFile: any | { multiple: false };
  galleryDocDisplayText: any;
  galleryFileName: any;
  metaTagFileNameFormatText: any;
  replaceDisplayText: any;
  documentSource: any;
  fromFilingCenter = Constants.docFromFile;
  fromGallery = Constants.docSourceGallery;
  defaultPage = Constants.stageChooseDoc;
  selectedDocPage = Constants.stageDocSelected;
  selectDocState: any;
  data: any = {};
  docDeliveryDetails: any;
  actualFileName: string;
  constants: any = {};
  docUploadTitle: string;
  folderNameCopyFilingCenter: string;
  ownerIdCopyFilingCenter: any;
  filenameFormatCopyFilingCenter: string;
  enableApplessMode: boolean;
  citusInteractionChannnel: any;
  associatePatientModal = false;
  consoloData: any;
  enableMultiSite = false;
  disablePatientorRecipient = false;
  disablePatientAdmission = false;
  disableRecipientField = false;
  isRecipientCaregiver = false;
  defaultFilingCenterFolder: string;
  recipientsCompletedDocumentIDs = '';
  externalIdentityDetails: any;
  enableDocumentManagement: boolean;
  documentFolderSelected: string;
  @HostListener('input', ['$event']) onInput(event: any): void {
    // Checking id to update value only related to id="save-as-document-display" for entering input to avoid other area changes value impact
    if (!isBlank(event) && event.target.id === Constants.saveAsDocumentDisplay) {
      this.changeFileName(event);
    }
  }
  constructor(
    private readonly actionSheetController: ActionSheetController,
    private readonly commonService: CommonService,
    private readonly modalController: ModalController,
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly navCtrl: NavController,
    public readonly sharedService: SharedService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly httpService: HttpService,
    private readonly graphqlService: GraphqlService,
    private readonly socketService: SocketService,
    private readonly platform: Platform
  ) {
    this.mrn = this.commonService.getTranslateData('GENERAL.MRN');
    this.userData = this.sharedService.userData;
    this.checkConfigValues();
    this.route.queryParams.subscribe(() => {
      if (this.router.getCurrentNavigation()?.extras.state) {
        this.galleryFile = [];
        this.selectDocState = this.router.getCurrentNavigation().extras.state;
        this.folder = this.selectDocState.folder;
        this.folderName = this.folder.name;
        this.documentType = this.folder.doctype;
        this.data.allowPersonalSignature = this.documentType.allowPersonalSignature;
        this.docUploadTitle = !this.documentType.sendDocumentWithoutSignature
          ? this.commonService.getTranslateData('BUTTONS.NEXT')
          : this.commonService.getTranslateData('BUTTONS.SUBMIT');
        this.setPage(this.folder, '');
        this.chooseOptionsForUpload();
        this.trackActivity(this.folder.doctype.name);
      } else if (
        this.sharedService.automaticLinkedItems &&
        this.sharedService.automaticLinkedItems.documentTypeDetails &&
        !isBlank(this.sharedService.automaticLinkedItems.documentTypeDetails.id)
      ) {
        this.consoloData = this.sharedService.automaticLinkedItems;
        this.selectedDocument = {};
        this.documentType = this.consoloData.documentTypeDetails;
        if (this.checkConsoloValidations()) {
          this.sharedService.isLoading = true;
          this.data.allowPersonalSignature = this.documentType.allowPersonalSignature;
          this.docUploadTitle = !this.documentType.sendDocumentWithoutSignature
            ? this.commonService.getTranslateData('BUTTONS.NEXT')
            : this.commonService.getTranslateData('BUTTONS.SUBMIT');
          const fileName = this.consoloData?.fileDetails?.name;
          this.selectedDocument.name = fileName.slice(0, fileName.lastIndexOf('.'));
          this.selectedDocument.fileType = this.selectedDocument.type = fileName.slice(fileName.lastIndexOf('.') + 1);
          this.selectedDocument.path = `/${this.consoloData.fileDetails.folder}`;
          this.patientDisplayText = this.consoloData.patient.displayName;
          this.selectedRecipient = this.consoloData.patient
            ? this.consoloData.patient
            : this.consoloData.recipients
              ? this.consoloData.recipients
              : this.consoloData.associate;
          this.fetchSiteRegistrationId(this.selectedRecipient?.siteId);
          this.todayDate = getCurrentDate(Constants.dateFormat.mmddyy);
          this.setPage('', this.fromFilingCenter);
          this.documentUpload();
          this.trackActivity(this.documentType.name);
        }
      } else {
        this.navCtrl.back();
      }
    });
    this.selectedDocForm = this.formBuilder.group({
      interactionMethod: [Constants.defaultInteractionMethod]
    });
  }
  ngOnInit(): void {
    this.constants = Constants;
    this.interactionMethodValues = Constants.interactionChannel;
  }
  checkConfigValues(): void {
    this.enableDocumentManagement = this.sharedService.isEnableConfig(Config.enableDocumentManagement);
    this.enableApplessMode = this.sharedService.isEnableConfig(Config.enableApplessModel);
    this.enableMultiSite = this.sharedService.isEnableConfig(Config.enableMultiSite);
  }
  setPage(document: any, page: any): any {
    this.documentSource = page;
    this.stage = document.id || isBlank(page) ? this.defaultPage : !isBlank(page) ? this.selectedDocPage : '';
    this.metaTagFileNameFormatText = this.documentType?.fileNameFormatText;
    if (this.stage === this.selectedDocPage) {
      this.tagname = this.folder ? this.folder.doctype.name : this.documentType ? this.documentType.name : '';
      this.documentDisplayText = this.metaTagFileNameFormatText
        ?.replace('{documentName}', this.selectedDocument ? this.selectedDocument.name : '')
        ?.replace('{tagName}', this.tagname ? this.tagname : '')
        ?.replace('{date}', this.todayDate ? this.todayDate : '');
      this.documentDisplayText = this.checkDisplayText(this.documentDisplayText);
    }
  }
  checkDisplayText(documentDisplayText: string): string {
    if (documentDisplayText?.includes(Signature.docTypeTags.preSignedSnapSend)) {
      this.replaceDisplayText = documentDisplayText.replace(Signature.docTypeTags.preSignedSnapSend, '');
    } else if (documentDisplayText?.includes(Signature.docTypeTags.estimateCreateSend)) {
      this.replaceDisplayText = documentDisplayText.replace(Signature.docTypeTags.estimateCreateSend, '');
    } else if (documentDisplayText?.includes(Signature.docTypeTags.estimateCreateSaveSign)) {
      this.replaceDisplayText = documentDisplayText.replace(Signature.docTypeTags.estimateCreateSaveSign, '');
    } else if (documentDisplayText?.includes(Signature.docTypeTags.estimatePresignedSnapSend)) {
      this.replaceDisplayText = documentDisplayText.replace(Signature.docTypeTags.estimatePresignedSnapSend, '');
    } else if (documentDisplayText?.includes(Signature.docTypeTags.presignSnapSend)) {
      this.replaceDisplayText = documentDisplayText.replace(Signature.docTypeTags.presignSnapSend, '');
    } else if (documentDisplayText?.includes(Signature.docTypeTags.presignedSnapSend)) {
      this.replaceDisplayText = documentDisplayText.replace(Signature.docTypeTags.presignedSnapSend, '');
    } else if (documentDisplayText?.includes(Signature.docTypeTags.createSend)) {
      this.replaceDisplayText = documentDisplayText.replace(Signature.docTypeTags.createSend, '');
    } else if (documentDisplayText?.includes(Signature.docTypeTags.createSaveSign)) {
      this.replaceDisplayText = documentDisplayText.replace(Signature.docTypeTags.createSaveSign, '');
    } else if (documentDisplayText?.includes(Signature.docTypeTags.pre_signed_SnapSend)) {
      this.replaceDisplayText = documentDisplayText.replace(Signature.docTypeTags.snapSend, '');
    } else if (documentDisplayText?.includes(Signature.docTypeTags.pre_SignedSnapSend)) {
      this.replaceDisplayText = documentDisplayText.replace(Signature.docTypeTags.snapSend, '');
    } else if (documentDisplayText?.includes(Signature.docTypeTags.pre_signed)) {
      this.replaceDisplayText = documentDisplayText.replace(Signature.docTypeTags.pre_signed, '');
    } else if (documentDisplayText?.includes(Signature.docTypeTags.pre_Signed)) {
      this.replaceDisplayText = documentDisplayText.replace(Signature.docTypeTags.pre_Signed, '');
    } else if (documentDisplayText?.includes(Signature.docTypeTags.presigned)) {
      this.replaceDisplayText = documentDisplayText.replace(Signature.docTypeTags.presigned, '');
    } else {
      this.replaceDisplayText = documentDisplayText;
    }
    if (this.sharedService.appConfig) {
      const fileTypes = this.sharedService.appConfig;
      const getLocalWithAPIData = [...fileTypes.configurations.allowedFileFormat, ...ConfigValues.messages.configurations.allowedFileFormat];
      this.allowedFileTypes = [...new Set(getLocalWithAPIData)];

      this.allowedFileTypes.forEach((element: string) => {
        this.replaceDisplayText = this.replaceDisplayText.replaceAll(`.${element}`, '');
      });
    }
    this.data.documentName = this.replaceDisplayText;
    return this.replaceDisplayText;
  }
  async chooseOptionsForUpload(): Promise<any> {
    let buttons;
    this.disablePatientorRecipient = false;
    const setGalleryOptionName = this.platform.is('capacitor') && this.platform.is('ios') ? 'OPTIONS.FROM_PHOTO_LIBRARY' : 'OPTIONS.FROM_GALLERY';
    // Camera - Option
    const cameraOption = {
      text: this.commonService.getTranslateData('OPTIONS.FROM_CAMERA'),
      id: 'action-camera',
      handler: () => {
        // TODO: handle action
        const data = {
          displayName: this.userData.displayName
        };
        this.sharedService.trackActivity({
          type: Activity.signatureRequest,
          name: Activity.fileCooser,
          des: { data, desConstant: Activity.fileChooserDes },
          linkageId: this.userData.displayName
        });
        this.checkModalConditions(Constants.docSourceCam);
      }
    };
    // Scanner - Option
    const scanOption = {
      text: this.commonService.getTranslateData('OPTIONS.FROM_SCANNER'),
      id: 'action-scan',
      handler: () => {
        // TODO: handle action
        const data = {
          displayName: this.userData.displayName
        };
        this.sharedService.trackActivity({
          type: Activity.signatureRequest,
          name: Activity.fileCooser,
          des: { data, desConstant: Activity.fileChooserDes },
          linkageId: this.userData.displayName
        });
        this.checkModalConditions(Constants.docSourceScan);
      }
    };

    // Gallery - Option
    const galleryOption = {
      text: this.commonService.getTranslateData(setGalleryOptionName),
      id: 'action-gallery',
      handler: () => {
        const data = {
          displayName: this.userData.displayName
        };
        this.sharedService.trackActivity({
          type: Activity.signatureRequest,
          name: Activity.fileCooser,
          des: { data, desConstant: Activity.fileChooserDes },
          linkageId: this.userData.displayName
        });

        this.checkModalConditions(Constants.docSourceGallery);
      }
    };

    // iCloud - Option
    const iCloudOption = {
      text: this.commonService.getTranslateData('OPTIONS.FROM_ICLOUD'),
      id: 'action-icloud',
      handler: () => {
        const data = {
          displayName: this.userData.displayName
        };
        this.sharedService.trackActivity({
          type: Activity.signatureRequest,
          name: Activity.fileCooser,
          des: { data, desConstant: Activity.fileChooserDes },
          linkageId: this.userData.displayName
        });
        this.checkModalConditions(Constants.docSourceiCloud);
      }
    };

    // Filling Center - Option
    const fillingCenterOption = {
      text: this.commonService.getTranslateData('OPTIONS.FROM_FILING_CENTERS'),
      id: 'action-filing',
      handler: () => {
        this.presentFilingCenterModal(FolderType.fillingCenter);
      }
    };

    // Document Folder - option
    const documentFolderOption = {
      text: this.commonService.getTranslateData('OPTIONS.FROM_DOCUMENT_UPLOADS'),
      id: 'action-document-folder',
      handler: () => {
        this.presentFilingCenterModal(FolderType.documentFolder);
      }
    };

    // filling center - option or Document Folder - option
    const selectedOption = this.enableDocumentManagement ? documentFolderOption : fillingCenterOption;
    this.documentFolderSelected = selectedOption.text;

    // Cancel - Option
    const cancelOption = {
      text: this.commonService.getTranslateData('BUTTONS.CANCEL'),
      id: 'action-cancel',
      role: 'cancel'
    };

    if (this.platform.is('capacitor')) {
      if (this.platform.is('ios')) {
        buttons = [cameraOption, scanOption, galleryOption, iCloudOption, selectedOption, cancelOption];
      } else {
        buttons = [cameraOption, scanOption, galleryOption, selectedOption, cancelOption];
      }
    } else {
      buttons = [cameraOption, galleryOption, selectedOption, cancelOption];
    }

    const actionSheet = await this.actionSheetController.create({ mode: 'ios', buttons });
    await actionSheet.present();
  }

  /**
   *
   * @param type Camera, iCloud, Gallery
   */
  checkModalConditions(type: string) {
    if (
      this.enableMultiSite &&
      !this.documentType.allowRecipientRoles &&
      !this.documentType.allowAssociateRoles &&
      !this.documentType.allowAssociatePatient
    ) {
      if (this.userData && this.userData.mySites && this.userData.mySites.length === 1) {
        this.selectFileFromType(type);
      } else {
        this.chooseFromModal(this.constants.recipient, type);
      }
    } else if (
      this.enableMultiSite &&
      this.documentType.allowRecipientRoles &&
      this.documentType.allowAssociateRoles &&
      !this.documentType.allowAssociatePatient
    ) {
      this.chooseFromModal(this.constants.recipient, type);
    } else if (
      this.enableMultiSite &&
      (this.documentType.allowRecipientRoles || this.documentType.allowAssociateRoles) &&
      !this.documentType.allowAssociatePatient
    ) {
      this.disablePatientorRecipient = true;
      this.chooseFromModal(this.documentType.allowAssociateRoles ? this.constants.associate : this.constants.recipient, type);
    } else if (
      this.enableMultiSite &&
      (this.documentType.allowRecipientRoles || this.documentType.allowAssociateRoles) &&
      this.documentType.allowAssociatePatient
    ) {
      this.disableRecipientField = true;
      this.selectFileFromType(type);
    } else {
      this.selectFileFromType(type);
    }
  }

  selectFileFromType(type: string) {
    if (this.platform.is('capacitor')) {
      this.showMultiSelectFileUploadPopup(type);
    } else if (type === Constants.docSourceCam) {
      this.cameraInputElement?.click();
    } else if (type === Constants.docSourceGallery) {
      this.docInputElement?.click();
    }
  }

  selectedSite: any;
  selectedSites: any = [];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  async presentFilingCenterModal(selectedFolderType: FolderType): Promise<any> {
    this.galleryFile = [];
    this.singleDocFile = '';
    const modal = await this.modalController.create({
      component: FilingCenterPage,
      componentProps: {
        folderName: this.folder?.name,
        selectedItem: this.selectDocState?.folder?.doctype,
        selectedFolderType
      }
    });
    modal.onDidDismiss().then(({ data }) => {
      if (data.selectedSite) {
        this.selectedSite = data.selectedSite;
      }
      if (data.selectedSites) {
        this.selectedSites = data.selectedSites;
      }
      if (data?.fcmMappingData) {
        if (isPresent(data.fcmMappingData.defaultFolderFromFilingCenter)) {
          this.defaultFilingCenterFolder = data.fcmMappingData.defaultFolderFromFilingCenter;
        }
        if (isPresent(data.fcmMappingData.folderBySite)) {
          this.selectDocState.folder.name = data.fcmMappingData.folderBySite;
        } else if (isPresent(data.fcmMappingData.responseData) && isPresent(data.fcmMappingData.responseData.folder)) {
          this.selectDocState.folder.name = data.fcmMappingData.responseData.folder;
        }
      }
      if (this.sharedService.isMultiAdmissionsEnabled && !isBlank(data.admission)) {
        this.disablePatientAdmission = true;
        this.data.patientAdmissionId = data.admission?.admissionId;
        this.admissionDisplayText = data.admission?.admissionName;
      }
      if (data && data.item) {
        this.selectedDocument = data.item;
        this.selectedDocument.path = `/${data.item ? data.item?.folder : ''}`;
        this.todayDate = data.date || getCurrentDate(Constants.dateFormat.mmddyy);
        this.actualFileName = data.actualFileName || this.selectedDocument.name;
        let alternate;
        this.setPage(this.selectedDocument, this.fromFilingCenter);
        if (
          this.enableMultiSite &&
          (this.documentType.allowRecipientRoles || this.documentType.allowAssociateRoles) &&
          !this.documentType.allowAssociatePatient
        ) {
          this.disablePatientorRecipient = true;
          if (data.selectedRecipients) {
            let setVirtualKey;

            this.selectedRecipient = data.selectedRecipients;
            if (!isPresent(this.selectedRecipient.siteId)) {
              this.selectedRecipient.siteId = this.selectedSite[0];
            }
            this.fetchSiteRegistrationId(this.selectedRecipient.siteId);
            if (data.alternateIndex >= 0) {
              alternate = this.selectedRecipient.alternate = this.selectedRecipient.alternateContacts[data.alternateIndex];
              if (isPresent(alternate)) {
                setVirtualKey = !this.sharedService.isConfigTrue(alternate.password);
                this.selectedRecipient.displayTextName = `${alternate.patientDisplayName}(${alternate?.displayName}${
                  alternate?.relation ? `-${alternate?.relation}` : ''
                })-${alternate.patientDob ? `${formatDate(alternate.patientDob, Constants.dateFormat.mdy)}` : ''} ${
                  alternate.patientMrn ? `[${this.mrn}: ${alternate.patientMrn}]` : ''
                } ${!setVirtualKey ? `(${Constants.enrolledUser})` : `(${Constants.showVirtualUser})`}`;
              }
            } else {
              this.selectedRecipient.displayTextName = `${this.commonService.getPatientDisplayName(this.selectedRecipient, Constants.recipient)} ${
                this.selectedRecipient.isVirtual ? `(${Constants.showVirtualUser})` : `(${Constants.enrolledUser})`
              }`;
            }

            if (!isNil(data.selectedRecipients.isVirtual)) {
              if (isNil(setVirtualKey)) {
                setVirtualKey = data.selectedRecipients.isVirtual;
              }
              if (!isNil(setVirtualKey)) {
                this.associatePatientModal = true;
                if (setVirtualKey) {
                  this.interactionMethodValues = [Constants.interactionChannel[0]];
                  this.selectedDocForm.patchValue({
                    interactionMethod: Constants.magicLink
                  });
                } else {
                  this.interactionMethodValues = Constants.interactionChannel;
                  this.selectedDocForm.patchValue({
                    interactionMethod: Constants.mobileapp
                  });
                }
              }

              this.data.recipient = this.selectedRecipient;
              this.recipientDisplayText = this.selectedRecipient.displayTextName;
            }
          }
        } else if (this.enableMultiSite && this.documentType.allowAssociatePatient) {
          this.disablePatientorRecipient = true;
          const siteName = data.selectedRecipients.sitename
            ? data.selectedRecipients.sitename
            : data.selectedRecipients.site_name
              ? data.selectedRecipients.site_name
              : '';
          this.patientDisplayText = `${data.selectedRecipients.displayText}${siteName ? ` - ${siteName}` : ''}`;
          this.data.patient = data.selectedRecipients;
          this.fetchSiteRegistrationId(data.selectedRecipients.siteId);
        }
        this.setDisplayTextForCreatedPatient(data.selectedRecipients, Constants.docFromFile);
      } else {
        this.stage = this.defaultPage;
        this.chooseOptionsForUpload();
      }
    });
    return await modal.present();
  }
  setDisplayTextForCreatedPatient(patient, source: string): void {
    const createdPatientDetails = patient?.createdPatientDetails;
    if (createdPatientDetails) {
      const mrnLabel = createdPatientDetails.mrn ? `[ ${this.mrn}: ${createdPatientDetails.mrn} ]` : '';
      const dob = createdPatientDetails.dob ? formatDate(createdPatientDetails.dob, Constants.dateFormat.mdy) : '';
      const enrolledLabel = createdPatientDetails.passwordStatus === 'true' ? `(${Constants.enrolledUser})` : `(${Constants.showVirtualUser})`;
      let siteLabel = '';
      if (this.enableMultiSite && createdPatientDetails.siteIds) {
        const siteName = this.sharedService.userData.mySites?.find((x) => x.id == createdPatientDetails.siteIds)?.name || '';
        siteLabel = siteName ? ` - ${siteName}` : '';
      }
      const label = [patient?.displayname, ' - ', dob, mrnLabel, enrolledLabel, siteLabel].join('');
      if (source === Constants.docSourceGallery) {
        this.selectedRecipient.displayText = label;
      }
      if (source === Constants.docFromFile) {
        this.patientDisplayText = label;
      }
      this.selectedDocForm.patchValue({ interactionMethod: Constants.mobileapp });
    }
  }

  async chooseFromModal(value: string, modalSelectionType?): Promise<any> {
    const recipientType = value;
    const patient = this.data.patient ? this.data.patient : this.selectedRecipient;
    const modal = await this.modalController.create({
      component: DocumentCenterRecipientsComponent,
      componentProps: {
        recipientType,
        documentType: this.documentType,
        sites: this.selectedSite,
        admissionId: this.documentType.allowAssociatePatient ? this.admissionId : '',
        selectedAssociatePatient: this.documentType.allowAssociatePatient ? patient : ''
      },
      id: 'choose-recipient'
    });
    modal.onDidDismiss().then(({ data }) => {
      this.recipientsCompletedDocument = this.commonService.getTranslateData('TITLES.CHOOSE_RECIPIENTS_OF_COMPLETED_DOCUMENTS');
      this.recipientsCompletedDocumentIDs = '';
      let setVirtualKey;
      if (recipientType === Constants.recipient) {
        this.associatePatientModal = false;
        setVirtualKey = undefined;
      }
      if (data) {
        if (data.selectedSite) {
          this.selectedSite = data.selectedSite;
        }
        if (data.selectedSites) {
          this.selectedSites = data.selectedSites;
        }
        if (data.recipient) {
          this.selectedRecipient = data.recipient;
          this.selectedRecipient.displayText = `${data.recipient.dob ? formatDate(data.recipient.dob, Constants.dateFormat.mdy) : ''} ${data.recipient.IdentityValue ? `[${this.mrn}: ${data.recipient.IdentityValue}]` : ''
            } ${data.recipient.passwordStatus === 'true' ? `(${Constants.enrolledUser})` : `(${Constants.showVirtualUser})`}`;
          this.patientDisplayText = `${this.selectedRecipient.displayname}`;
          this.setDisplayTextForCreatedPatient(data.recipient, Constants.docSourceGallery);
          if (data.recipient?.createdPatientDetails) {
            const { createdPatientDetails } = data.recipient;
            if (
              !(
                this.enableMultiSite &&
                (this.documentType.allowRecipientRoles || this.documentType.allowAssociateRoles) &&
                !this.documentType.allowAssociatePatient
              )
            ) {
              this.disableRecipientField = false;
              this.selectedRecipient = {
                ...this.selectedRecipient,
                ...createdPatientDetails,
                userId: createdPatientDetails.userid,
                siteId: createdPatientDetails.siteIds,
                role: createdPatientDetails.userType
              };
            }
          }
          if (!this.sharedService.isMultiAdmissionsEnabled || !this.isRolePatient) {
            this.fetchSiteRegistrationId(this.selectedRecipient?.siteId);
          }
          this.data.patient = this.selectedRecipient;
          if (!isNil(data.recipient.isVirtual)) {
            setVirtualKey = data.recipient.isVirtual;
            if (setVirtualKey) {
              this.interactionMethodValues = [Constants.interactionChannel[0]];
            } else {
              this.interactionMethodValues = Constants.interactionChannel;
            }
          }
        } else {
          this.selectedRecipient = data.data;
          if (!this.sharedService.isMultiAdmissionsEnabled || !this.isRolePatient) {
            this.fetchSiteRegistrationId(this.selectedRecipient?.siteId);
          }
          let alternate;
          if (!isNil(data.data.isVirtual)) {
            setVirtualKey = data.data.isVirtual;
          }
          if (data.alternateIndex >= 0) {
            alternate = this.selectedRecipient.alternate = this.selectedRecipient.alternateContacts[data.alternateIndex];
            if (isPresent(alternate)) {
              setVirtualKey = !this.sharedService.isConfigTrue(alternate.password);
              this.selectedRecipient.displayText = `${alternate.patientDisplayName}(${alternate?.displayName}${alternate?.relation ? `-${alternate?.relation}` : ''
                })-${alternate.patientDob ? `${formatDate(alternate.patientDob, Constants.dateFormat.mdy)}` : ''} ${alternate.patientMrn ? `[${this.mrn}: ${alternate.patientMrn}]` : ''
                } ${!setVirtualKey ? `(${Constants.enrolledUser})` : `(${Constants.showVirtualUser})`}`;
            }
          }
          if (
            this.enableMultiSite &&
            (this.documentType.allowRecipientRoles || this.documentType.allowAssociateRoles) &&
            !this.documentType.allowAssociatePatient
          ) {
            this.selectFileFromType(modalSelectionType);
          } else {
            // Do disable when multi admission is enabled on patient selection
            this.disableRecipientField = !this.sharedService.isMultiAdmissionsEnabled ? false : value === Constants.patientValue;
          }
          if (!isNil(setVirtualKey)) {
            if (setVirtualKey) {
              this.interactionMethodValues = [Constants.interactionChannel[0]];
            } else {
              this.interactionMethodValues = Constants.interactionChannel;
            }
          }
        }
        if (
          this.sharedService.isMultiAdmissionsEnabled &&
          (value === Constants.patientValue || (!this.documentType.allowAssociatePatient && value === Constants.recipient))
        ) {
          this.admissionDisplayText = this.commonService.getTranslateData('TITLES.CHOOSE_ADMISSION');
          this.data.patientAdmissionId = '';
        }
        switch (value) {
          case Constants.recipient:
            this.recipientDisplayText = `${this.selectedRecipient.displayText}`;
            this.data.recipient = this.selectedRecipient;
            break;
          case Constants.patientValue:
            this.patientDisplayText = ` ${this.selectedRecipient.displayText}`;
            this.data.patient = this.selectedRecipient;
            this.resetAllPatientDependentData();
            break;
          case Constants.associate:
            this.associateDisplayText = `${this.selectedRecipient.displayName}`;
            this.data.associate = this.selectedRecipient;
            break;
        }
        if (this.sharedService.isEnableConfig(Config.enableApplessModel)) {
          if (setVirtualKey !== undefined) {
            this.associatePatientModal = true;
            if (setVirtualKey) {
              this.selectedDocForm.patchValue({
                interactionMethod: Constants.magicLink
              });
            } else {
              this.selectedDocForm.patchValue({
                interactionMethod: Constants.mobileapp
              });
            }
          }
        }
      }
    });
    return await modal.present();
  }

  resetAllPatientDependentData() {
    if (isPresent(this.selectedSite) && this.selectedSite.length > 1) {
      this.recipientDisplayText = this.commonService.getTranslateData('TITLES.CHOOSE_RECIPIENTS');
      this.data.recipient = {};
      this.associateDisplayText = this.commonService.getTranslateData('TITLES.CHOOSE_DELEGATED_STAFF');
      this.data.associate = {};
    }
  }

  fetchSiteRegistrationId(siteId) {
    if (this.enableMultiSite && siteId) {
      this.graphqlService.siteDetails(parseInt(siteId), this.admissionId).subscribe((response) => {
        if (response && response.data.getSiteDetails) {
          if (this.selectedRecipient) {
            this.selectedRecipient.siteRegistrationId = response.data.getSiteDetails.registrationId;
          }
          if (this.data.patient) {
            this.data.patient.siteRegistrationId = response.data.getSiteDetails.registrationId;
          }
        } else {
          this.sharedService.trackActivity({
            type: Activity.signatureRequest,
            name: Activity.fetchSiteRegistrationId,
            des: Activity.siteIdNotFound
          });
        }
      });
    }
  }

  removeFile(): void {
    this.galleryFile = [];
    this.singleDocFile = '';
    this.galleryDocDisplayText = '';
    this.getFileExt = '';
    this.documentSource = '';
    this.recipientsCompletedDocumentIDs = '';
    this.recipientsCompletedDocument = this.commonService.getTranslateData('TITLES.CHOOSE_RECIPIENTS_OF_COMPLETED_DOCUMENTS');
    this.stage = this.defaultPage;
  }

  loadDocument(data: any): void {
    if (this.sharedService.appConfig) {
      const fileTypes = this.sharedService.appConfig;
      this.allowedFileTypes = fileTypes.configurations.allowedFileFormat;
    }
    if (data.target.files.length > 0) {
      this.setPage('', this.fromGallery);
      if (this.galleryFile.length < Constants.maxFileLength) {
        this.galleryDocs = Array.from(data.target.files).forEach((e: any, index: number) => {
          if (e.size > Constants.maxFileUploadSize) {
            const sizeErrorMessage = this.commonService.getTranslateDataWithParam('ERROR_MESSAGES.FILE_UPLOADSIZE', {
              maxFileSize: Constants.maxFileSize
            });
            this.commonService.showMessage(sizeErrorMessage);
          } else {
            const fileName = e.name.includes(Constants.checkSpace) ? decodeURIComponent(e.name) : e.name;
            const fileSize = e.size;
            const ext = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
            const pos = this.allowedFileTypes.indexOf(ext);
            if (pos < 0) {
              this.fileFormat = false;
              this.commonService.showMessage(this.commonService.getTranslateData('ERROR_MESSAGES.FILE_FORMAT'));
              this.stage = this.defaultPage;
            } else if (fileSize > Constants.maxFileUploadSize) {
              this.fileSize = false;
              const sizeErrorMessage = this.commonService.getTranslateDataWithParam('ERROR_MESSAGES.FILE_UPLOADSIZE', {
                maxFileSize: Constants.maxFileSize
              });
              this.commonService.showMessage(sizeErrorMessage);
              this.stage = this.defaultPage;
            } else if (isHTMLTagAvailable(fileName)) {
              this.stage = this.defaultPage;
              this.commonService.showMessage(this.commonService.getTranslateData('ERROR_MESSAGES.ERROR_MSG_INVALID_INPUT'));
            } else {
              this.fileFormat = true;
              this.fileSize = true;
              this.getFileExt = this.commonService.getFileType(e.type);
              this.galleryFile.push({
                e,
                name: fileName,
                size: e.size,
                type: e.type,
                extension: this.getFileExt
              });
              if (index === data.target.files.length - 1) {
                this.galleryFile.reverse();
              }
              this.setGalleryDocName();
            }
          }
        });
      } else {
        const fileLengthErrorMessage = this.commonService.getTranslateDataWithParam('ERROR_MESSAGES.MAX_FILE_LENGTH', {
          maxFileLength: Constants.maxFileLength
        });
        this.commonService.showMessage(fileLengthErrorMessage);
        isBlank(this.galleryFile) ? (this.stage = this.defaultPage) : (this.stage = this.selectedDocPage);
      }
    } else if (this.galleryFile?.length === 0) {
      this.stage = this.defaultPage;
    }
  }

  setGalleryDocName(): void {
    this.galleryFileName = this.galleryFile[0]?.name.split('.');

    const date = formatDate();
    const today = `${formatDate(date, Constants.dateFormat.mmddyy)}`;
    this.galleryDocDisplayText = this.metaTagFileNameFormatText
      ?.replace('{documentName}', this.galleryFileName[0])
      ?.replace('{tagName}', this.tagname ? this.tagname : '')
      ?.replace('{date}', today);
    this.documentDisplayText = this.checkDisplayText(this.galleryDocDisplayText);
    this.singleDocFile = this.galleryFile[0];
    if (this.galleryFile.length > 1) {
      this.singleDocFile.multiple = true;
    } else {
      this.singleDocFile.multiple = false;
    }
    if (Constants.checkFileExtensionType.includes(this.singleDocFile.extension)) {
      const newInstance = getFileReader();
      let imageURL;
      const getFileObject = this.galleryFile[0].e || this.galleryFile[0].file;
      newInstance.readAsDataURL(getFileObject);
      newInstance.onload = () => {
        imageURL = newInstance.result as string;
        this.singleDocFile.url = imageURL;
      };
    } else {
      this.singleDocFile.url = '';
    }
  }
  removeGalleryFile(i: number): void {
    this.galleryFile.splice(i, 1);
    if (this.galleryFile.length) {
      this.getFileExt = this.galleryFile[0].extension;
      this.setGalleryDocName();
    } else {
      this.galleryFile = [];
      this.stage = this.defaultPage;
    }
  }

  ionViewDidEnter(): void {
    this.docInputElement = this.docInputViewChild.nativeElement;
    this.cameraInputElement = this.cameraInputViewChild.nativeElement;
  }
  cancel(): void {
    this.stage = this.defaultPage;
    this.commonService.navCtrl.back();
  }
  next(): void {
    const msg = '';
    const errorMessage = this.validationForRecipients(msg);
    if (this.galleryFile.length > Constants.maxFileLength) {
      const fileLengthErrorMessage = this.commonService.getTranslateDataWithParam('ERROR_MESSAGES.MAX_FILE_LENGTH_FINISH', {
        maxFileLength: Constants.maxFileLength,
        removeCount: this.galleryFile.length - Constants.maxFileLength
      });
      this.commonService.showMessage(fileLengthErrorMessage);
    } else if (this.totalFileSize > Constants.maxFileUploadSize) {
      const sizeErrorMessage = this.commonService.getTranslateDataWithParam('ERROR_MESSAGES.FILE_UPLOADSIZE', {
        maxFileSize: Constants.maxFileSize
      });
      this.commonService.showMessage(sizeErrorMessage);
    } else if (isBlank(errorMessage)) {
      this.sharedService.isLoading = true;
      let patientId;
      let patientname;
      this.citusInteractionChannnel = this.selectedDocForm.value.interactionMethod;
      if (
        this.sharedService.isEnableConfig(Config.enableApplessModel) &&
        this.sharedService.isEnableConfig(Config.enableVerificationOfCellAndMobile) &&
        this.citusInteractionChannnel === Constants.magicLink
      ) {
        if (
          this.data.recipient.isVirtual &&
          this.data.recipient.userEMverification.mobileVerified !== 1 &&
          this.data.recipient.userEMverification.emailVerified !== 1
        ) {
          this.commonService.showMessage(this.commonService.getTranslateData('VALIDATION_MESSAGES.MAGICLINK_VALIDATION'));
        }
      }
      if (!isBlank(this.data.recipient) && this.data.recipient.role === Constants.patientGroupId && isBlank(this.data.patient)) {
        patientId = this.data.recipient.id;
        patientname = this.data.recipient.displayName;
        if (!isBlank(this.data.recipient.careGiver) && !isBlank(this.data.recipient.careGiver.id)) {
          patientId = this.data.recipient.careGiver.id;
          patientname = this.data.recipient.careGiver.displayName;
        }
      } else if (this.data.patient) {
        patientId = this.data.patient?.userId || this.data.patient?.userid;
        patientname = this.data.patient.displayName || this.data.patient?.displayname;
      } else if (!isBlank(this.data.recipient) && this.data.recipient.role !== Constants.patientGroupId && isBlank(this.data.patient)) {
        patientId = '';
        this.documentUpload();
      } else if (isBlank(this.data.recipient) && isBlank(this.data.patient)) {
        patientId = '';
        this.documentUpload();
      }
      if (!isBlank(patientId)) {
        this.stage = Constants.stageDocView;
        const reqData: any = {
          patient_id: patientId || this.selectedRecipient.userId,
          documentTypeId: this.folder.id,
          action: IntegrationType.DOCUMENT_SUBMIT
        };
        if (this.sharedService.isMultiAdmissionsEnabled) {
          reqData.admissionId = this.admissionId;
        }
        this.httpService
          .doPost({
            endpoint: APIs.checkDocIntegrationStatus,
            payload: {},
            extraParams: reqData,
            contentType: 'form',
            loader: false,
            skipErrorHandling: true
          })
          .subscribe(
            (res) => {
              this.sharedService.isLoading = false;
              this.externalIdentityDetails = res;
              if (res.success) {
                this.documentUpload();
              }
            },
            (error) => {
              this.sharedService.isLoading = false;
              this.externalIdentityDetails = error;
              this.commonService
                .showAlert({
                  message: `<center>
                  <strong>${error.status.message}</strong> <br/><br/> ${this.commonService.getTranslateData('MESSAGES.YOU_CAN_CONTINUE_ANYWAY')}.
                  </center>`,
                  header: '',
                  cssClass: 'common-alert visit-alert ',
                  buttons: [
                    {
                      text: this.commonService.getTranslateData('BUTTONS.GO_BACK')
                    },
                    {
                      text: this.commonService.getTranslateData('BUTTONS.CONTINUE_ANYWAY')
                    }
                  ]
                })
                .then((confirmation) => {
                  if (confirmation) {
                    this.documentUpload();
                  }
              });
            }
          );
      }
    } else {
      this.sharedService.isLoading = false;
    }
  }
  fetchDefaultFolder(siteId) {
    this.graphqlService.fetchSites(Number(siteId), this.folder.id, '', this.admissionId).then((data) => {
      if (isPresent(data)) {
        this.defaultFilingCenterFolder = data;
      }
    });
  }
  documentUpload(): void {
    let siteId =
      isPresent(this.selectedSite) && this.selectedSite.length === 1 && String(this.selectedSite[0]) !== '0'
        ? this.selectedSite.toString()
        : this.data.patient?.siteId;
    siteId = isPresent(siteId) && typeof Number(siteId) === 'number' ? Number(siteId) : 0;
    if (
      this.folder &&
      isPresent(this.folder?.id) &&
      siteId !== 0 &&
      isPresent(this.folder?.doctype) &&
      this.documentSource !== this.fromFilingCenter &&
      this.sharedService.getConfigValue(Config.documentExchangeMode) === Constants.documentExchangeFC &&
      this.sharedService.isEnableConfig(Config.enableMultiSite)
    ) {
      this.fetchDefaultFolder(siteId);
    }
    const document: any = {};
    if (
      (!this.documentType?.allowRecipientRoles || (this.documentType?.allowRecipientRoles && !isBlank(this.data?.recipient))) &&
      (!this.documentType?.allowAssociatePatient || (this.documentType?.allowAssociatePatient && !isBlank(this.data?.patient))) &&
      (!this.documentType?.allowAssociateRoles || (this.documentType?.allowAssociateRoles && !isBlank(this.data?.associate)))
    ) {
      this.sharedService.isLoading = true;
      if (this.singleDocFile && !isBlank(this.singleDocFile.multiple) && this.singleDocFile.multiple) {
        document.documentFile = this.galleryFile;
      } else if (this.singleDocFile && !isBlank(this.singleDocFile)) {
        document.documentFile = this.singleDocFile;
      } else {
        document.documentFile = this.selectedDocument?.path;
      }
      document.sendDocumentStatus = this.documentType?.sendDocumentWithoutSignature;
      const sendDocName = this.documentSource === this.fromFilingCenter ? this.documentDisplayText : this.galleryDocDisplayText;
      const userDetails = {
        patientId:
          this.documentType?.allowAssociatePatient && this.data?.patient
            ? this.data?.patient?.userId || this.data?.patient?.userid || this.data?.patient?.id
            : 0,
        associateId: this.documentType?.allowAssociateRoles && this.data.associate ? this.data.associate.id : 0,
        associateSignatureProcess: this.documentType?.allowAssociateRoles && this.documentType?.allowRecipientRoles,
        siteId: isPresent(this.selectedSite) ? this.selectedSite.toString() : '0'
      };
      const patientPrePopulatedData = this.setPatientPrePopulatedData();
      const argsForGql = {
        associateSignatureProcess: userDetails.associateSignatureProcess,
        id: Number(this.documentType.id),
        sendDocumentWithoutSignature: this.documentType?.sendDocumentWithoutSignature,
        text: sendDocName,
        associatePatient: Number(userDetails.patientId),
        associateUser: Number(userDetails.associateId),
        sessionId: this.sharedService.userData.authenticationToken,
        metaData: this.setMetaData(patientPrePopulatedData),
        siteId: userDetails.siteId,
        data: {
          tag_id: Number(this.documentType.id),
          documentName: sendDocName,
          patient: {
            id: Number(userDetails.patientId)
          }
        }
      };
      document.argsForGql = JSON.stringify(argsForGql);
      const fileUploadData = {
        endpoint: APIs.proofOfDeliveryMultipleUpload,
        payload: document,
        field: this.singleDocFile ? 'file' : 'url', // key is file for uploading flow, url for filing center, fileId for Document uploads flow
        id: '', // No need to pass Id since API handle this,
        socketId: this.sharedService.socketClientId,
        page: 'docUpload',
        loader: false
      };
      if (this.enableDocumentManagement && !this.singleDocFile) {
        fileUploadData.field = 'fileId';
        fileUploadData.payload.fileId = this.selectedDocument?.fileId;
      }
      this.httpService.fileUpload(fileUploadData).subscribe({
        next: (resp) => {
          this.docDeliveryDetails = resp;
          this.sharedService.isLoading = false;
          if (resp?.gqlResp && !resp?.gqlError) {
            const resultDocument = resp.gqlResp;

            this.folderNameCopyFilingCenter = this.sharedService.isEnableConfig(Config.enableMultiSite)
              ? this.defaultFilingCenterFolder
              : resultDocument.type.fromFilingCenter;
            this.ownerIdCopyFilingCenter = resultDocument.ownerId;
            this.filenameFormatCopyFilingCenter = resultDocument.type?.fileNameFormatText;
            if (!this.documentType.sendDocumentWithoutSignature) {
              this.redirectToPage(resultDocument, this.userData.tenantId);
            } else {
              const docNameDocument = resultDocument.document.displayText;
              const fileUrlDownload = `${environment.apiServer}${Urls.writableFilePath}${this.ownerIdCopyFilingCenter}${Urls.documentSignedpdfPath}${docNameDocument}.${Constants.documentTypes.pdf}`;
              if (isPresent(this.folderNameCopyFilingCenter) && this.externalIdentityDetails?.success) {
                const patientid = resultDocument.associatedP;
                let esi = '';
                this.sharedService.isLoading = true;
                this.graphqlService.getExternalSystemsByPatient(patientid, this.admissionId).valueChanges.subscribe(
                  ({ data }) => {
                    const { externalSystems } = data.getSessionTenant;
                    esi = this.sharedService.getEsiFromExternalSystem(externalSystems[0]);
                    let patientDataList = {
                      displayName: '',
                      firstName: '',
                      lastName: ''
                    };
                    if (patientPrePopulatedData && patientPrePopulatedData.patient) {
                      patientDataList = {
                        displayName: '',
                        firstName: patientPrePopulatedData.patient.firstName,
                        lastName: patientPrePopulatedData.patient.lastName
                      };
                    }
                    const fileNameDetails = {
                      resultDocument,
                      esi,
                      typeName: this.documentType.name,
                      associatePatient: patientDataList
                    };
                    const fileTargetNameUpdated = this.sharedService.setDocFilename(fileNameDetails);
                    let exchangeData = deepParseJSON(resultDocument.exchangeData);
                    if (exchangeData) {
                      exchangeData = JSON.stringify(exchangeData);
                    } else {
                      exchangeData = JSON.stringify(resultDocument.exchangeData);
                    }
                    const params = {
                      fileUrlDownload,
                      fileTargetNameUpdated,
                      ownerIdCopyFilingCenter: parseInt(this.ownerIdCopyFilingCenter, 10),
                      exchangeData,
                      folderNameCopyFilingCenter: this.folderNameCopyFilingCenter,
                      enableMultisite: this.enableMultiSite,
                      siteId
                    };
                    this.graphqlService.copyToFilingCenterAfterSignature(params).subscribe(
                      () => {
                        this.sharedService.isLoading = false;
                        const submitDocumentDes = {
                          displayName: this.userData.displayName,
                          documentName: this.data.documentName,
                          status: resultDocument.signatureStatus,
                          sendDocumentWithoutSignature: this.documentType.sendDocumentWithoutSignature,
                          obtainSignature: this.documentType.obtainSignature,
                          personalSignature: this.data.allowPersonalSignature
                        };
                        this.sharedService.trackActivity({
                          type: `${Activity.signatureRequest} - ${resultDocument.signatureStatus}`,
                          name: Activity.sendDocument,
                          des: {
                            data: submitDocumentDes,
                            desConstant: Activity.submitDocumentDes
                          },
                          linkageId: this.userData.displayName
                        });
                      },
                      () => {
                        this.sharedService.isLoading = false;
                      }
                    );
                  },
                  () => {
                    this.sharedService.isLoading = false;
                  }
                );
              } else if (!this.externalIdentityDetails?.success) {
                this.sharedService.trackActivity({
                  type: Activity.signatureRequest,
                  name: Activity.copiedSignedSignatureToFilingCenterError,
                  des: {
                    data: {
                      reason: JSON.stringify(this.externalIdentityDetails),
                      displayName: this.userData.displayName,
                      documentId: resultDocument.id
                    },
                    desConstant: Activity.copiedSignedSignatureToFilingCenterErrorDes
                  },
                  linkageId: this.documentDisplayText
                });
              }
              // CHP-18680 Removed moveFilingCenterToArchive from client side after signature request completed
              this.obtainSignPollingData(resultDocument);
              this.commonService.showMessage(this.commonService.getTranslateData('SUCCESS_MESSAGES.SUBMITTED_DOCUMENT'));
              const signedSignatureReq = {
                displayName: this.userData.displayName,
                documentName: this.data.documentName
              };
              this.sharedService.trackActivity({
                type: Activity.signatureRequest,
                name: Activity.signedSignatureRequest,
                des: {
                  data: signedSignatureReq,
                  desConstant: Activity.signedSignatureReqDes
                }
              });
              if (this.sharedService.isEnableConfig(Config.signatureServerside)) {
                this.commonService.redirectToPage('/document-center/pending-documents');
              }
            }
          } else {
            // Handle GraphQL errors from proof-of-delivery API (status 200 but with gqlError)
            this.handleFileUploadError(resp?.gqlError ?? resp?.msg);
          }
        },
        error: () => {
          this.handleFileUploadError();
        }
      });
    }
  }
  redirectToPage(document: any, senderTenant: any): void {
    const selectedFileDetails = {
      documentDisplayText: this.documentDisplayText,
      originalFileName: this.actualFileName,
      isFileFromGallery: this.documentSource === this.fromGallery
    };
    const navigationExtras: NavigationExtras = {
      state: {
        folder: {
          document,
          senderTenant,
          selectedFileDetails,
          recipientDetails: this.selectedRecipient,
          data: this.data,
          docDeliveryDetails: this.docDeliveryDetails,
          citusInteractionChannnel: this.selectedDocForm.value.interactionMethod
        },
        selectDocument: !isBlank(this.selectDocState) ? this.selectDocState : this.documentType
      }
    };
    this.router.navigate(['/document-center/view-document'], navigationExtras);
  }

  setPatientPrePopulatedData(): PatientInformationForPrepopulation {
    const patientInformationForPrepopulation: PatientInformationForPrepopulation = {};
    let item: PatientInformation = {};
    const patientGuid = '';
    let patientMrn = '';
    if (
      this.sharedService.automaticLinkedItems &&
      (isPresent(this.sharedService.automaticLinkedItems.patientMrn) || isPresent(patientInformationForPrepopulation.patientGuid))
    ) {
      patientInformationForPrepopulation.patientMrn = this.sharedService.automaticLinkedItems?.patientMrn || '';
      patientInformationForPrepopulation.patientGuid = this.sharedService.automaticLinkedItems?.patientGuid || '';
      patientInformationForPrepopulation.patient = this.sharedService.automaticLinkedItems.patient
        ? this.sharedService.automaticLinkedItems.patient
        : {};
      return patientInformationForPrepopulation;
    }
    if (isPresent(this.data.patient)) {
      item = deepCopyJSON(this.data.patient);
      const recipient = this.data.recipient ? deepCopyJSON(this.data.recipient) : undefined;
      item.dateOfBirth = isPresent(item.dob) ? formatDate(item.dob, Constants.dateFormat.mdy) : '';
      item.firstName = item?.firstname || '';
      item.lastName = item?.lastname || '';
      item.email = item?.email || '';
      item.id = item.userId ? item.userId : item.id ? Number(item.id) : 0;
      patientMrn = item?.IdentityValue || '';
      if (isPresent(this.data.patient?.createdPatientDetails)) {
        const createdPatientDetails = this.data.patient?.createdPatientDetails;
        item.dateOfBirth = isPresent(createdPatientDetails?.dob) ? formatDate(createdPatientDetails.dob, Constants.dateFormat.mdy) : item.dateOfBirth;
        item.isVirtual = false;
        patientMrn = isPresent(createdPatientDetails.mrn) ? createdPatientDetails.mrn : patientMrn;
      }
      if (
        recipient &&
        recipient.alternate &&
        this.sharedService.getConfigValue(Config.defaultPatientsWorkflow) === Constants.workflowAlternateContact
      ) {
        this.data.recipient.id = recipient?.alternate?.id;
        this.data.recipient.cmisId = recipient?.alternate?.cmisid;
        this.data.recipient.altPatientid = recipient.userId;
        this.data.recipient.isAlternate = !isBlank(recipient?.alternate);
        this.isRecipientCaregiver = true;
        const patientData = removeUndefinedKeysJSON({
          altPatientDob: item.dateOfBirth,
          altPatientEmail: recipient?.email || '',
          altPatientFirstName: recipient?.firstName || '',
          altPatientid: recipient.userId,
          altPatientLastName: recipient?.lastName || '',
          altPatientMobile: recipient?.mobile || '',
          altPatientName: recipient?.displayName || '',
          cmisId: recipient?.alternate?.cmisid,
          countryCode: recipient?.alternate?.countryCode,
          displayName: recipient?.displayName || '',
          id: recipient?.alternate?.id,
          userId: recipient?.alternate?.id,
          isAlternate: true,
          isVirtual: false,
          patientIdentity: recipient?.patientIdentity,
          relation: recipient?.alternate?.relation,
          role: recipient?.alternate?.roleId,
          userEMverification: recipient?.userEMverification,
          userName: recipient?.alternate?.username
        });
        item = Object.assign(item, patientData);
      }
    } else if (isPresent(this.data.recipient)) {
      item = this.data.recipient;
      item.dateOfBirth = isPresent(item.dateOfBirth) ? formatDate(item.dateOfBirth, Constants.dateFormat.mdy) : '';
      patientMrn = item.patientIdentity && item.patientIdentity.IdentityValue ? item.patientIdentity.IdentityValue : '';
      item.email = item?.userName || '';
      item.id = item.userId ? item.userId : item.id ? Number(item.id) : 0;
      if (isPresent(item.careGiver)) {
        this.isRecipientCaregiver = true;
        item.firstName = item.careGiver?.firstName || '';
        item.lastName = item.careGiver?.lastName || '';
        item.dateOfBirth = isPresent(item.careGiver.dob) ? formatDate(item.careGiver.dob, Constants.dateFormat.mdy) : '';
        item.displayName = item.careGiver?.displayName || '';
        item.mobile = item.careGiver?.mobile || '';
        item.email = item.careGiver?.userName || '';
        item.gender = item.careGiver.gender && item.careGiver.gender !== '' ? item.careGiver.gender : '';
      }
      if (item.alternateContacts || item.alternate) {
        this.isRecipientCaregiver = !isBlank(item.alternateContacts) || !isBlank(item.alternate);
        item.altPatientFirstName = item?.firstName || '';
        item.altPatientLastName = item?.lastName || '';
        item.altPatientDob = item?.dateOfBirth || '';
        item.altPatientName = item?.displayName || '';
        item.altPatientMobile = item?.mobile || '';
        item.altPatientEmail = item?.email || '';
        item.altPatientid = item?.id ? +item.id : +item.userId;
        item.isAlternate = this.isRecipientCaregiver;
        item.isVirtual = false;
        if (item.alternate) {
          item.cmisId = +item.alternate.cmisid;
          item.userName = item.alternate.username;
          item.id = +item.alternate.id;
          item.userId = item.alternate.id;
        }
      }

      if (item.alternateContacts) {
        delete item.alternateContacts;
      }
      if (item.alternate) {
        delete item.alternate;
      }
    }
    patientInformationForPrepopulation.patientMrn = patientMrn;
    patientInformationForPrepopulation.patientGuid = patientGuid;
    patientInformationForPrepopulation.patient = item;
    return patientInformationForPrepopulation;
  }
  setMetaData(patientPrePopulatedData): string {
    if (this.sharedService.isEnableConfig(Config.enableApplessModel)) {
      this.documentType = {
        ...this.documentType,
        enableApplessWorkflow: this.citusInteractionChannnel === Constants.magicLink,
        applessDevices: this.documentType.enableApplessWorkflow ? Constants.applessDevices : ''
      };
    }
    let alternateId;
    if (!isBlank(this.data.recipient?.alternate)) {
      alternateId = this.data.recipient.alternate.id;
    }
    let signatureByUsers = [];
    if (!isBlank(this.data.recipient)) {
      signatureByUsers = alternateId ? [alternateId] : [Number(this.data?.recipient?.id)];
    }

    let signatureByAssociateCareGiverUsers = [];
    if (
      !isBlank(this.data?.recipient) &&
      !isBlank(this.data?.recipient?.careGiver) &&
      this.sharedService.getConfigValue(Config.defaultPatientsWorkflow) === Constants.userCaregiver
    ) {
      signatureByAssociateCareGiverUsers = [this.data?.recipient?.careGiver.id];
    } else if (
      !isBlank(this.data?.recipient) &&
      this.sharedService.getConfigValue(Config.defaultPatientsWorkflow) === Constants.workflowAlternateContact &&
      this.data.recipient.isAlternate
    ) {
      signatureByAssociateCareGiverUsers = [this.data?.recipient?.altPatientid];
    }
    const data = JSON.stringify({
      attributes: {
        data: [
          {
            crossTenant: 0,
            allowPendingApproveSignature: this.documentType.allowPendingApproveSignature,
            admissionId: this.admissionId,
            owner: this.userData.userCmisId,
            userType: Constants.messageUserType,
            callbellOwner: this.userData.userId,
            userId: this.userData.userCmisId,
            institutionTenantId: this.userData.tenantId,
            institutionCmisTenantId: this.userData.tenantCmisId,
            institutionTenantName: this.userData.tenantName,
            isDeleted: false,
            parentFolderId: Constants.parentFolderId,
            fileName: this.documentSource === this.fromFilingCenter && !this.enableDocumentManagement ? this.selectedDocument?.name : '',
            isFileFromGallery: this.documentSource === this.fromGallery,
            // isFileFromGallery  true in case of gallery, camera
            // isFileFromGallery  false if the file is selected from internal files
            documentName:
              this.documentSource === this.fromFilingCenter
                ? this.documentDisplayText
                : this.documentSource === this.fromGallery
                  ? this.galleryDocDisplayText
                  : '',
            documentOrginalName:
              this.documentSource === this.fromFilingCenter
                ? this.selectedDocument.name.split('.').shift()
                : this.documentSource === this.fromGallery
                  ? this.galleryFileName[0]
                  : '',
            documentFormat:
              this.documentSource === this.fromFilingCenter
                ? this.selectedDocument.name.split('.').pop()
                : this.documentSource === this.fromGallery
                  ? this.galleryFileName[1]
                  : Constants.defaultDocumentFormat,
            documentMimeType:
              this.documentSource === this.fromFilingCenter
                ? this.selectedDocument.type
                : this.documentSource === this.fromGallery
                  ? this.galleryFile[this.galleryFile.length - 1].type
                  : Constants.defaultDocumentMimeType,
            signatureStateMachineState: Signature.signatureStatus.signaturePendingStatus, // 1 stage,2,,
            signatureStateMachine: Constants.signatureStateMachine, // complex,
            fileExtension: '',
            // "multiSignatureOptions": "", //me,others,meAndOthers
            signatureDocument: true,
            documentSourceType: isPresent(Constants.docSourceType[this.documentSource]) ? Constants.docSourceType[this.documentSource] : '', // gallery,existingPDF
            documentSource: '', // camera format, qaulity
            documentTag: this.documentType.id, // signature,staffReport,homeReport
            documentTypeDisplay: this.documentType,
            documentVisibleToRoles: '',
            documentVisibleToUsers: '',
            gpsLocation: [],
            createdDate: '',
            updatedDate: '',
            allowFileNameEditing: this.documentType.allowFileNameEditing,
            maxSignatoriesAllowed: this.documentType.maxSignatoriesAllowed,
            allowPersonalSignature: this.documentType.allowPersonalSignature,
            version: ConfigValues.appVersion, // CMIS Version
            allowMoreOptions: true,
            disableTooltip: true,
            obtainSignature: this.documentType.obtainSignature,
            obtainSignatureByRoles: '',
            obtainSignatureByUsers: '',
            isSignable: true,
            signatureByRoles: this.documentType.signatureByRoles ? [this.documentType.signatureByRoles] : [],
            signatureByUsers,
            signatureByAssociateCareGiverUsers,
            signatureByCmisUsers: !isBlank(this.data?.recipient?.cmisId) ? [this.data.recipient.cmisId] : [],
            signatureByAssociateStaff: !isBlank(this.data?.associate) ? [this.data.associate.id] : [],
            signatureByCmisAssociateStaff: !isBlank(this.data.associate?.cmisId) ? [this.data.associate.cmisId] : [],
            isEditable: true,
            isEditedInstance: false,
            editedInstanceSource: '',
            editableByRoles: '',
            editableByUsers: '',
            readByUsers: [this.userData.userId],
            readByUsersHistory: [this.userData.userId],
            readByUsersSignedHistory: [],
            assignToPatient: this.documentType.allowRecipientUsers,
            assignedPatients: !isBlank(this.data.recipient) ? (alternateId ? [alternateId] : [Number(this.data.recipient.id)]) : [],
            notifyOnSubmitSignatureUsers: this.documentType?.notifyOnSubmitSignatureUsers ? this.documentType.notifyOnSubmitSignatureUsers : [],
            notifyOnSubmitSignatureRoles: this.documentType?.notifyOnSubmitSignatureRoles ? this.documentType.notifyOnSubmitSignatureRoles : [],
            enableApplessWorkflow: this.documentType.enableApplessWorkflow,
            applessDevices: this.documentType.applessDevices,
            prepopulationInformation: patientPrePopulatedData,
            externalDocumentId:
              isPresent(this.sharedService.automaticLinkedItems) && isPresent(this.sharedService.automaticLinkedItems.externalDocumentId)
                ? this.sharedService.automaticLinkedItems.externalDocumentId
                : '',
            documentReferanceId:
              isPresent(this.selectedDocument?.name) && this.selectedDocument.name.indexOf('_') !== -1
                ? this.selectedDocument.name.split('-').pop().split('.').shift()
                : 0,
            isRecipientPatient: this.isRolePatient && !this.isRecipientCaregiver,
            completedDocumentRecipients: this.recipientsCompletedDocumentIDs
          }
        ]
      }
    });
    return data;
  }

  get isRolePatient() {
    return isPresent(this.data?.recipient?.role) && this.data?.recipient?.role === UserGroup.PATIENT;
  }

  trackActivity(name: string): void {
    const data = {
      displayName: this.userData.displayName,
      docName: name
    };
    this.sharedService.trackActivity({
      type: Activity.signatureRequest,
      name: Activity.tagSelect,
      des: { data, desConstant: Activity.tagSelectDescription },
      linkageId: this.userData.displayName
    });
  }
  
  handleFileUploadError(errorMsg?: string): void {
    this.sharedService.consoloLoader = false;
    this.sharedService.isLoading = false;
    const errorMessage = errorMsg || this.commonService.getTranslateData('ERROR_MESSAGES.TECHNICAL_DIFFICULTY_TRY_AGAIN');
    // Track error for analytics
    this.sharedService.trackActivity({
      type: Activity.signatureRequest,
      name: Activity.sendDocument,
      des: {
        data: {
          displayName: this.userData?.displayName ?? '',
          documentType: this.documentType?.name ?? '',
          errorMessage: errorMsg ?? '',
          isConsoloFlow: !isBlank(this.sharedService.automaticLinkedItems)
        },
        desConstant: Activity.documentSendErrorDes
      },
      linkageId: this.userData?.displayName ?? ''
    });

    // Show alert with OK button for Consolo flow
    if (isPresent(this.sharedService.automaticLinkedItems)) {
      this.commonService
        .showWarningAlert({
          message: errorMessage,
          icon: 'warning',
          iconColor: '#eb445a',
          header: this.commonService.getTranslateData('LABELS.ERROR')
        })
        .then(() => {
          this.sharedService.consoloAppExit(1000, 'MESSAGES.CONSOLO_ATTEMPTING_CLOSE');
        });
    } else {
      this.commonService.showMessage(errorMessage);
    }
  }

  validationForRecipients(msg: any): string {
    let errorMessage: string;
    if (this.documentType.allowRecipientRoles && isBlank(this.data?.recipient)) {
      errorMessage = this.commonService.getTranslateData('VALIDATION_MESSAGES.MANDATORY_RECIPIENTS');
    }
    if (this.documentType.allowAssociatePatient && isBlank(this.data?.patient)) {
      errorMessage = this.commonService.getTranslateData('VALIDATION_MESSAGES.MANDATORY_PATIENTS');
    }
    if (
      this.sharedService.isMultiAdmissionsEnabled &&
      (this.documentType?.allowAssociatePatient ? !isBlank(this.data.patient) : this.isRolePatient && !isBlank(this.data?.recipient)) &&
      isBlank(this.data?.patientAdmissionId)
    ) {
      errorMessage = this.commonService.getTranslateData('VALIDATION_MESSAGES.MANDATORY_ADMISSION');
    }
    if (this.documentType?.allowAssociateRoles && isBlank(this.data?.associate)) {
      errorMessage = this.commonService.getTranslateData('VALIDATION_MESSAGES.MANDATORY_DELEGATED_USER');
    }
    if (isBlank(this.documentDisplayText)) {
      errorMessage = this.commonService.getTranslateData('VALIDATION_MESSAGES.MANDATORY_DOCUMENT');
    }
    if (!isBlank(errorMessage)) {
      this.commonService.showMessage(errorMessage);
    }
    return errorMessage;
  }

  obtainSignPollingData(resultDocument: any): void {
    const obtainSignPollingData: any = {};
    if (this.documentType.notifyOnSubmitSignatureUsers) {
      obtainSignPollingData.notifyOnSubmitSignatureUsers = this.documentType.notifyOnSubmitSignatureUsers.split(',');
    } else if (this.documentType.notifyOnSubmitSignatureRoles) {
      obtainSignPollingData.notifyOnSubmitSignatureRoles = this.documentType.notifyOnSubmitSignatureRoles.split(',');
    }
    obtainSignPollingData.environment = environment.alias;
    obtainSignPollingData.serverBaseUrl = environment.apiServer;
    obtainSignPollingData.apiVersion = ConfigValues.config.apiVersion;
    obtainSignPollingData.signed = true;
    obtainSignPollingData.notificationMessageData = resultDocument;
    obtainSignPollingData.tenantId = this.userData.tenantId;
    obtainSignPollingData.tenantName = this.userData.tenantName;
    if (this.documentType.allowRecipientRoles && !isBlank(this.data.recipient)) {
      obtainSignPollingData.user = this.data.recipient.id;
    }
    if (this.selectedDocForm.value.interactionMethod !== Constants.magicLink) {
      this.socketService.emitEvent(Socket.obtainSignPollingToServer, obtainSignPollingData);
    }
  }
  changePersonalSignatureStatus(eve: any): void {
    this.data.allowPersonalSignature = eve?.detail?.checked;
  }
  checkConsoloValidations(): boolean {
    if (this.documentType.allowRecipientRoles) {
      if (!isBlank(this.consoloData.recipients.id)) {
        this.data.recipient = this.consoloData.recipients;
      } else {
        this.commonService.showMessage(ConfigValues.messages.consolo.autoLinkRecipientError);
        return false;
      }
    }
    if (this.documentType.allowAssociateRoles) {
      if (!isBlank(this.consoloData.associate.id)) {
        this.data.associate = this.consoloData.associate;
      } else {
        this.commonService.showMessage(ConfigValues.messages.consolo.autoLinkAssociateError);
        return false;
      }
    }
    if (this.documentType.allowAssociatePatient) {
      if (!isBlank(this.consoloData.patientMrn) || !isBlank(this.consoloData.patientGuid)) {
        this.data.patient = this.consoloData.patient;
      } else {
        this.commonService.showMessage(ConfigValues.messages.consolo.autoLinkAssociateError);
        return false;
      }
    }
    if (
      !isBlank(this.consoloData.recipients.id) ||
      !isBlank(this.consoloData.associate.id) ||
      !isBlank(this.consoloData.patientMrn) ||
      !isBlank(this.consoloData.patientGuid)
    ) {
      return true;
    }
  }

  showMultiSelectFileUploadPopup(type: string) {
    this.modalController
      .create({
        component: MultipleFileSelectionComponent,
        componentProps: {
          sourceType: type
        }
      })
      .then((modal: any) => {
        modal.present();
        modal.onDidDismiss().then(({ data }) => {
          if (data) {
            this.loadDocument(data.multipleFileUploadData);
          }
        });
      });
  }

  /**
   * Change document name when allowFileNameEditing flag enabled
   * @param event keyPress event text-box value
   */
  changeFileName(event) {
    if (this.documentType.allowFileNameEditing) {
      this.documentDisplayText = event?.target?.value;
      this.galleryDocDisplayText = event?.target?.value;
    }
  }

  async selectRecipientsCompleteDoc(): Promise<void> {
    const errorMessage = this.validationForRecipients('');
    let patientId;
    let reqData;
    let tenantId;
    if (isBlank(errorMessage)) {
      if (!isBlank(this.data?.recipient) && this.data?.recipient?.role?.toString() === UserGroup.PATIENT.toString() && isBlank(this.data?.patient)) {
        patientId = this.data?.recipient?.id;
        if (!isBlank(this.data.recipient?.alternate)) {
          tenantId = this.data.recipient?.alternate?.tenantId;
        }
        if (!isBlank(this.data.recipient?.careGiver) && !isBlank(this.data.recipient?.careGiver?.id)) {
          patientId = this.data.recipient.careGiver.id;
          tenantId = this.data.recipient.careGiver?.tenantId;
        }
      } else if (this.data.patient) {
        patientId = this.data.patient?.userId || this.data.patient?.userid;
      } else if (!isBlank(this.data.recipient) && this.data.recipient.role !== Constants.patientGroupId && isBlank(this.data.patient)) {
        patientId = '';
      } else if (isBlank(this.data.recipient) && isBlank(this.data.patient)) {
        patientId = '';
      }
      if (!isBlank(patientId)) {
        reqData = {
          userid: patientId || this.selectedRecipient.userId,
          tenantId
        };
      }

      const modal = await this.modalController.create({
        component: CompletedDocumentRecipientsComponent,
        componentProps: { recipient: reqData, pageName: Constants.copyDocumentPageName.documentCenter, admissionId: this.admissionId },
        id: 'choose-recipient-copy'
      });
      await modal.present();
      const { data } = await modal.onWillDismiss();
      if (data) {
        if (!isBlank(data?.selectedStaffRoles)) {
          this.recipientsCompletedDocumentIDs = data?.selectedStaffRoles?.join(',');
        }
        if (!isBlank(data.selectedStaff)) {
          const getAllNames = await data.selectedStaff.map((item) => {
            return item.displayName;
          });
          this.recipientsCompletedDocument = getAllNames.join(', ');
        }
      }
    }
  }

  checkValidInputDocumentName(event: any): void {
    if (isHTMLTagAvailable(event?.target?.value)) {
      this.commonService.showMessage(this.commonService.getTranslateData('ERROR_MESSAGES.INVALID_INPUT_MSG_WITH_TAG'));
      event.target.value = '';
      this.documentDisplayText = '';
    }
  }

  get totalFileSize(): number {
    return this.galleryFile.reduce((total, item) => total + item.size, 0);
  }

  get admissionId(): string {
    return this.sharedService.isMultiAdmissionsEnabled && !isBlank(this.data) ? this.data.patientAdmissionId : undefined;
  }

  /** open modal for select admission */
  async selectAdmission(): Promise<void> {
    const params = {
      admissionId: this.data.patient?.patientAdmissionId ? this.data.patient?.patientAdmissionId : '',
      userId: this.documentType.allowAssociatePatient
        ? this.data.patient?.userId || this.data.patient?.userid
        : this.isRolePatient && this.data.recipient?.userId,
      siteIds: isPresent(this.selectedSites) && this.selectedSites.length === 1 ? this.selectedSites : undefined
    };
    this.sharedService.selectAdmission(params, undefined, AdmissionComponent, (admission: any) => {
      if (this.documentType.allowAssociatePatient) {
        this.resetAllPatientDependentData();
      }
      if (isObject(admission) && this.data) {
        if (admission.siteId) {
          this.selectedSite = admission.siteId;
        }
        this.disableRecipientField = false;
        this.data.patientAdmissionId = admission.admissionId;
        this.admissionDisplayText = admission.admissionName;
        this.fetchSiteRegistrationId(this.selectedSite);
      }
    });
  }
}
