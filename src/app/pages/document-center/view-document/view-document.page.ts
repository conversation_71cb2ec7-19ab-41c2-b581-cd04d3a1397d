import { Component, OnInit, ElementRef, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectorRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { PopoverController, ModalController, ActionSheetController, AlertOptions } from '@ionic/angular';
import { environment } from 'src/environments/environment';
import { Constants, UserGroup } from 'src/app/constants/constants';
import { Activity } from 'src/app/constants/activity';
import { Urls } from 'src/app/constants/urls';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { uniqueId, isBlank, isObject, isPresent, deepCopyJSON, parseNumbers } from 'src/app/utils/utils';
import { SignatureMorePalettesComponent } from 'src/app/pages/document-center/signature-more-palettes/signature-more-palettes.component';
import { CommonService } from 'src/app/services/common-service/common.service';
import { SignatureComponent } from 'src/app/components/signature/signature.component';
import { Config } from 'src/app/constants/config';
import { ConfigValues } from 'src/assets/config/config';
import { SocketService } from 'src/app/services/socket-service/socket.service';
import { Socket } from 'src/app/constants/socket';
import { Signature } from 'src/app/constants/signature';
import * as moment from 'moment';
import { SiteListByUserIdResponse } from 'src/app/interfaces/document-request-signature';
import { Keyboard } from '@capacitor/keyboard';
import { PageRoutes } from 'src/app/constants/page-routes';

@Component({
  selector: 'app-view-document',
  templateUrl: './view-document.page.html',
  styleUrls: ['./view-document.page.scss']
})
export class ViewDocumentPage implements OnInit, OnDestroy {
  @ViewChild('signDocumentWrapper', { static: true }) signDocument: ElementRef;
  documentId: number;
  currentImagePath: string = Urls.imagePlaceholder;
  documentInfo: any;
  currentDocPage = 1;
  totalDocsCount = 1;
  downloadUrl: string;
  isSigned = false;
  platform: boolean;
  userData: any;
  platformValue: string;
  isCheckboxCross = false;

  constants: any;
  signature: any;
  addObjects: any = [];
  inBounds = true;
  signatureTypeTagData: any;
  recipientDetails: any;
  signatureDocumentDetails: any;
  data: any = {};
  paletteButtons = false;
  plusButton: boolean;
  isUploadDocView = false;
  target: any;
  checkboxGroupObject: any = {
    showCheckboxGroup: true,
    addToExistingGroup: false,
    isNewCheckboxGroup: false,
    checkboxLabel: true
  };
  documentSignersDetails: any = [];
  sendButtonText: any;
  selectDocState: any;
  enableSign = false;
  selectedDocumentSignerDetails: any;
  selfSignButtonClicked = false;
  myPaletteSign = false;
  docAttachment: any = {};
  selectedFileDetails: any;
  textValue = {};
  checkboxLabelValue = {};
  paletteSize: any = {};
  saveButtonValue: string;
  sendButtonValue: string;
  startSigningButtonValue: string;
  citusInteractionChannnel: string;
  showSignDoc = false;
  myOutOfBounds = {
    top: false,
    right: false,
    bottom: false,
    left: false
  };
  borderColor: string;
  dummyDroppedObjects = [];
  displayTypeConditions: any;
  documentOwnerId: string;
  startSign = false;
  finishSign = false;
  approvePending = false;
  mySignatureNeeded = false;
  snapAndSignApproval = false;
  associateSignatureByRoles: any;
  signatureGroupDetails: any = [];
  autoNavigateObjects: any;
  signaturesByUsers: any;
  signaturesByUsersId: string;
  associateSignaturesByUsersId: any;
  associatePhysicaianSignature = false;
  finishButtonName: string;
  signatureCrossTenant: any;
  ownerIdCopyFilingCenter: any;
  folderNameCopyFilingCenter: any;
  filenameFormatCopyFilingCenter: any;
  ownerId: any;
  nextPage = false;
  approverSignPending = false;
  represntativeConfirmSignature = false;
  snapSignPendingApproveUser = false;
  totalSignPageCount: any;
  applessDocumentWorkflow = false;
  applessDocumentWorkFlowComplete = false;
  consoloWorkFlow = false;
  applessDocumentWorkFlowCompletedMessage: string;
  initiateResign = false;
  paletteStatuses = Signature.paletteStatuses;
  paletteStatus: string = this.paletteStatuses.notSigned;
  documentRootInfo: any;
  dateWithSign: any;
  documentDisplayText = '';
  caregiverAssociatePatient = '';
  customAlertOptions: AlertOptions = {
    header: '',
    cssClass: 'select-option-alert'
  };
  isMobilePlatform = false;
  scrollTopPosition = 0;
  prepopulationData: any;
  textareaPalette: any;
  textareaPaletteEmptyWidth: any;
  finishButtonClickCount = 0;
  autoNavigateItem = [];
  allGroupName = [];
  siteIdForFolderFetch: number;
  isiPad = false;
  allowDesignView = true;
  isMobileWeb = false;
  isConsolo = false;
  enableSaveSignatureToProfile = false;
  confirmSaveSignToProfile = false;
  constructor(
    private readonly route: ActivatedRoute,
    private readonly graphqlService: GraphqlService,
    public readonly sharedService: SharedService,
    private readonly router: Router,
    private readonly popoverController: PopoverController,
    private readonly commonService: CommonService,
    private readonly modalController: ModalController,
    private readonly socketService: SocketService,
    private readonly actionSheetController: ActionSheetController,
    private readonly cdRef: ChangeDetectorRef
  ) {
    if (this.sharedService.platform.is('ipad') || this.sharedService.platform.is('tablet')) {
      this.isiPad = true;
    }
    this.isMobilePlatform = this.sharedService.platform.is('capacitor');
    this.isMobileWeb = this.sharedService.platform.is('mobileweb');
    this.constants = Constants;
    this.signature = Signature;
    this.userData = this.sharedService.userData;
    this.platformValue = this.sharedService.platformValue;
    this.saveButtonValue = this.commonService.getTranslateData('BUTTONS.SAVE');
    this.sendButtonValue = this.commonService.getTranslateData('BUTTONS.SEND');
    this.customAlertOptions.header = this.commonService.getTranslateData('TITLES.SELECT_A_SIGNER');
    this.startSigningButtonValue = this.commonService.getTranslateData('BUTTONS.START_SIGNING');
    this.isConsolo = isPresent(this.sharedService.automaticLinkedItems);
    if (
      this.router.getCurrentNavigation()?.extras.state &&
      isPresent(this.router.getCurrentNavigation()?.extras.state.selectDocument) &&
      isPresent(this.router.getCurrentNavigation()?.extras.state.folder)
    ) {
      this.isUploadDocView = true;
      this.paletteButtons = true;
      this.checkboxGroupObject = {
        showCheckboxGroup: true,
        addToExistingGroup: false,
        isNewCheckboxGroup: false,
        checkboxLabel: true
      };
      this.selectDocState = this.router.getCurrentNavigation().extras.state.selectDocument;

      if (!isBlank(this.selectDocState)) {
        this.signatureTypeTagData = this.selectDocState.folder ? this.selectDocState.folder.doctype : this.selectDocState;
        this.docAttachment.archiveAfterAttachement = this.signatureTypeTagData.allowArchiveSignature;
        this.dateWithSign = this.signatureTypeTagData.dateWithSign;
        this.isCheckboxCross = this.selectDocState?.folder?.doctype?.checkboxImageBehavior === 2;
      }
      const details = this.router.getCurrentNavigation().extras.state.folder;
      if (!details.recipientDetails) {
        this.recipientDetails = details.data.patient;
      } else {
        this.recipientDetails = details.recipientDetails;
      }
      this.documentId = details.document.id;
      this.signatureDocumentDetails = details.document;
      this.confirmSaveSignToProfile = this.signatureDocumentDetails?.confirmSaveSignToProfile;
      this.signaturesByUsers = details.document.signatureByUsers;
      this.documentDisplayText = details.document.displayText.text;
      this.totalDocsCount = this.signatureDocumentDetails.document.pageCount;
      this.sharedService.docDetails.documentUniqueName = this.signatureDocumentDetails.document.displayText;
      if (isPresent(this.signatureDocumentDetails?.type)) {
        this.allowDesignView = this.signatureDocumentDetails.type?.allowDesignView;
      }
      this.citusInteractionChannnel = details.citusInteractionChannnel;
      if (!isBlank(details.docDeliveryDetails)) {
        const { docDeliveryDetails } = details;
        this.paletteSize.width = docDeliveryDetails.location.width;
        this.paletteSize.height = docDeliveryDetails.location.height;
        this.paletteSize.signWidth = docDeliveryDetails.location.signwidth;
        this.paletteSize.signHeight = docDeliveryDetails.location.signheight;
        this.paletteSize.minWidth = docDeliveryDetails.location.minWidth;
        this.paletteSize.minHeight = docDeliveryDetails.location.minHeight;
        this.paletteSize.maxWidthForText = docDeliveryDetails.location.maxWidthForText;
        this.paletteSize.maxHeightForText = docDeliveryDetails.location.maxHeightForText;
      }
      this.selectedFileDetails = details.selectedFileDetails;
      this.sharedService.docDetails.actualFileName = this.selectedFileDetails.documentDisplayText;
      setTimeout(() => {
        if (!isBlank(this.signatureTypeTagData)) {
          this.data.recipient = {};
          this.data.patient = {};
          this.data.associate = {};
          this.data.tag_id = this.signatureTypeTagData.id;
          this.data.allowPersonalSignature = details.data.allowPersonalSignature;
          if (this.signatureTypeTagData.allowRecipientRoles) {
            this.data.recipient = details.data.recipient;
          }
          if (this.signatureTypeTagData.allowAssociatePatient) {
            this.data.patient = details.data.patient;
          }
          if (this.signatureTypeTagData.allowAssociateRoles) {
            this.data.associate = details.data.associate;
          }
          if (!this.signatureTypeTagData.sendDocumentWithoutSignature) {
            if (this.data.allowPersonalSignature) {
              this.borderColor = Signature.backgroundColor.meBorderColor; // Need to make dynamic
              this.pushToDocSignerDetails(this.userData.userId, this.commonService.getTranslateData('LABELS.ME'), true, '', '');
            }
            if (this.signatureTypeTagData.obtainSignature && !this.signatureTypeTagData.allowAssociateRoles) {
              this.sendButtonText = !this.data.allowPersonalSignature ? this.saveButtonValue : this.startSigningButtonValue;
              this.borderColor = Signature.backgroundColor.associateBorderColor; // Need to make dynamic
              this.pushToDocSignerDetails(0, this.commonService.getTranslateData('LABELS.FOR_PATIENT'), false, '', '');
            } else if (
              !this.data.allowPersonalSignature &&
              !this.signatureTypeTagData.allowAssociateRoles &&
              !this.signatureTypeTagData.allowRecipientRoles
            ) {
              this.sendButtonText = this.saveButtonValue;
            }
            if (this.signatureTypeTagData.allowAssociateRoles && !isBlank(this.data.associate)) {
              this.sendButtonText = this.startSigningButtonValue;
              if (!this.data.allowPersonalSignature) {
                this.sendButtonText = this.saveButtonValue;
              }
              if (!this.signatureTypeTagData.obtainSignature) {
                this.sendButtonText = this.sendButtonValue;
              }
              this.borderColor = Signature.backgroundColor.associateBorderColor;
              this.pushToDocSignerDetails(this.data.associate.id, this.data.associate.displayName, false, '', '');
            }

            if (this.signatureTypeTagData.allowRecipientRoles && this.data.recipient && this.data.recipient.id) {
              this.sendButtonText = this.data.allowPersonalSignature ? this.startSigningButtonValue : this.sendButtonValue;
              let recipientName = this.data.recipient.displayName;
              let associateCareGiverPatientId = '';
              const getDisplayText = this.data.recipient.displayText || this.data.recipient.displayTextName;
              if (getDisplayText) {
                const name = getDisplayText.split('-');
                recipientName = name[0];
              }
              if (this.data.recipient.careGiver && this.data.recipient.careGiver.displayName) {
                recipientName = getDisplayText ? recipientName : `${this.data.recipient.careGiver.displayName} (${this.data.recipient.displayName})`;
                associateCareGiverPatientId = this.data.recipient.careGiver.id;
              } else if (this.data.recipient.isAlternate) {
                recipientName =
                  this.data.recipient.altPatientid === this.data.recipient.id
                    ? this.data.recipient.displayName
                    : recipientName.slice(-1) === ')'
                      ? recipientName
                      : `${recipientName})`;
                associateCareGiverPatientId = this.data.recipient.altPatientid;
              }

              this.borderColor = Signature.backgroundColor.otherBorderColor; // Need to make dynamic
              this.pushToDocSignerDetails(this.data.recipient.id, recipientName, false, associateCareGiverPatientId, '');
            }
            if (this.signatureTypeTagData.obtainSignature) {
              this.sendButtonText = this.data.allowPersonalSignature ? this.startSigningButtonValue : this.saveButtonValue;
            } else {
              this.sendButtonText = this.data.allowPersonalSignature ? this.startSigningButtonValue : this.sendButtonValue;
            }
            if (
              this.signatureTypeTagData.obtainSignature &&
              !this.signatureTypeTagData.allowAssociateRoles &&
              this.signatureTypeTagData.allowRecipientRoles
            ) {
              this.sendButtonText = !this.data.allowPersonalSignature ? this.saveButtonValue : this.startSigningButtonValue;
            } else if (
              this.signatureTypeTagData.obtainSignature &&
              this.signatureTypeTagData.allowAssociateRoles &&
              this.signatureTypeTagData.allowRecipientRoles
            ) {
              this.sendButtonText = !this.data.allowPersonalSignature ? this.saveButtonValue : this.startSigningButtonValue;
            }
            if (this.signatureTypeTagData.allowPendingApproveSignature) {
              this.borderColor = Signature.backgroundColor.meBorderColor;
              this.pushToDocSignerDetails(this.userData.userId, this.commonService.getTranslateData('LABELS.ME_APPROVER'), false, '', true);
            }
            this.documentSignersDetails[0].selected = true;
            this.selectedDocumentSignerDetails = {
              id: this.documentSignersDetails[0].id,
              name: this.documentSignersDetails[0].name,
              identifier: this.documentSignersDetails[0].identifier,
              selected: true,
              borderColor: this.documentSignersDetails[0].borderColor
            };
          }
          if (this.signatureTypeTagData.textBoxpaletteAllowed && this.signatureTypeTagData.signaturepaletteAllowed) {
            this.plusButton = true;
          } else if (this.signatureTypeTagData.textBoxpaletteAllowed || this.signatureTypeTagData.signaturepaletteAllowed) {
            this.plusButton = false;
          }
        }
      }, 1000);
      this.enableSaveSignWorkFlow(this.signatureDocumentDetails);
      this.buildImageUrl();
    } else {
      this.isUploadDocView = false;
      this.paletteButtons = false;
      this.sharedService.isLoading = true;
      if (isPresent(this.router.getCurrentNavigation()?.extras.state)) {
        this.documentRootInfo = this.router.getCurrentNavigation()?.extras.state.documentInfo;
      }
      this.route.paramMap.subscribe((paramMap) => {
        this.documentId = Number(paramMap.get('documentId'));
        const senderTenant = Number(paramMap.get('senderTenant'));
        this.graphqlService.getDocumentDetails(this.documentId, senderTenant)?.subscribe(
          ({ data }) => {
            this.documentInfo = data.signatureRequest[0];
            if (!isBlank(this.documentInfo) && !isBlank(this.documentInfo.prepopulationData)) {
              this.prepopulationData = JSON.parse(this.documentInfo.prepopulationData);
            }
            if (!isBlank(this.documentInfo)) {
              const applessverified = sessionStorage.getItem(Constants.storageKeys.applessDocTokenVerified);
              this.applessDocumentWorkflow = !this.sharedService.isAppLessHomeLoggedIn() && (
                (!isBlank(this.sharedService.applessDocumentId) && this.sharedService.applessDocumentId != undefined) ||
                  (applessverified != null && applessverified != '')
              );
              this.displayTypeConditions = this.documentInfo.type;
              this.confirmSaveSignToProfile = this.documentInfo?.confirmSaveSignToProfile;
              this.isCheckboxCross = this.displayTypeConditions?.checkboxImageBehavior === 2;
              this.dateWithSign = this.documentInfo.type.dateWithSign;
              this.documentDisplayText = this.documentInfo.displayText.text;
              this.ownerIdCopyFilingCenter = this.documentInfo.ownerId;
              this.caregiverAssociatePatient = this.documentInfo?.caregiverAssociatePatient;
              if (this.sharedService.getConfigValue(Config.documentExchangeMode) === Constants.documentExchangeFC) {
                if (!sharedService.isEnableConfig(Config.enableMultiSite)) {
                  this.folderNameCopyFilingCenter = this.displayTypeConditions.fromFilingCenter;
                }
                this.fetchSiteIdForFolder();
              }
              this.filenameFormatCopyFilingCenter = this.displayTypeConditions.fileNameFormatText;
              this.documentOwnerId = this.documentInfo.ownerId;
              this.totalDocsCount = this.documentInfo.document.pageCount;
              this.downloadUrl = this.documentInfo.downloadUrl;
              this.ownerId = this.documentInfo.ownerId;
              if (this.documentInfo.signatureStatus === Signature.signatureStatus.signatureSignedStatus) {
                this.isSigned = true;
                if (this.applessDocumentWorkflow) {
                  this.applessDocumentWorkFlowComplete = true;
                  this.applessDocumentWorkFlowCompletedMessage = this.commonService.getTranslateData(
                    'SUCCESS_MESSAGES.APPLESS_DOCUMENT_ALREADY_SUBMIT'
                  );
                }
              }
              this.snapAndSignApproval = this.documentInfo.signatureStatus === Signature.signatureStatus.snapSignApproval;
              if (this.sharedService.platformValue === Constants.platform.web) {
                this.platform = true;
              }
              this.signatureCrossTenant = this.documentInfo.senderTenant && this.documentInfo.senderTenant != 0 ? this.documentInfo.senderTenant : '';
              this.signaturesByUsers = this.documentInfo.signatureByUsers;
              this.signaturesByUsersId = !isBlank(this.signaturesByUsers) ? `[${this.signaturesByUsers.userId}]` : '';
              this.associateSignatureByRoles = this.documentInfo.associateSignatureByUsers;
              this.associateSignaturesByUsersId = !isBlank(this.associateSignatureByRoles.userId) ? `[${this.associateSignatureByRoles.userId}]` : '';
              if (
                !this.documentInfo.sendDocumentWithoutSignature &&
                this.documentInfo.document.signaturePalette.length
              ) {
                if (
                  !this.isSigned &&
                  this.signaturesByUsers &&
                  Number(this.userData.userId) === Number(this.signaturesByUsers.userId)
                ) {
                  this.mySignatureNeeded = true;
                }
                let droppedObjectArray = this.documentInfo.document.signaturePalette;
                droppedObjectArray.forEach((element) => {
                  if (
                    (((typeof element.signature === 'object' && element.signature.signatureId == 0) ||
                      element.signature == '' ||
                      !element.signature) &&
                      element.fieldForUser == this.userData.userId) ||
                    element.fieldForUser != this.userData.userId
                  ) {
                    this.dummyDroppedObjects.push(element);
                  }
                });
                this.dummyDroppedObjects = this.dummyDroppedObjects.sort((a, b) => {
                  a['textPlaceholder'] = a['textPlaceholder'].includes('undefined')
                    ? Signature.paletteType.textValue
                    : a['textPlaceholder'] || '';
                  b['textPlaceholder'] = b['textPlaceholder'].includes('undefined')
                    ? Signature.paletteType.textValue
                    : b['textPlaceholder'] || '';

                  return (
                    a.signatureDocumentPageNumber - b.signatureDocumentPageNumber ||
                    a.fieldorder - b.fieldorder ||
                    a.verticalSpaceTop - b.verticalSpaceTop
                  );
                });
              }
              this.buildImageUrl();
              this.sharedService.consoloLoader = false;
              const openDocumentDes = {
                displayName: this.userData.displayName,
                displayText: this.documentInfo.displayText.text,
                signatureStatus: this.documentInfo.signatureStatus
              };
              this.sharedService.trackActivity({
                type: Activity.signatureRequest,
                name: Activity.openDocument,
                des: {
                  data: openDocumentDes,
                  desConstant: Activity.openDocumentDes
                }
              });
              const viewDocumentDes = {
                displayName: this.userData.displayName,
                documentName: this.documentInfo.document.displayText,
                signatureStatus: this.documentInfo.signatureStatus,
                type: this.documentInfo.type.name
              };
              this.sharedService.trackActivity({
                type: Activity.signatureRequest,
                name: Activity.viewDocument,
                des: {
                  data: viewDocumentDes,
                  desConstant: Activity.viewDocumentDes
                },
                linkageId: this.userData.displayName
              });
            } else {
              this.sharedService.isLoading = false;
            }
            this.enableSaveSignWorkFlow(this.documentInfo);
          },
          (error) => {
            this.sharedService.errorHandler(error);
          }
        );
      });
    }
    if (this.isMobilePlatform) {
      Keyboard.addListener('keyboardDidShow', () => {
        const activeDocElement = document.getElementById(document.activeElement.id) as HTMLElement;
        setTimeout(() => {
          activeDocElement.blur();
          activeDocElement.focus();
        }, 100);
      });
    }
  }

  buildImageUrl(): void {
    this.sharedService.isLoading = true;
    const ownerId = !isBlank(this.signatureDocumentDetails) ? this.signatureDocumentDetails.ownerId : this.documentInfo.ownerId;
    const displayText = !isBlank(this.signatureDocumentDetails)
      ? this.signatureDocumentDetails.document.displayText
      : this.documentInfo.document.displayText;
    const imageUrlPrefix = `${environment.apiServer}${Urls.writableFilePath}`;
    const countAfterPad = this.currentDocPage.toString().padStart(3, '0');
    this.currentImagePath = `${imageUrlPrefix}/${ownerId}${Urls.documentSignaturePath}`;
    this.currentImagePath += `${countAfterPad}_${displayText}.png`;
  }

  pushToDocSignerDetails(
    userId: number,
    name: string,
    selected: boolean,
    associateCareGiverPatientId: any,
    pendingApproveUser: any
  ): void {
    this.documentSignersDetails.push({
      id: userId,
      name,
      identifier: `${userId}:${name}`,
      borderColor: this.borderColor,
      selected,
      associateCareGiverPatientId: !isBlank(associateCareGiverPatientId) ? associateCareGiverPatientId : '',
      pendingApproveUser: !isBlank(pendingApproveUser) ? pendingApproveUser : ''
    });
  }

  next(pageNumber = 0): void {
    if (pageNumber != 0) {
      this.currentDocPage = pageNumber;
    } else {
      this.currentDocPage++;
    }
    this.nextPage = true;
    this.buildImageUrl();
  }

  previous(): void {
    this.currentDocPage--;
    this.buildImageUrl();
  }

  back(): void {
    if (this.sharedService.isAppLessHomeLoggedIn()) {
      this.router.navigate([`.${PageRoutes.appLess}/home`]);
    } else if (isPresent(this.dummyDroppedObjects)) {
      const checkPaletteSigned = this.isSignedPalette(this.dummyDroppedObjects);
      if (checkPaletteSigned) {
        this.showConfirmBoxOnBack();
      } else {
        this.navigateToBack();
      }
    } else {
      this.navigateToBack();
    }
  }

  showConfirmBoxOnBack():void {
    const buttons = [
      { text: this.commonService.getTranslateData('BUTTONS.CANCEL'), confirm: false },
      { text: this.commonService.getTranslateData('BUTTONS.OK'), confirm: true }
    ];
    this.commonService
      .showAlert({
        message: ConfigValues.messages.backToinboxFromSignatureMessage,
        header: 'TITLES.SIGNATURE_REQUEST',
        buttons,
        cssClass: 'common-alert document-auto-sign'
      })
      .then((confirmation) => {
        if (confirmation) {
          this.navigateToBack();
        }
      });
  }

  isSignedPalette(droppedArray) {
    return droppedArray.some((result) => {
      return (
        (result.paletteType === Signature.paletteType.signValue || result.paletteType === Signature.paletteType.textValue) &&
        typeof result.signature === 'string' &&
        result.signature.trim() !== ''
      );
    });
  }

  checkEveryFieldAsGroupBox(droppedArray): boolean {
    return droppedArray.every((result) => {
      return result.paletteType === Signature.paletteType.checkboxValue && result.group;
    });
  }

  navigateToBack(): void {
    if (this.sharedService.automaticLinkedItems) {
      this.commonService.redirectToPage('/document-center/select-document');
    } else {
      this.commonService.navCtrl.navigateBack([`/document-center/${location.href.split('/').pop()}`]);
    }
  }

  ngOnInit(): void {
    this.enableSaveSignatureToProfile = this.sharedService.isEnableConfig(Config.enableSaveSignatureToProfile);
    this.sharedService.trackActivity({ type: Activity.pageAccess });
    this.lockDeviceOrientation();
  }

  lockDeviceOrientation(): void {
    if (this.isMobilePlatform && !this.sharedService.platform.is('ipad') && !this.sharedService.platform.is('tablet')) {
      this.sharedService.lockDevice(Constants.deviceOrientation.portrait);
    } else if (
      (this.isMobilePlatform && this.sharedService.platform.is('ipad')) ||
      (this.isMobilePlatform && this.sharedService.platform.is('tablet'))
    ) {
      const isLandScapeMode = window.orientation === -90 || window.orientation === 90;
      if (!isLandScapeMode) {
        this.sharedService.lockDevice(Constants.deviceOrientation.portrait);
      }
      this.sharedService.unlockDevice();
    }
  }

  downloadDoc(): void {
    this.sharedService.trackActivity({
      type: Activity.signatureRequest,
      name: Activity.downloadDoc,
      des: {
        data: {
          displayName: this.userData.displayName,
          documentName: this.documentInfo.displayText.text,
          id: this.documentInfo.id
        },
        desConstant: Activity.downloadDocDes
      }
    });
  }
  imageLoaded(event: any): void {
    this.target = event;
    this.changeOrientationMode();
    if (this.isUploadDocView) {
      this.showSignDoc = true;
      setTimeout(() => {
        this.bindPaletteDetailsFromTag();
      }, 1000);
      this.setTopAndLeftDynamic();
      if (!isBlank(this.sharedService.automaticLinkedItems) && this.addObjects.length === 0) {
        this.sharedService.consoloLoader = false;
      }
      const documentUploadDes = {
        displayName: this.userData?.displayName,
        actualFileName: this.selectedFileDetails?.documentDisplayText,
        status: this.signatureDocumentDetails?.signatureStatus,
        sendDocumentWithoutSignature: this.signatureTypeTagData?.sendDocumentWithoutSignature,
        ObtainSignature: this.signatureTypeTagData?.obtainSignature,
        personalSignature: this.data?.allowPersonalSignature
      };
      this.sharedService.trackActivity({
        type: `${Activity.signatureRequest}-${this.signatureDocumentDetails?.signatureStatus}`,
        name: Activity.documentUpload,
        des: {
          data: documentUploadDes,
          desConstant: Activity.documentUploadDes
        },
        linkageId: this.userData?.displayName
      });
      this.sharedService.isLoading = false;
    } else {
      if (isPresent(this.dummyDroppedObjects) && !this.nextPage) {
        const screenWidth = window.innerWidth > 0 ? window.innerWidth : screen.width;
        this.dummyDroppedObjects.forEach((element, index) => {
          const checkSignatureType = this.signatureTypeTagData
            ? parseInt(this.signatureTypeTagData?.checkBoxGroupAreMandatory)
            : this.displayTypeConditions?.checkBoxGroupAreMandatory
              ? this.displayTypeConditions.checkBoxGroupAreMandatory
              : 0;

          const setBorderColorAndBoxShadow =
            element.mandatory ||
            (element.paletteType === Signature.paletteType.checkboxValue &&
              element.group &&
              checkSignatureType !== Signature.checkboxGroupValidationBehavior.optional);
          element.boxShadow = setBorderColorAndBoxShadow
            ? Signature.backgroundColor.boxShadow
            : Signature.backgroundColor.optionalBoxShadow;
          element.border = setBorderColorAndBoxShadow
            ? Signature.backgroundColor.toBeSignedBorder
            : Signature.backgroundColor.optionalToBeSignedBorder;
          element.mySignNeed = false;
          if (element.paletteType === Signature.paletteType.checkboxValue) {
            element.mandatory = setBorderColorAndBoxShadow ? true : element.mandatory;
            if (element.checkboxLabel) {
              const regex = Signature.checkboxPaletteRegex;
              element.checkBoxPaletteLabel = element.checkBoxPaletteLabel.replace(regex, '\n');
            } else {
              element.checkBoxPaletteLabel = '';
            }
          }

          if (element.fieldorder === 0) {
            element.fieldorder = new Date().getHours() + '' + new Date().getMinutes() + index;
          }
          if (!this.isSigned && element.signature && !element.signature.signatureImage) {
            element.signature = false;
          }
          let isDisableForPatient = false;
          // Pre-population text palette
          // TODO: Use prepopulateData function
          if (
            element.paletteType === Signature.paletteType.textValue &&
            this.prepopulationData
          ) {
            if (element.textFieldActAs === Signature.prepopulationFields.mrn) {
              element.nickName = element.textPlaceholder;
              let prepopulationDataMrn = this.prepopulationData.patientMrn;
              if (
                this.displayTypeConditions.patientInformationExchange === Signature.prepopulationFields.guid &&
                !isBlank(this.sharedService.automaticLinkedItems?.document)
              ) {
                prepopulationDataMrn = this.prepopulationData.patientGuid;
              }
              element.signature = prepopulationDataMrn;
            }

            if (
              this.prepopulationData.patient &&
              (this.prepopulationData.patient.id !== '' || this.prepopulationData.patient.id != 0)
            ) {
              element.nickName = element.textPlaceholder;
              if (
                element.textFieldActAs === Signature.prepopulationFields.firstName ||
                element.textFieldActAs === Signature.prepopulationFields.lastName
              ) {
                element.signature =
                  element.textFieldActAs === Signature.prepopulationFields.firstName
                    ? this.prepopulationData.patient.firstName
                    : this.prepopulationData.patient.lastName;
              }
              if (
                element.textFieldActAs === Signature.prepopulationFields.firstName ||
                element.textFieldActAs === Signature.prepopulationFields.lastName
              ) {
                element.signature =
                  element.textFieldActAs === Signature.prepopulationFields.firstName
                    ? this.prepopulationData.patient.firstName
                    : this.prepopulationData.patient.lastName;
              }
              if (element.textFieldActAs === Signature.prepopulationFields.flname) {
                element.signature = [
                  this.prepopulationData.patient.firstName,
                  this.prepopulationData.patient.lastName
                ].join(' ');
              } else if (element.textFieldActAs === Signature.prepopulationFields.lfname) {
                element.signature = [
                  this.prepopulationData.patient.lastName,
                  this.prepopulationData.patient.firstName
                ].join(' ');
              }
              if (
                element.textFieldActAs === Signature.prepopulationFields.dob &&
                !isBlank(this.prepopulationData.patient?.dateOfBirth)
              ) {
                element.signature = this.prepopulationData.patient.dateOfBirth;
              } else if (
                element.textFieldActAs === Signature.prepopulationFields.dob &&
                isPresent(this.prepopulationData.patient?.dob)
              ) {
                element.signature = this.prepopulationData.patient.dob;
              }
              if (
                element.textFieldActAs === Signature.prepopulationFields.mobile &&
                !isBlank(this.prepopulationData.patient?.mobile)
              ) {
                element.signature = this.prepopulationData.patient.mobile;
              }
              if (
                element.textFieldActAs === Signature.prepopulationFields.email &&
                !isBlank(this.prepopulationData.patient?.email)
              ) {
                element.signature = this.prepopulationData.patient?.email;
              }
              if (
                element.textFieldActAs === Signature.prepopulationFields.gender &&
                !isBlank(this.prepopulationData.patient?.gender)
              ) {
                element.signature = this.prepopulationData.patient.gender;
              }
            }

            if (element.textFieldActAs !== Signature.paletteType.textValue) {
              this.textValue[element.id] = element.signature && !isBlank(element.signature) ? element.signature : '';
            }
            isDisableForPatient = !this.displayTypeConditions.editablePrePopulatedFields && !isBlank(element.signature) && element.signature && +this.userData.group === UserGroup.PATIENT || false;
            element.isDisableForPatient = isDisableForPatient;
          }
          //End of Pre-population text palette
          if (
            this.displayTypeConditions?.obtainSignature &&
            !this.displayTypeConditions?.allowAssociateRoles &&
            !this.displayTypeConditions?.allowRecipientRoles
          ) {
            element.mySignNeed = true;
          } else if (
            !this.displayTypeConditions?.allowAssociateRoles &&
            this.displayTypeConditions?.allowRecipientRoles
          ) {
            if (parseInt(element.fieldForUser, 10) === parseInt(this.userData.userId, 10)) {
              element.mySignNeed = true;
            } else if (
              this.displayTypeConditions.obtainSignature &&
              this.documentOwnerId === this.userData.userId &&
              element.fieldForUser === 0
            ) {
              element.mySignNeed = true;
            }
          } else if (
            this.displayTypeConditions?.allowAssociateRoles &&
            !this.displayTypeConditions?.allowRecipientRoles
          ) {
            if (
              this.documentOwnerId === this.userData.userId &&
              element.associateUser &&
              this.displayTypeConditions.obtainSignature
            ) {
              element.mySignNeed = true;
            } else if (parseInt(element.fieldForUser, 10) === parseInt(this.userData.userId, 10)) {
              element.mySignNeed = true;
            }
          } else if (
            this.displayTypeConditions?.allowAssociateRoles &&
            this.displayTypeConditions?.allowRecipientRoles
          ) {
            if (
              this.documentOwnerId === this.userData.userId &&
              element.associateUser &&
              this.displayTypeConditions.obtainSignature
            ) {
              element.mySignNeed = true;
            } else if (parseInt(element.fieldForUser, 10) === parseInt(this.userData.userId, 10)) {
              element.mySignNeed = true;
            }
          }
          if (
            element.paletteType === Signature.paletteType.checkboxValue &&
            element.group &&
            element.mySignNeed &&
            !element.signaturePaletteLock
          ) {
            element.groupName = element.groupName.replace(/\s/g, '');
            const groupData = {
              groupName: element.groupName,
              color: element.groupColor,
              checked: false,
              approver: element.pendingApproveUser
            };

            this.pushGroupObjectToArray(groupData);
          }

          if (
            this.documentInfo?.signatureStatus === Signature.signatureStatus.signatureApprovalStatus &&
            element.pendingApproveUser &&
            parseInt(element.fieldForUser, 10) !== parseInt(this.userData.userId, 10)
          ) {
            this.approvePending = true;
            this.mySignatureNeeded = false;
          }
          if (
            !this.mySignatureNeeded &&
            this.documentInfo?.signatureStatus === Signature.signatureStatus.signatureApprovalStatus &&
            element.pendingApproveUser &&
            parseInt(element.fieldForUser, 10) === parseInt(this.userData.userId, 10)
          ) {
            this.mySignatureNeeded = true;
          }
          element.orginalHorizontalSpaceLeft = element.horizontalSpaceLeft;
          element.rotationLeftOriginalSpace = element.horizontalSpaceLeft;
          element.orginalVerticalSpaceTop = element.verticalSpaceTop;
          element.orginalSignaturePaletteWidth = element.signaturePaletteWidth;
          element.orginalSignaturePaletteHeight = element.signaturePaletteHeight;
          const orginalFileInfo = this.sharedService.getDocumentWidthAndHeight(
            element.orginalHorizontalSpaceLeft,
            element.orginalVerticalSpaceTop,
            element.orginalSignaturePaletteWidth,
            element.orginalSignaturePaletteHeight,
            true,
            '',
            this.target,
            this.signDocument
          );

          if (element.paletteType === Signature.paletteType.checkboxValue) {
            //firoz
            if (
              element.group &&
              checkSignatureType === Signature.checkboxGroupValidationBehavior.exactlyOneRequiredValue
            ) {
              element.mandatory = true;
              element.boxShadow = Signature.backgroundColor.boxShadow;
              element.border = Signature.backgroundColor.toBeSignedBorder;
            }
            if (element.checkboxLabel) {
              if (orginalFileInfo.width > ConfigValues.messages.configurations.signatureDocumentPalette.signwidth) {
                orginalFileInfo.width = ConfigValues.messages.configurations.signatureDocumentPalette.signwidth;
              }

              if (orginalFileInfo.height > ConfigValues.messages.configurations.signatureDocumentPalette.signheight) {
                orginalFileInfo.height = ConfigValues.messages.configurations.signatureDocumentPalette.signheight;
              }
            }
          }

          let paletteResizeHeight, paletteResizeWidth;
          element.horizontalSpaceLeft = `${orginalFileInfo.left}${Constants.pixel}`;
          element.verticalSpaceTop = `${orginalFileInfo.top}${Constants.pixel}`;
          element.signaturePaletteWidth = `${orginalFileInfo.width}${Constants.pixel}`;
          element.signaturePaletteHeight = `${parseInt(orginalFileInfo.height) < ConfigValues.messages.configurations.signatureDocumentPalette.minHeight
            ? ConfigValues.messages.configurations.signatureDocumentPalette.minHeight
            : orginalFileInfo.height
            }${Constants.pixel}`;
          element.fontSize = this.sharedService.getFontSize(
            parseInt(orginalFileInfo.height) < ConfigValues.messages.configurations.signatureDocumentPalette.minHeight
              ? ConfigValues.messages.configurations.signatureDocumentPalette.minHeight
              : parseInt(orginalFileInfo.height), this.target?.offsetWidth
          );

          element.view = false;

          if (screenWidth <= 480) {
            if (!element.checkboxLabel) {
              paletteResizeHeight = Number(Signature.paletteSize.responsiveCheckBox);
              paletteResizeWidth = Number(Signature.paletteSize.responsiveCheckBox);
            } else {
              paletteResizeHeight =
                parseInt(orginalFileInfo.height) > Number(Signature.paletteSize.maxHeight)
                  ? Number(Signature.paletteSize.maxHeight)
                  : parseInt(orginalFileInfo.height) <
                    ConfigValues.messages.configurations.signatureDocumentPalette.minHeight
                    ? ConfigValues.messages.configurations.signatureDocumentPalette.minHeight
                    : orginalFileInfo.height;

              paletteResizeWidth =
                orginalFileInfo.width > Number(Signature.paletteSize.maxWidth)
                  ? Number(Signature.paletteSize.maxWidth)
                  : orginalFileInfo.width;
            }
          } else {
            if (!element.checkboxLabel) {
              paletteResizeHeight = Number(Signature.paletteSize.checkboxLabelHeight);
              paletteResizeWidth = Number(Signature.paletteSize.checkboxLabelHeight);
            } else {
              paletteResizeHeight =
                parseInt(orginalFileInfo.height) > Number(Signature.paletteSize.maxHeight)
                  ? Number(Signature.paletteSize.maxHeight)
                  : parseInt(orginalFileInfo.height) <
                    ConfigValues.messages.configurations.signatureDocumentPalette.minHeight
                    ? ConfigValues.messages.configurations.signatureDocumentPalette.minHeight
                    : orginalFileInfo.height;

              paletteResizeWidth =
                orginalFileInfo.width < ConfigValues.messages.configurations.signatureDocumentPalette.minWidth
                  ? ConfigValues.messages.configurations.signatureDocumentPalette.minWidth
                  : orginalFileInfo.width > Number(Signature.paletteSize.maxWidth)
                    ? Number(Signature.paletteSize.maxWidth)
                    : orginalFileInfo.width;
            }
          }

          element.signaturePaletteWidth = paletteResizeWidth + Constants.pixel;
          element.signaturePaletteHeight = paletteResizeHeight + Constants.pixel;

          if (
            this.documentInfo?.signatureStatus === Signature.signatureStatus.signaturePendingStatus &&
            element.pendingApproveUser &&
            parseInt(element.fieldForUser, 10) === parseInt(this.userData.userId, 10)
          ) {
            element.mySignNeed = false;
            if (this.displayTypeConditions.obtainSignature) {
              element.signaturePaletteLock = Signature.paletteLock;
            }
          } else if (
            this.documentInfo?.signatureStatus === Signature.signatureStatus.signaturePendingStatus &&
            element.pendingApproveUser &&
            parseInt(element.fieldForUser, 10) !== parseInt(this.userData.userId, 10)
          ) {
            element.signaturePaletteLock = Signature.paletteLock;
          }

          if (isObject(element.signature)) {
            element.signature.signatureImage = `${environment.apiServer}${Urls.writableFilePath}${element.signature.signatureImage}`;
          }

          if (index === this.dummyDroppedObjects.length - 1) {
            this.sharedService.isLoading = false;
          }

        });
        if (
          !this.isSigned &&
          (!this.displayTypeConditions?.allowRecipientRoles ||
            this.displayTypeConditions?.allowAssociateRoles ||
            (this.displayTypeConditions?.allowAssociateRoles && this.documentOwnerId === this.userData?.userId) ||
            this.associateSignatureByRoles?.userId === this.userData?.userId ||
            this.mySignatureNeeded)
        ) {
          if (
            !this.displayTypeConditions?.obtainSignature &&
            this.displayTypeConditions?.allowAssociateRoles &&
            parseInt(this.associateSignatureByRoles?.userId, 10) !== parseInt(this.userData?.userId, 10) &&
            this.documentInfo?.associateSignatureProcess
          ) {
            // do nothing
          } else {
            if (
              (this.documentInfo?.ownerId === this.userData?.userId && !this.displayTypeConditions?.obtainSignature) ||
              (this.documentInfo?.associateSignatureProcess &&
                this.displayTypeConditions?.allowAssociateRoles &&
                this.associateSignatureByRoles.userId === this.userData.userId &&
                this.documentInfo.signatureStatus !== Signature.signatureStatus.signaturePendingStatus)
            ) {
              if (this.documentInfo?.signatureStatus === Signature.signatureStatus.signatureApprovalStatus) {
                this.startSigningProcess();
              } else {
                this.sharedService.isLoading = false;
              }
            } else {
              this.associatePhysicaianSignature = true;
              this.startSigningProcess();
            }
          }
        } else {
          this.sharedService.isLoading = false;
        }
      } else {
        this.sharedService.isLoading = false;

        this.setTopAndLeftDynamic();
        if (this.nextPage) {
          this.findNext(false);
        }
      }
    }
  }

  changeOrientationMode(): void {
    window.addEventListener(
      'orientationchange',
      () => {
        // const isLandScapeMode = window.orientation === -90 || window.orientation === 90;
        // Create New Doc
        setTimeout(() => {
          if (this.showSignDoc) {
            const signatureImageInfo = this.signatureTypeTagData?.signatureImageInfo;
            for (let index = 0; index < this.addObjects.length; index++) {
              const element = this.addObjects[index].myLocation;
              let top = element.verticalSpaceTop;
              let left = element.horizontalSpaceLeft;
              let width = element.signaturePaletteWidth;
              let height = element.signaturePaletteHeight;

              let palletName = this.addObjects[index].name;
              let isCheckBoxWithLabel = this.addObjects[index].checkboxLabel || false;
              const orginalFileSizeInfo = this.sharedService.getDocumentWidthAndHeight(
                left,
                top,
                width,
                height,
                true,
                signatureImageInfo,
                this.target,
                this.signDocument
              );
              if (
                palletName.toLowerCase() === Signature.paletteType.textField ||
                (palletName.toLowerCase() === Signature.paletteType.checkboxField && isCheckBoxWithLabel)
              ) {
                let paletteResizeWidth = 0,
                  paletteResizeHeight = 0;
                if (
                  parseInt(orginalFileSizeInfo.width) <
                  ConfigValues.messages.configurations.signatureDocumentPalette.minWidth
                ) {
                  paletteResizeWidth = ConfigValues.messages.configurations.signatureDocumentPalette.minWidth;
                } else if (
                  orginalFileSizeInfo.width >
                  ConfigValues.messages.configurations.signatureDocumentPalette.maxWidthForText
                ) {
                  paletteResizeWidth = ConfigValues.messages.configurations.signatureDocumentPalette.maxWidthForText;
                } else {
                  paletteResizeWidth = orginalFileSizeInfo.width;
                }

                if (
                  orginalFileSizeInfo.height < ConfigValues.messages.configurations.signatureDocumentPalette.minHeight
                ) {
                  paletteResizeHeight = ConfigValues.messages.configurations.signatureDocumentPalette.minHeight;
                } else if (
                  orginalFileSizeInfo.height >
                  ConfigValues.messages.configurations.signatureDocumentPalette.maxHeightForText
                ) {
                  paletteResizeHeight = ConfigValues.messages.configurations.signatureDocumentPalette.maxHeightForText;
                } else {
                  paletteResizeHeight = orginalFileSizeInfo.height;
                }

                this.addObjects[index].myLocation.height = paletteResizeHeight + Constants.pixel;
                this.addObjects[index].myLocation.width = paletteResizeWidth + Constants.pixel;
                this.addObjects[index].myLocation.fontSize = this.sharedService.getFontSize(paletteResizeHeight, this.target?.offsetWidth);
              }

              if (palletName === Signature.paletteType.signValue) {
                let paletteResizeWidth = 0,
                  paletteResizeHeight = 0;
                if (
                  parseInt(orginalFileSizeInfo.width) <
                  ConfigValues.messages.configurations.signatureDocumentPalette.minWidth
                ) {
                  paletteResizeWidth = ConfigValues.messages.configurations.signatureDocumentPalette.minWidth;
                } else if (
                  orginalFileSizeInfo.width > ConfigValues.messages.configurations.signatureDocumentPalette.signwidth
                ) {
                  paletteResizeWidth = ConfigValues.messages.configurations.signatureDocumentPalette.signwidth;
                } else {
                  paletteResizeWidth = orginalFileSizeInfo.width;
                }

                if (
                  orginalFileSizeInfo.height < ConfigValues.messages.configurations.signatureDocumentPalette.minHeight
                ) {
                  paletteResizeHeight = ConfigValues.messages.configurations.signatureDocumentPalette.minHeight;
                } else if (
                  orginalFileSizeInfo.height > ConfigValues.messages.configurations.signatureDocumentPalette.signheight
                ) {
                  paletteResizeHeight = ConfigValues.messages.configurations.signatureDocumentPalette.signheight;
                } else {
                  paletteResizeHeight = orginalFileSizeInfo.height;
                }

                this.addObjects[index].myLocation.height = paletteResizeHeight + Constants.pixel;
                this.addObjects[index].myLocation.width = paletteResizeWidth + Constants.pixel;
              }

              this.addObjects[index].myLocation.fixTop = `${parseInt(orginalFileSizeInfo.top)}${Constants.pixel}`;
              this.addObjects[index].myLocation.fixLeft = `${parseInt(orginalFileSizeInfo.left)}${Constants.pixel}`;
              this.addObjects[index].myLocation.top = `${parseInt(orginalFileSizeInfo.top)}${Constants.pixel}`;
              this.addObjects[index].myLocation.left = `${parseInt(orginalFileSizeInfo.left)}${Constants.pixel}`;

              if (index === this.addObjects.length - 1) {
                this.cdRef.detectChanges();
              }
            }
          }
          // Pending Folder
          if (!this.isSigned) {
            for (let index = 0; index < this.dummyDroppedObjects.length; index++) {
              const element = this.dummyDroppedObjects[index];
              let palletName = element.paletteType;
              let isCheckBoxWithLabel = element.checkboxLabel || false;

              const orginalFileInfo = this.sharedService.getDocumentWidthAndHeight(
                element.rotationLeftOriginalSpace,
                element.orginalVerticalSpaceTop,
                element.orginalSignaturePaletteWidth,
                element.orginalSignaturePaletteHeight,
                true,
                '',
                this.target,
                this.signDocument
              );

              if (
                palletName.toLowerCase() === Signature.paletteType.textField ||
                (palletName.toLowerCase() === Signature.paletteType.checkboxField && isCheckBoxWithLabel)
              ) {
                let paletteResizeWidth = 0,
                  paletteResizeHeight = 0;
                if (
                  parseInt(orginalFileInfo.width) <
                  ConfigValues.messages.configurations.signatureDocumentPalette.minWidth
                ) {
                  paletteResizeWidth = ConfigValues.messages.configurations.signatureDocumentPalette.minWidth;
                } else if (
                  orginalFileInfo.width > ConfigValues.messages.configurations.signatureDocumentPalette.maxWidthForText
                ) {
                  paletteResizeWidth = ConfigValues.messages.configurations.signatureDocumentPalette.maxWidthForText;
                } else {
                  paletteResizeWidth = orginalFileInfo.width;
                }

                if (
                  parseInt(orginalFileInfo.height) <
                  ConfigValues.messages.configurations.signatureDocumentPalette.minHeight
                ) {
                  paletteResizeHeight = ConfigValues.messages.configurations.signatureDocumentPalette.minHeight;
                } else if (
                  parseInt(orginalFileInfo.height) >
                  ConfigValues.messages.configurations.signatureDocumentPalette.maxHeightForText
                ) {
                  paletteResizeHeight = ConfigValues.messages.configurations.signatureDocumentPalette.maxHeightForText;
                } else {
                  paletteResizeHeight = orginalFileInfo.height;
                }

                this.dummyDroppedObjects[index].signaturePaletteHeight = paletteResizeHeight + Constants.pixel;
                this.dummyDroppedObjects[index].signaturePaletteWidth = paletteResizeWidth + Constants.pixel;
                this.dummyDroppedObjects[index].fontSize = this.sharedService.getFontSize(paletteResizeHeight, this.target?.offsetWidth);
              }

              if (palletName === Signature.paletteType.signValue) {
                let paletteResizeWidth = 0,
                  paletteResizeHeight = 0;
                if (
                  parseInt(orginalFileInfo.width) <
                  ConfigValues.messages.configurations.signatureDocumentPalette.minWidth
                ) {
                  paletteResizeWidth = ConfigValues.messages.configurations.signatureDocumentPalette.minWidth;
                } else if (
                  orginalFileInfo.width > ConfigValues.messages.configurations.signatureDocumentPalette.signwidth
                ) {
                  paletteResizeWidth = ConfigValues.messages.configurations.signatureDocumentPalette.signwidth;
                } else {
                  paletteResizeWidth = orginalFileInfo.width;
                }

                if (
                  parseInt(orginalFileInfo.height) <
                  ConfigValues.messages.configurations.signatureDocumentPalette.minHeight
                ) {
                  paletteResizeHeight = ConfigValues.messages.configurations.signatureDocumentPalette.minHeight;
                } else if (
                  parseInt(orginalFileInfo.height) >
                  ConfigValues.messages.configurations.signatureDocumentPalette.signheight
                ) {
                  paletteResizeHeight = ConfigValues.messages.configurations.signatureDocumentPalette.signheight;
                } else {
                  paletteResizeHeight = orginalFileInfo.height;
                }

                this.dummyDroppedObjects[index].signaturePaletteHeight = paletteResizeHeight + Constants.pixel;
                this.dummyDroppedObjects[index].signaturePaletteWidth = paletteResizeWidth + Constants.pixel;
              }

              this.dummyDroppedObjects[index].horizontalSpaceLeft = `${orginalFileInfo.left}${Constants.pixel}`;
              this.dummyDroppedObjects[index].verticalSpaceTop = `${orginalFileInfo.top}${Constants.pixel}`;

              if (index === this.dummyDroppedObjects.length - 1) {
                this.cdRef.detectChanges();
              }
            }
          }
        }, 100);
      },
      false
    );
  }
  // Click on next and previous button with multiple document fixed document overlap issue
  setTopAndLeftDynamic(): void {
    if (this.addObjects) {
      this.addObjects.forEach((element) => {
        if (Number(this.currentDocPage) === Number(element.page)) {
          element.myLocation.fixLeft = element.myLocation.left;
          element.myLocation.fixTop = element.myLocation.top;

          this.resetDivTransform(element.id);
        }
      });
    }
  }
  bindPaletteDetailsFromTag(): void {
    const signaturePaletteDetails = this.signatureTypeTagData?.signaturePalette;
    const signatureImageInfo = this.signatureTypeTagData?.signatureImageInfo;
    if (signaturePaletteDetails && signaturePaletteDetails.length && signaturePaletteDetails[0].paletteType !== '') {
      const recipientUserId = this.data?.recipient?.id;
      const associateUserId = this.data?.associate?.id;
      let i = 0;
      signaturePaletteDetails.forEach((element) => {
        i++;
        const singlePaletteIndex = signaturePaletteDetails.indexOf(element);
        const singleSignaturePaletteDetails = element;
        this.borderColor = Signature.backgroundColor.otherBorderDottedColor; // TODO: Need to make dynmaic
        let buttonColor = Signature.backgroundColor.otherBorderColor; // TODO: Need to make dynmaic
        if (
          singleSignaturePaletteDetails?.signatureDocumentPageNumber <=
          this.signatureDocumentDetails?.document?.pageCount &&
          ((singleSignaturePaletteDetails?.fieldForUser === Signature.selfValue &&
            this.signatureTypeTagData?.allowPersonalSignature &&
            this.data?.allowPersonalSignature) ||
            singleSignaturePaletteDetails?.fieldForUser === Constants.otherValue ||
            singleSignaturePaletteDetails?.fieldForUser === Constants.patientValue ||
            (singleSignaturePaletteDetails?.fieldForUser === Signature.approverValue &&
              this.signatureTypeTagData?.allowPendingApproveSignature))
        ) {
          let userId = this.userData?.userId;
          if (singleSignaturePaletteDetails.fieldForUser === Constants.otherValue) {
            if (!singleSignaturePaletteDetails.associate && recipientUserId !== 0) {
              userId = recipientUserId;
            } else if (singleSignaturePaletteDetails.associate && associateUserId !== 0) {
              userId = associateUserId;
            }
          } else if (singleSignaturePaletteDetails.fieldForUser === Constants.patientValue) {
            userId = 0;
          }
          if (userId === this.userData.userId) {
            this.borderColor = Signature.backgroundColor.meBorderDottedColor;
            buttonColor = Signature.backgroundColor.meBorderColor;
          } else if (
            (this.signatureTypeTagData?.allowAssociateRoles && userId === this.data.associate.id) ||
            (this.signatureTypeTagData?.obtainSignature &&
              !this.signatureTypeTagData?.allowAssociateRoles &&
              this.signatureTypeTagData?.allowRecipientRoles &&
              userId === 0) ||
            (this.signatureTypeTagData?.obtainSignature &&
              !this.signatureTypeTagData?.allowAssociateRoles &&
              !this.signatureTypeTagData?.allowRecipientRoles &&
              userId === 0)
          ) {
            this.borderColor = Signature.backgroundColor.associateBorderDottedColor;
            buttonColor = Signature.backgroundColor.associateBorderColor;
          }

          const top = singleSignaturePaletteDetails.verticalSpaceTop;
          const left = singleSignaturePaletteDetails.horizontalSpaceLeft;
          const width = singleSignaturePaletteDetails.signaturePaletteWidth;
          const height = singleSignaturePaletteDetails.signaturePaletteHeight;

          const orginalFileSizeInfo = this.sharedService.getDocumentWidthAndHeight(
            left,
            top,
            width,
            height,
            true,
            signatureImageInfo,
            this.target,
            this.signDocument
          );

          let paletteResizeHeight;
          if (
            parseInt(orginalFileSizeInfo.height) <
            ConfigValues.messages.configurations.signatureDocumentPalette.minHeight
          ) {
            paletteResizeHeight = ConfigValues.messages.configurations.signatureDocumentPalette.minHeight;
          } else if (
            parseInt(orginalFileSizeInfo.height) >
            ConfigValues.messages.configurations.signatureDocumentPalette.maxHeightForText
          ) {
            paletteResizeHeight = ConfigValues.messages.configurations.signatureDocumentPalette.maxHeightForText;
          } else {
            paletteResizeHeight = orginalFileSizeInfo.height;
          }

          let paletteResizeWidth;
          if (orginalFileSizeInfo.width < ConfigValues.messages.configurations.signatureDocumentPalette.minWidth) {
            paletteResizeWidth = ConfigValues.messages.configurations.signatureDocumentPalette.minWidth;
          } else if (
            orginalFileSizeInfo.width > ConfigValues.messages.configurations.signatureDocumentPalette.maxWidthForText
          ) {
            paletteResizeWidth = ConfigValues.messages.configurations.signatureDocumentPalette.maxWidthForText;
          } else {
            paletteResizeWidth = orginalFileSizeInfo.width;
          }

          const myLocation = {
            fixTop: `${parseInt(orginalFileSizeInfo.top)}${Constants.pixel}`, // Resolve palette jump issue
            fixLeft: `${parseInt(orginalFileSizeInfo.left)}${Constants.pixel}`, // Resolve palette jump issue
            top: `${parseInt(orginalFileSizeInfo.top)}${Constants.pixel}`,
            left: `${parseInt(orginalFileSizeInfo.left)}${Constants.pixel}`,
            fitLeft: parseInt(left),
            fitTop: parseInt(top),
            fitWidth: parseInt(width),
            fitHeight: parseInt(height),
            width: `${parseInt(paletteResizeWidth)}${Constants.pixel}`,
            height: `${parseInt(paletteResizeHeight)}${Constants.pixel}`,
            checkboxLabelWidth: paletteResizeWidth - Signature.paletteSize.checkBoxLabelWidthRedeuse + 'px',
            checkboxLabelHeight: paletteResizeHeight - Signature.paletteSize.checkBoxLabelHeightRedeuse + 'px',
            fontSize: this.sharedService.getFontSize(parseInt(paletteResizeHeight), this.target?.offsetWidth),
            verticalSpaceTop: singleSignaturePaletteDetails.verticalSpaceTop,
            horizontalSpaceLeft: singleSignaturePaletteDetails.horizontalSpaceLeft,
            signaturePaletteWidth: singleSignaturePaletteDetails.signaturePaletteWidth,
            signaturePaletteHeight: singleSignaturePaletteDetails.signaturePaletteHeight
          };

          const fieldMandatory = singleSignaturePaletteDetails.mandatory;
          const data: any = {
            associate: singleSignaturePaletteDetails.associate,
            borderColor: this.borderColor,
            id: `${singlePaletteIndex}_${uniqueId()}`,
            mandatory: fieldMandatory,
            pendingApproveUser: singleSignaturePaletteDetails.pendingApproveUser,
            myLocation,
            name: singleSignaturePaletteDetails.paletteType,
            nickName: singleSignaturePaletteDetails.paletteType,
            page: singleSignaturePaletteDetails.signatureDocumentPageNumber,
            signpad: singleSignaturePaletteDetails.paletteType === Signature.paletteType.signValue ? true : false,
            userId,
            textFieldActAs: singleSignaturePaletteDetails.textFieldActAs,
            textPlaceholder: singleSignaturePaletteDetails.textPlaceholder,
            view: false,
            group: singleSignaturePaletteDetails.group,
            groupName: singleSignaturePaletteDetails.groupName,
            groupColor: '',
            signature: '',
            isResizable: true,
            isDraggable: true,
            fieldorder: !isBlank(singleSignaturePaletteDetails.fieldorder)
              ? singleSignaturePaletteDetails.fieldorder
              : 0,
            hidePalette: false,
            iconColor: buttonColor,
            resizeMinWidth: ConfigValues.messages.configurations.signatureDocumentPalette.minWidth,
            resizeMinHeight: ConfigValues.messages.configurations.signatureDocumentPalette.minHeight,
            resizeMaxWidth:
              singleSignaturePaletteDetails.paletteType === Signature.paletteType.signValue
                ? ConfigValues.messages.configurations.signatureDocumentPalette.signwidth
                : Signature.paletteSize.maxWidth,
            resizeMaxHeight:
              singleSignaturePaletteDetails.paletteType === Signature.paletteType.signValue
                ? ConfigValues.messages.configurations.signatureDocumentPalette.signheight
                : ConfigValues.messages.configurations.signatureDocumentPalette.resizeMaxHeightForText
          };
          this.prepopulationData = JSON.parse(this.signatureDocumentDetails.prepopulationData);
          let signatureValue = '';
          if (singleSignaturePaletteDetails.paletteType.toLowerCase() === Signature.paletteType.textField.toLowerCase() && this.prepopulationData && data.userId !== this.userData.userId) {
            signatureValue = this.getPrepopulatedValue(singleSignaturePaletteDetails.textFieldActAs);
          }
          if (singleSignaturePaletteDetails.textFieldActAs !== 'Text') {
            if (signatureValue && signatureValue != '') {
              data.signature = signatureValue;
              data.view = true;
            }
          }
          //    TODO: Need to add some conditions.
          if (singleSignaturePaletteDetails.paletteType === Signature.paletteType.checkboxValue) {
            data.checkbox = true;
            data.signature = false;
            data.checkboxLabel = singleSignaturePaletteDetails.checkboxLabel;
            data.checkboxLabelValue = singleSignaturePaletteDetails.checkBoxPaletteLabel
              ? singleSignaturePaletteDetails.checkBoxPaletteLabel
              : '';
            if (!data.checkboxLabel) {
              let responsiveCheckBox;
              if (window.innerWidth < 481) {
                responsiveCheckBox = 32;
              } else {
                responsiveCheckBox = 30;
              }

              data.myLocation.width =
                ConfigValues.messages.configurations.signatureDocumentPalette.checkboxHeight -
                responsiveCheckBox +
                Constants.pixel;
              data.myLocation.height =
                ConfigValues.messages.configurations.signatureDocumentPalette.checkboxHeight -
                responsiveCheckBox +
                Constants.pixel;
              data.isResizable = false;
            } else {
              const getHeight = parseInt(data.myLocation.height);

              if (getHeight < Signature.paletteSize.responsiveCheckBox) {
                data.myLocation.height = `${Signature.paletteSize.responsiveCheckBox}${Constants.pixel}`;
              }
              this.checkboxLabelValue[data.id] = singleSignaturePaletteDetails.checkBoxPaletteLabel;
              data.myLocation.checkboxLabelWidth =
                paletteResizeWidth - Signature.paletteSize.checkBoxLabelWidthRedeuse + Constants.pixel;
              data.myLocation.checkboxLabelHeight =
                paletteResizeHeight - Signature.paletteSize.checkBoxLabelHeightRedeuse + Constants.pixel;
              data.resizeMinHeight = ConfigValues.messages.configurations.signatureDocumentPalette.minHeight + 5;
            }

            if (singleSignaturePaletteDetails.group && (userId == this.userData.userId || userId == 0)) {
              const checkSignatureType = this.signatureTypeTagData
                ? parseInt(this.signatureTypeTagData.checkBoxGroupAreMandatory)
                : this.displayTypeConditions.checkBoxGroupAreMandatory
                  ? this.displayTypeConditions.checkBoxGroupAreMandatory
                  : 0;

              const groupData = {
                groupName: singleSignaturePaletteDetails.groupName,
                totalField: 0,
                color: '',
                checked: false,
                condition: checkSignatureType,
                userId: userId,
                approver: singleSignaturePaletteDetails.pendingApproveUser ? true : false
              };

              this.pushGroupObjectToArray(groupData);
              this.incrementOrDecrementPalateCount(singleSignaturePaletteDetails.groupName, true);
            }
          }
          if (!isBlank(userId)) {
            this.addObjects.push(data);
          }
        }
        if (signaturePaletteDetails.length == i) {
          if (!this.allowDesignView && this.addObjects.length > 0) {
            this.documentSend();
          }
        }
      });
      this.isUploadDocView = false;
    } else {
      this.setTopAndLeftDynamic();
    }
  }

  getPrepopulatedValue(textFieldActAs) {
    if (!this.prepopulationData) return '';
    if (textFieldActAs === Signature.prepopulationFields.mrn) {
      let prepopulationDataMrn = this.prepopulationData.patientMrn;
        if (this.displayTypeConditions && this.displayTypeConditions.patientInformationExchange === Signature.prepopulationFields.guid) {
          prepopulationDataMrn = this.prepopulationData.patientGuid;
        }
        return prepopulationDataMrn || '';
    }
    if (!this.prepopulationData.patient || this.prepopulationData.patient.id === '' || +this.prepopulationData.patient.id === 0) return '';
    switch (textFieldActAs) {
      case Signature.prepopulationFields.firstName:
        return this.prepopulationData.patient.firstName ? this.prepopulationData.patient.firstName : '';
      case Signature.prepopulationFields.lastName:
        return this.prepopulationData.patient.lastName ? this.prepopulationData.patient.lastName : '';
      case Signature.prepopulationFields.flname:
        return (this.prepopulationData.patient.firstName && this.prepopulationData.patient.lastName) ?
          `${this.prepopulationData.patient.firstName} ${this.prepopulationData.patient.lastName}` : '';
      case Signature.prepopulationFields.lfname:
        return (this.prepopulationData.patient.firstName && this.prepopulationData.patient.lastName) ?
          `${this.prepopulationData.patient.lastName} ${this.prepopulationData.patient.firstName}` : '';
      case Signature.prepopulationFields.dob:
        return this.prepopulationData.patient.dateOfBirth ? this.prepopulationData.patient.dateOfBirth : '';
      case Signature.prepopulationFields.mobile:
        return this.prepopulationData.patient.mobile ? this.prepopulationData.patient.mobile : '';
      case Signature.prepopulationFields.email:
        return this.prepopulationData.patient.email ? this.prepopulationData.patient.email : '';
      case Signature.prepopulationFields.gender:
        return this.prepopulationData.patient.gender ? this.prepopulationData.patient.gender : '';
      default:
        return '';
    }
  }

  incrementOrDecrementPalateCount(groupName, condition): void {
    for (let i = 0; i < this.signatureGroupDetails.length; i++) {
      const data = this.signatureGroupDetails[i];
      if (data.groupName == groupName) {
        if (condition) {
          data.totalField = data.totalField + 1;
        } else {
          data.totalField = data.totalField != 0 ? data.totalField - 1 : 0;
        }
      }
    }
  }

  createPaletteButton(field: any, checkboxLabelCondition = true, groupObject: any): void {
    const scrollTop = this.signDocument?.nativeElement?.scrollTop;
    const scrollLeft = this.signDocument?.nativeElement?.scrollLeft;
    const top = scrollTop + 100;
    const left = scrollLeft + 100;
    if (this.paletteSize) {
      this.paletteSize.top = top;
      this.paletteSize.left = left;
    }
    const paletteSizeWidth = this.paletteSize?.width;
    const paletteSizeHeight = this.paletteSize?.height;
    const orginalFileSizeInfo = this.sharedService.getDocumentWidthAndHeight(
      left,
      top,
      parseFloat(paletteSizeWidth),
      parseFloat(paletteSizeHeight),
      false,
      '',
      this.target,
      this.signDocument
    );

    const params = {
      field,
      checkboxLabelCondition,
      groupObj: groupObject,
      signatureTypeTagData: this.signatureTypeTagData,
      addObjects: this.addObjects,
      documentSignersDetails: this.selectedDocumentSignerDetails,
      paletteSize: this.paletteSize,
      orginalFileSizeInfo,
      pageCount: this.currentDocPage,
      data: this.data
    };
    this.sharedService.createPaletteButton(params, this.target?.offsetWidth);
  }
  async morePaletteButton(): Promise<void> {
    if (!isBlank(this.target) && !isBlank(this.signDocument)) {
      const documentSizeDetails = {
        imageSizeDetails: this.target,
        documentSizeDetails: this.signDocument
      };
      const popover = await this.popoverController.create({
        component: SignatureMorePalettesComponent,
        componentProps: {
          signatureTypeTagData: this.signatureTypeTagData,
          documentSignersDetails: this.selectedDocumentSignerDetails,
          paletteSize: this.paletteSize,
          documentFileSizeInfo: documentSizeDetails,
          userDetails: this.data,
          addObjects: this.addObjects,
          checkFieldDetails: this.signatureGroupDetails,
          checkboxGroupObject: this.checkboxGroupObject,
          pageCount: this.currentDocPage
        },
        cssClass: 'common-blue-popover more-palette-button',
        mode: 'ios'
      });
      await popover.present();
      await popover.onWillDismiss().then(({ data }) => {
        if (!isBlank(data)) {
          if (!isBlank(data?.groupObj)) {
            this.createPaletteButton(data?.field, data?.checkboxCondition, data?.groupObj);
          } else if (isBlank(data?.groupObj) && data?.checkboxCondition) {
            this.createPaletteButton(data?.field, true, '');
          } else if (isBlank(data?.groupObj) && !data?.checkboxCondition) {
            this.createPaletteButton(data?.field, false, '');
          }
          if (!isBlank(data?.checkboxGroupDetails)) {
            this.pushGroupObjectToArray(data?.checkboxGroupDetails[0]);
          }
          this.checkboxGroupObject = data?.checkboxGroupObject;
        }
      });
    }
  }
  removePaletteOption(data: any, index: number): void {
    let activityDescriptionfields = '';
    if (this.addObjects[index]?.name === Signature.paletteType.signValue) {
      activityDescriptionfields = this.commonService.getTranslateDataWithParam('MESSAGES.REMOVED_FIELD', {
        fieldName: `${Signature.paletteType.signatureField}`,
        page: `${this.addObjects[index]?.page}`
      });
    } else if (this.addObjects[index]?.name === Signature.paletteType.textValue) {
      activityDescriptionfields = this.commonService.getTranslateDataWithParam('MESSAGES.REMOVED_FIELD', {
        fieldName: `${Signature.paletteType.textField}`,
        page: `${this.addObjects[index].page}`
      });
    } else {
      const checkBoxCondition = this.addObjects[index]?.mandatory ? Signature.mandatory : Signature.notMandatory;
      activityDescriptionfields = this.commonService.getTranslateDataWithParam('MESSAGES.REMOVED_CHECKBOX', {
        checkBoxCondition: `${checkBoxCondition}`,
        page: `${this.addObjects[index]?.page}`
      });
    }
    if (index !== -1) {
      this.addObjects.splice(index, 1);
    }
    let count = 0;
    this.addObjects.forEach((element) => {
      count = element.groupName === data.groupName ? 1 : 0;
    });
    if (count === 0) {
      this.signatureGroupDetails.forEach((element, index) => {
        if (element.groupName === data.groupName) {
          this.signatureGroupDetails.splice(index, 1);
          this.incrementOrDecrementPalateCount(element.groupName, false);
        }
      });
      if (this.signatureGroupDetails.length === 0) {
        this.checkboxGroupObject.showCheckboxGroup = true;
      }
    }
    const removePaletteDes = {
      displayName: this.userData.displayName,
      activityDescriptionfields,
      actualFileName: this.selectedFileDetails?.documentDisplayText,
      documentUniqueName: this.signatureDocumentDetails?.document?.displayText
    };
    this.sharedService.trackActivity({
      type: Activity.signatureRequest,
      name: Activity.removePalette,
      des: { data: removePaletteDes, desConstant: Activity.removePaletteDes },
      linkageId: this.selectedFileDetails?.documentDisplayText
    });
  }
  checkboxClick(): boolean {
    return this.enableSign ? true : false;
  }
  cancel(): void {
    this.commonService
      .showAlert({
        message: '',
        header: 'MESSAGES.SIGNATURE_REQUEST_CONFIRMATION',
        cssClass: 'common-alert document-auto-sign'
      })
      .then((confirmation) => {
        if (confirmation) {
          const cancelSignatureProcessDes = {
            displayName: this.userData.displayName,
            actualFileName: this.selectedFileDetails.documentDisplayText,
            documentUniqueName: this.signatureDocumentDetails.document.displayText
          };
          this.sharedService.trackActivity({
            type: Activity.signatureRequest,
            name: Activity.cancelSignatureProcess,
            des: {
              data: cancelSignatureProcessDes,
              desConstant: Activity.cancelSignatureProcessDes
            },
            linkageId: this.selectedFileDetails.documentDisplayText
          });
          this.commonService.navCtrl.back();
        }
      });
  }
  prepopulateData(params: { element; signatureTypeTagData; prepopulationData }) {
    // Pre-population text palette
    const { element, signatureTypeTagData, prepopulationData } = params;
    element.paletteType = element?.paletteType || element?.name;
    if (
      [element.name, element.paletteType].includes(Signature.paletteType.textValue) &&
      prepopulationData &&
      !isBlank(prepopulationData.patientMrn)
    ) {
      if (element.textFieldActAs === Signature.prepopulationFields.mrn) {
        element.nickName = element.textPlaceholder;
        let prepopulationDataMrn = prepopulationData.patientMrn;
        if (
          signatureTypeTagData?.patientInformationExchange === Signature.prepopulationFields.guid &&
          !isBlank(this.sharedService.automaticLinkedItems)
        ) {
          prepopulationDataMrn = prepopulationData.patientGuid;
        }
        element.signature = prepopulationDataMrn;
      }
      if (isPresent(prepopulationData?.patient?.id) && +prepopulationData.patient.id !== 0) {
        element.nickName = element.textPlaceholder;
        if (element.textFieldActAs === Signature.prepopulationFields.firstName || element.textFieldActAs === Signature.prepopulationFields.lastName) {
          element.signature =
            element.textFieldActAs === Signature.prepopulationFields.firstName
              ? prepopulationData.patient.firstName
              : prepopulationData.patient.lastName;
        }
        if (element.textFieldActAs === Signature.prepopulationFields.firstName || element.textFieldActAs === Signature.prepopulationFields.lastName) {
          element.signature =
            element.textFieldActAs === Signature.prepopulationFields.firstName
              ? prepopulationData.patient.firstName
              : prepopulationData.patient.lastName;
        }
        if (element.textFieldActAs === Signature.prepopulationFields.flname) {
          element.signature = [prepopulationData.patient.firstName, prepopulationData.patient.lastName].join(' ');
        } else if (element.textFieldActAs === Signature.prepopulationFields.lfname) {
          element.signature = [prepopulationData.patient.lastName, prepopulationData.patient.firstName].join(' ');
        }
        if (element.textFieldActAs === Signature.prepopulationFields.dob && !isBlank(prepopulationData.patient?.dateOfBirth)) {
          element.signature = prepopulationData.patient.dateOfBirth;
        } else if (element.textFieldActAs === Signature.prepopulationFields.dob && isPresent(prepopulationData.patient?.dob)) {
          element.signature = prepopulationData.patient.dob;
        }
        if (element.textFieldActAs === Signature.prepopulationFields.mobile && !isBlank(prepopulationData.patient?.mobile)) {
          element.signature = prepopulationData.patient.mobile;
        }
        if (element.textFieldActAs === Signature.prepopulationFields.email && !isBlank(prepopulationData.patient?.email)) {
          element.signature = prepopulationData.patient?.email;
        }
        if (element.textFieldActAs === Signature.prepopulationFields.gender && !isBlank(prepopulationData.patient?.gender)) {
          element.signature = prepopulationData.patient.gender;
        }
      }
      if (element.textFieldActAs !== Signature.paletteType.textValue) {
        this.textValue[element.id] = element.signature && !isBlank(element.signature) ? element.signature : '';
      }
      element.isDisableForPatient =
        (!signatureTypeTagData?.editablePrePopulatedFields &&
          !isBlank(element.signature) &&
          element.signature &&
          +this.userData.group === UserGroup.PATIENT) ||
        false;
    }
    // End of Pre-population text palette
    return element;
  }
  signTotalCount = 0;
  signFields = [];

  documentSend(): void {
    let checkMySign = true;
    let checkRecipentSign = false;
    let checkAssociateSign = false;
    let checkmyPalette = false;
    let checkPatientPalette = false;
    let checkboxLabelNeed = false;
    let checkboxCondition = true;
    let checkPendingApprove = false;
    let groupForMe = false;
    let checkboxGroup = true;
    let myfirstPage = 0;
    const sendingButtonValue = this.commonService.getTranslateData('BUTTONS.SENDING');
    const groupCondition =
      this.signatureTypeTagData?.checkBoxGroupAreMandatory ===
        Signature.checkboxGroupValidationBehavior.allRequiredValue ||
        this.signatureTypeTagData?.checkBoxGroupAreMandatory === Signature.checkboxGroupValidationBehavior.optional
        ? false
        : true;

    if (!isBlank(this.addObjects)) {
      if (!this.data.allowPersonalSignature) {
        checkmyPalette = true;
      }
      //crud design check staff has alredy palette
      if (
        !checkmyPalette &&
        this.signatureTypeTagData.fromCrud &&
        this.folderNameCopyFilingCenter === Constants.docFromFile
      ) {
        checkmyPalette = true;
      }

      if (!this.signatureTypeTagData.allowPendingApproveSignature) {
        checkPendingApprove = true;
      }

      if (!this.signatureTypeTagData.allowRecipientRoles && !this.data.allowPersonalSignature) {
        checkRecipentSign = true;
        checkPatientPalette = true;
      }

      if (!this.signatureTypeTagData.allowAssociateRoles) {
        checkAssociateSign = true;
      }

      if (
        this.signatureTypeTagData.allowRecipientRoles &&
        this.signatureTypeTagData.allowAssociateRoles &&
        this.signatureTypeTagData.obtainSignature
      ) {
        checkPatientPalette = true;
      }

      if (
        !this.signatureTypeTagData.allowRecipientRoles &&
        this.signatureTypeTagData.allowAssociateRoles &&
        this.signatureTypeTagData.obtainSignature
      ) {
        checkPatientPalette = true;
      }
      if (!this.signatureTypeTagData.obtainSignature) {
        checkPatientPalette = true;
      }

      // TODO : Need to add more conditions.
      let prefilledStaffPalleteCount = 0;
      this.addObjects.forEach((element, index) => {
        const isFirstCheck = this.sharedService.automaticLinkedItems && !this.documentRootInfo && !deepCopyJSON(element)?.paletteType;
        if (isFirstCheck) {
          const prepopulatedElement = this.prepopulateData({
            element,
            signatureTypeTagData: this.signatureTypeTagData,
            prepopulationData: this.sharedService.automaticLinkedItems
          });
          if (element?.signature) {
            prefilledStaffPalleteCount += 1;
          }
          Object.assign(element, prepopulatedElement);
        }
        if (!this.selfSignButtonClicked) {
          if (Number(element.userId) === Number(this.userData.userId) && !element.pendingApproveUser) {
            this.signFields.push(element);
          }
          this.signTotalCount = this.signFields.length;
        }
        if (element.name === Signature.paletteType.checkboxValue) {
          if (element.checkboxLabel) {
            element.checkboxLabelValue = this.checkboxLabelValue[element.id];
            if (isBlank(element.checkboxLabelValue) && !checkboxLabelNeed) {
              checkboxLabelNeed = true;
              if (!myfirstPage) {
                myfirstPage = element.page;
              }
            }
          }
        }
        if (element.group && Number(element.userId) === Number(this.userData.userId) && !element.pendingApproveUser) {
          groupForMe = true;
        }

        if (
          this.data.allowPersonalSignature &&
          Number(element.userId) === Number(this.userData.userId) &&
          !checkmyPalette &&
          !element.pendingApproveUser
        ) {
          checkmyPalette = true;
        }

        if (
          this.data.allowPersonalSignature &&
          Number(element.userId) === Number(this.userData.userId) &&
          element.pendingApproveUser
        ) {
          element.signaturePaletteLock = Signature.paletteLock;
        }

        if (
          !checkPendingApprove &&
          Number(element.userId) === Number(this.userData.userId) &&
          element.pendingApproveUser
        ) {
          checkPendingApprove = true;
        }
        // Line 1406
        if (
          this.data.allowPersonalSignature &&
          checkMySign &&
          Number(element.userId) === Number(this.userData.userId) &&
          !element.signature &&
          element.mandatory &&
          !element.pendingApproveUser
        ) {
          checkMySign = false;
          if (!myfirstPage) {
            myfirstPage = element.page;
          }
        } else if (
          checkboxCondition &&
          Number(element.userId) === Number(this.userData.userId) &&
          !element.signature &&
          !element.mandatory &&
          !groupCondition &&
          !element.pendingApproveUser
        ) {
          checkboxCondition = false;
          this.finishButtonClickCount = this.finishButtonClickCount + 1;
          if (!myfirstPage && this.finishButtonClickCount == 1) {
            myfirstPage = element.page;
          }
        } else if (
          checkboxCondition &&
          Number(element.userId) === Number(this.userData.userId) &&
          !element.mandatory &&
          groupCondition &&
          !element.pendingApproveUser
        ) {
          if (!myfirstPage) {
            myfirstPage = element.page;
          }
        }
        if (
          this.signatureTypeTagData.allowRecipientRoles &&
          Number(this.data.recipient.id) === Number(element.userId)
        ) {
          checkRecipentSign = true;
        }
        if (
          this.signatureTypeTagData.allowAssociateRoles &&
          Number(this.data.associate.id) === Number(element.userId)
        ) {
          checkAssociateSign = true;
        }
        if (
          !this.signatureTypeTagData.allowRecipientRoles &&
          (this.signatureTypeTagData.allowAssociateRoles || (this.data.allowPersonalSignature && element.userId === 0))
        ) {
          checkRecipentSign = true;
        }
        if (
          this.signatureTypeTagData.obtainSignature &&
          !checkPatientPalette &&
          this.signatureTypeTagData.allowRecipientRoles &&
          !this.signatureTypeTagData.allowAssociateRoles &&
          element.userId === 0
        ) {
          checkPatientPalette = true;
        }
        if (
          this.signatureTypeTagData.obtainSignature &&
          !checkPatientPalette &&
          element.userId === 0 &&
          !this.signatureTypeTagData.allowRecipientRoles &&
          !this.signatureTypeTagData.allowAssociateRoles
        ) {
          checkPatientPalette = true;
        }
        if (element.signature) {
          this.finishButtonClickCount = this.finishButtonClickCount === 0 ? 1 : this.finishButtonClickCount;
        }
        if (
          this.addObjects.length - 1 === index &&
          isFirstCheck &&
          prefilledStaffPalleteCount !== 0 &&
          prefilledStaffPalleteCount !== this.addObjects.length
        ) {
          checkMySign = false;
        }
      });
      if (!checkboxCondition && this.finishButtonClickCount === 1) {
        checkMySign = false;
      }

      if (checkMySign && (this.finishButtonClickCount === 0 || this.finishButtonClickCount === 1) && this.data.allowPersonalSignature) {
        if (this.finishButtonClickCount === 0) {
          checkMySign = false;
          this.finishButtonClickCount = this.finishButtonClickCount + 1;
        }
        this.signatureGroupDetails.forEach((item, index) => {
          if (Number(item.userId) !== Number(this.userData.userId) || item.approver) {
            delete this.signatureGroupDetails[index];
          }
        });
      }
      // at-least one, Any one
      if (
        groupForMe &&
        (this.signatureTypeTagData.checkBoxGroupAreMandatory ===
          Signature.checkboxGroupValidationBehavior.exactlyOneRequiredValue ||
          this.signatureTypeTagData.checkBoxGroupAreMandatory ===
          Signature.checkboxGroupValidationBehavior.atleastOneRequiredValue)
      ) {
        this.signatureGroupDetails.forEach((item) => {
          if (!item.checked) {
            checkboxGroup = false;
            checkMySign = false;
          }
        });
      }

      checkboxGroup =
        this.signatureTypeTagData.checkBoxGroupAreMandatory ===
          Signature.checkboxGroupValidationBehavior.allRequiredValue ||
          this.signatureTypeTagData.checkBoxGroupAreMandatory === Signature.checkboxGroupValidationBehavior.optional
          ? true
          : checkboxGroup;

      if (checkboxLabelNeed) {
        this.finishButtonClickCount = 0;
        this.commonService.showMessage(this.commonService.getTranslateData('ERROR_MESSAGES.REQUIRED_CHECKBOX_LABEL'));
      } else {
        if (
          checkboxGroup &&
          checkmyPalette &&
          checkMySign &&
          checkRecipentSign &&
          checkAssociateSign &&
          checkPatientPalette &&
          checkPendingApprove
        ) {
          let signaturePalette;
          const signaturePaletteDetails = [];
          this.addObjects.forEach((element) => {
            const signatureOwner = element.userId ? element.userId : '';
            let mySignature = element.signature && +element.userId === +this.userData.userId ? element.signature : '';
            let labelCheckBox = '';
            let checkboxLabelCondition = true;
            if (element.name === Signature.paletteType.signValue) {
              mySignature = mySignature.replace(Signature.signReplaceValue, '');
            } else if (element.name === Signature.paletteType.textValue) {
              mySignature = mySignature.replace(Signature.signaturePattern, '<br/>');
            } else if (element.name === Signature.paletteType.checkboxValue) {
              const label = element.checkboxLabel ? element.checkboxLabelValue : '';
              labelCheckBox = label.replace(Signature.signaturePattern, '<br/>');
              mySignature = String(mySignature);
              checkboxLabelCondition = element.checkboxLabel;
            }
            const order = !isBlank(element.fieldorder) ? element.fieldorder : 0;
            if (element.name === element.checkboxLabelValue && !element.checkboxLabel) {
              element.myLocation.fitWidth =
                ConfigValues.messages.configurations.signatureDocumentPalette.setFitHeightWidthForCheckbox;
              element.myLocation.fitHeight =
                ConfigValues.messages.configurations.signatureDocumentPalette.setFitHeightWidthForCheckbox;
            }

            signaturePalette = {
              fontSize: `${element.myLocation.fontSize}px`,
              fieldorder: order,
              textFieldActAs: element.textFieldActAs ? element.textFieldActAs : Signature.paletteType.textValue,
              textPlaceholder: element.textPlaceholder ? element.textPlaceholder : Signature.paletteType.textValue,
              mandatory: element.mandatory,
              pendingApproveUser: element.pendingApproveUser,
              checkboxLabel: checkboxLabelCondition,
              checkBoxPaletteLabel: labelCheckBox,
              associate: element.associate,
              fieldForUser: signatureOwner,
              signature: mySignature,
              signatureDocumentPageNumber: element.page,
              signaturePaletteWidth: element.myLocation.fitWidth,
              width: element.myLocation.width,
              signaturePaletteHeight: element.myLocation.fitHeight,
              horizontalSpaceLeft:
                parseFloat(element.myLocation.fitLeft) +
                ConfigValues.messages.configurations.signatureDocumentPalette.leftAlignmentAfterFillData,
              verticalSpaceTop: element.myLocation.fitTop,
              paletteType: element.name,
              group: element.group,
              groupColor: element.groupColor,
              groupName: element.groupName
            };
            signaturePaletteDetails.push(signaturePalette);
          });

          this.sharedService.isLoading = true;

          this.graphqlService.completeDocumentSignatureRequest(this.documentId, signaturePaletteDetails, this.selectedFileDetails?.isFileFromGallery).subscribe(
            (result) => {
              this.sharedService.isLoading = false;
              const data = result.data;
              const completeDocumentSignatureRequest = data.completeDocumentSignatureRequest;
              if (completeDocumentSignatureRequest && completeDocumentSignatureRequest.id !== '0') {
                this.sharedService.signatureStatus = completeDocumentSignatureRequest.signatureStatus;
                // CHP-18680 Removed moveFilingCenterToArchive from client side after signature request completed
                const obtainSignPollingData: any = {};
                if (completeDocumentSignatureRequest.notifyOnSubmitSignatureUsers) {
                  obtainSignPollingData.notifyOnSubmitSignatureUsers =
                    completeDocumentSignatureRequest.notifyOnSubmitSignatureUsers;
                } else {
                  if (completeDocumentSignatureRequest.notifyOnSubmitSignatureRoles) {
                    obtainSignPollingData.notifyOnSubmitSignatureRoles =
                      completeDocumentSignatureRequest.notifyOnSubmitSignatureRoles;
                  }
                }
                obtainSignPollingData.environment = environment.alias;
                obtainSignPollingData.serverBaseUrl = environment.apiServer;
                if (
                  this.sharedService.isEnableConfig(Config.enableApplessModel) &&
                  this.citusInteractionChannnel === Constants.magicLink
                ) {
                  obtainSignPollingData.workflow = Constants.appless;
                }
                obtainSignPollingData.apiVersion = ConfigValues.config.apiVersion;
                obtainSignPollingData.notificationMessageData = this.signatureDocumentDetails;
                obtainSignPollingData.tenantId = this.userData.tenantId;
                obtainSignPollingData.tenantName = this.userData.tenantName;
                obtainSignPollingData.notificationMessageData = completeDocumentSignatureRequest;
                if (
                  this.signatureTypeTagData.allowRecipientRoles &&
                  !isBlank(this.data.recipient) &&
                  this.data.recipient.id
                ) {
                  if (this.signatureTypeTagData.allowAssociateRoles) {
                    obtainSignPollingData.user = [this.data.associate.id];
                  } else {
                    obtainSignPollingData.user = this.data.recipient.id;
                  }

                  if (this.citusInteractionChannnel !== Constants.magicLink) {
                    this.socketService.emitEvent(Socket.obtainSignPollingToServer, obtainSignPollingData);
                  }
                } else {
                  // Push notification without polling
                  if (this.signatureTypeTagData.sendDocumentWithoutSignature) {
                    obtainSignPollingData.signed = true;
                  }
                  if (this.signatureTypeTagData.allowAssociateRoles) {
                    obtainSignPollingData.user = this.data.associate.id;
                  }
                  if (this.citusInteractionChannnel !== Constants.magicLink) {
                    this.socketService.emitEvent(Socket.obtainSignPollingToServer, obtainSignPollingData);
                  }
                }
                // Starting document reminder email implemetation
                let recipients = {};
                recipients = completeDocumentSignatureRequest.signatureByUsers;
                let cmisServerBaseUrl = environment.cmisApiBaseUrl.replace(Urls.cmisAPI, '');
                cmisServerBaseUrl = cmisServerBaseUrl.replace(Urls.https, '');
                const msg = this.commonService.getTranslateDataWithParam('MESSAGES.UNREAD_SIGNATURE_REQUEST', {
                  displayName: `${this.userData.displayName}`
                });
                let otherDatas: any = {};
                otherDatas = {
                  patientReminderTime: parseInt(this.sharedService.getConfigValue(Config.patientReminderTime), 10) * 1,
                  patientReminderTypes: this.sharedService.getConfigValue(Config.patientReminderTypes),
                  messageReplyTimeout: parseInt(this.sharedService.getConfigValue(Config.messageReplyTimeout), 10),
                  docId: completeDocumentSignatureRequest.id,
                  senderName: this.userData.displayName,
                  senderId: this.userData.userId,
                  tenantId: this.userData.tenantId,
                  tenantName: this.userData.tenantName,
                  serverBaseUrl: environment.apiServer,
                  apiVersion: ConfigValues.config.apiVersion,
                  message: msg,
                  cmisServerBaseUrl,
                  documentId: completeDocumentSignatureRequest.id,
                  organizationId: this.userData.tenantCmisId,
                  signDocReminderType: this.sharedService.getConfigValue(Config.patientReminderCheckingType),
                  environment: environment.alias,
                  docTenantId:
                    completeDocumentSignatureRequest.crossTenant &&
                      completeDocumentSignatureRequest.crossTenant !== 0 &&
                      completeDocumentSignatureRequest.crossTenant !== ''
                      ? completeDocumentSignatureRequest.crossTenant
                      : this.userData.tenantId
                };
                if (obtainSignPollingData.workflow && obtainSignPollingData.workflow === Constants.appless) {
                  otherDatas.docSendMode = Constants.appless;
                  otherDatas.applessDevices = 'both';
                }
                if (!completeDocumentSignatureRequest.type.allowPendingApproveSignature) {
                  this.socketService.emitEvent(Socket.reminderForDocument, recipients, otherDatas);
                }
                this.sharedService.reminderForDocument({ recipient: recipients, otherData: otherDatas }).subscribe();
                //  document reminder email implemetation ends
                const sendDocumentDes = {
                  displayName: this.userData.displayName,
                  actualFileName: this.selectedFileDetails.documentDisplayText,
                  recipient: this.data.recipient ? this.data.recipient.userName : '',
                  recipientId: this.data.recipient ? this.data.recipient.userId : '',
                  obtainSignature: this.signatureTypeTagData.obtainSignature
                };
                this.sharedService.trackActivity({
                  type: Activity.signatureRequest,
                  name: Activity.sendDocument,
                  des: {
                    data: sendDocumentDes,
                    desConstant: Activity.sendDocumentDes
                  },
                  linkageId: this.selectedFileDetails.documentDisplayText
                });
                if (this.sharedService.isEnableConfig(Config.signatureServerside)) {
                  if (this.sharedService.automaticLinkedItems) {
                    this.sharedService.automaticLinkedItems.documentId = data?.completeDocumentSignatureRequest?.id;
                  }
                  this.commonService.redirectToPage('/document-center/pending-documents');
                }
              } else {
                const documentSendError = {
                  displayName: this.userData.displayName,
                  actualFileName: this.selectedFileDetails.documentDisplayText,
                  recipient: !isBlank(this.data.recipient) ? this.data.recipient.userName : '',
                  recipientId: !isBlank(this.data.recipient) ? this.data.recipient.userId : '',
                  obtainSignature: this.signatureTypeTagData.obtainSignature
                };
                this.sharedService.trackActivity({
                  type: Activity.signatureRequest,
                  name: Activity.sendDocument,
                  des: {
                    data: documentSendError,
                    desConstant: Activity.documentSendErrorDes
                  },
                  linkageId: this.selectedFileDetails.documentDisplayText
                });
              }
            },
            () => {
              this.sharedService.isLoading = false;
            }
          );
        } else {
          if (!checkmyPalette) {
            this.showErrorMessage(this.userData.displayName);
          } else if (!checkAssociateSign) {
            this.showErrorMessage(this.data.associate.displayName);
          } else if (!checkPatientPalette) {
            this.showErrorMessage(Constants.patientValue);
          } else if (checkmyPalette && !checkMySign && checkRecipentSign && checkAssociateSign && checkPendingApprove) {
            this.addObjects.forEach((element) => {
              const isLoginUserSign = Number(element.userId) === Number(this.userData.userId);
              const pendingApproveUser = element.pendingApproveUser;
              element.isResizable = false;
              element.isDraggable = false;
              if (
                !isLoginUserSign || (pendingApproveUser && this.isUploadDocView)
              ) {
                element.hidePalette = true;
              }
              if (Number(element.userId) !== Number(this.userData.userId) && !pendingApproveUser && element.textFieldActAs && element.textFieldActAs !== 'Text') {
                element.hidePalette = false;
              }
            });
            this.enableSign = true;
            this.myPaletteSign = true;
            this.paletteButtons = false;
            let displayMessageText = sendingButtonValue;
            if (this.data.allowPersonalSignature && !this.signatureTypeTagData.allowRecipientRoles) {
              this.sendButtonText = this.saveButtonValue;
              displayMessageText = this.commonService.getTranslateData('BUTTONS.SAVING');
            } else {
              if (this.signatureTypeTagData.allowAssociateRoles) {
                this.sendButtonText = this.saveButtonValue;
              } else if (
                this.data.allowPersonalSignature &&
                !this.signatureTypeTagData.allowAssociateRoles &&
                !this.signatureTypeTagData.allowRecipientRoles
              ) {
                this.sendButtonText = this.saveButtonValue;
              } else {
                this.sendButtonText = this.sendButtonValue;
                checkboxCondition = false;
              }
              if (
                this.signatureTypeTagData.obtainSignature &&
                !this.signatureTypeTagData.allowAssociateRoles &&
                this.signatureTypeTagData.allowRecipientRoles
              ) {
                this.sendButtonText = this.saveButtonValue;
              }
            }
            if (!this.signatureTypeTagData.obtainSignature && this.signatureTypeTagData.allowAssociateRoles) {
              this.sendButtonText = this.sendButtonValue;
              displayMessageText = sendingButtonValue;
            }
            if (this.selfSignButtonClicked) {
              this.commonService.showMessage(
                this.commonService.getTranslateDataWithParam('VALIDATION_MESSAGES.REQUIRED_SIGNATURE_FIELD', {
                  displayText: `${displayMessageText}`
                })
              );
            }
            if (myfirstPage !== 0 && this.currentDocPage != myfirstPage) {
              this.nextPage = true;
              this.currentDocPage = myfirstPage;
              this.buildImageUrl();
            }
            this.selfSignButtonClicked = true;
          } else if (!checkRecipentSign) {
            let displayName = Constants.userPatient;
            if (!isBlank(this.data.recipient)) {
              displayName = this.data.recipient.displayName;
            }
            this.showErrorMessage(displayName);
          } else if (!checkPendingApprove) {
            this.commonService.showMessage(
              this.commonService.getTranslateData('ERROR_MESSAGES.NO_FIELDS_FOR_APPROVER')
            );
          }
        }
      }
    } else {
      this.commonService.showMessage(this.commonService.getTranslateData('ERROR_MESSAGES.N0_FIELDS'));
    }
  }

  async openSignPad(signData: any): Promise<any> {
    let obtainSignature = false;
    if (isPresent(this.signatureTypeTagData)) {
      obtainSignature = this.signatureTypeTagData.obtainSignature;
    } else if (isPresent(this.displayTypeConditions)) {
      obtainSignature = this.displayTypeConditions.obtainSignature;
    }
    this.paletteStatus =
      this.paletteStatus == this.paletteStatuses.confirmed ? this.paletteStatus : this.paletteStatuses.signing;
    const enableSaveSignFlow = sessionStorage.getItem('enableSaveSignFlow') ? JSON.parse(sessionStorage.getItem('enableSaveSignFlow')) : null;
    if (this.enableSaveSignatureToProfile && !isBlank(this.sharedService.userData.savedUserSignature) && enableSaveSignFlow) {
      const addPrefix = this.sharedService.userData.savedUserSignature.startsWith(Signature.signReplaceValue) ? '' : Signature.signReplaceValue;
      const signCombineData = `${addPrefix}${this.sharedService.userData.savedUserSignature}`;
      if (!isBlank(this.dummyDroppedObjects)) {
        this.addObjects = this.dummyDroppedObjects;
      }
      const index = this.addObjects.indexOf(signData);
      if (this.confirmSaveSignToProfile) {
        this.showLoadSignature(signCombineData, index, signData, obtainSignature);
      } else if (this.sharedService.userData.useSavedSign) {
        this.checkStartSignWithTextPad();
        this.combineCanvas(signCombineData, (sign) => {
          this.setSingleSign(index, sign);
        });
      } else {
        this.openSignPadWithCondition(signData, obtainSignature);
      }
    } else {
      // CHP-14958 Auto signature true but not added signature in profile page
      this.openSignPadWithCondition(signData, obtainSignature);
    }
  }

  dateCanvas(): string {
    const today = moment().format(Constants.dateFormat.mdy);
    const c = document.getElementById('dateCanvas') as HTMLCanvasElement;
    const ctx = c.getContext('2d');
    ctx.font = Signature.canvasAttributes.dateCanvasFont;
    ctx.fillText(today, Signature.canvasAttributes.fillTextX, Signature.canvasAttributes.fillTextY);
    const url = c.toDataURL('image/png');
    return url;
  }
  nameCanvas(text: string): string {
    const c = document.getElementById('nameCanvas') as HTMLCanvasElement;
    const ctx = c.getContext('2d');
    c.width = Signature.canvasAttributes.nameCanvasOnlyWidth;
    c.height = Signature.canvasAttributes.nameCanvasOnlyHeight;
    ctx.font = Signature.canvasAttributes.nameCanvasFont;
    ctx.fillText(text, Signature.canvasAttributes.fillTextX, Signature.canvasAttributes.fillTextY);
    const url = c.toDataURL('image/png');
    return url;
  }
  dateOrNameCanvasDraw(sigImg: string, callBack, text?: string): any {
    const dateOrNameToImageData = text ? this.nameCanvas(text) : this.dateCanvas();
    const multi = document.getElementById('combineCanvas') as HTMLCanvasElement;
    multi.height = text ? Signature.canvasAttributes.nameCanvasHeight : Signature.canvasAttributes.dateCanvasHeight;
    const multiCtx = this.getCombinedCanvasCtx(multi);
    const image = new Image();
    image.src = sigImg;
    image.onload = () => {
      multiCtx.drawImage(
        image,
        Signature.canvasAttributes.dx,
        Signature.canvasAttributes.dy,
        Signature.canvasAttributes.dw,
        Signature.canvasAttributes.dh1
      );
      const images = new Image();
      images.src = dateOrNameToImageData;
      images.onload = () => {
        multiCtx.drawImage(
          images,
          Signature.canvasAttributes.dx,
          Signature.canvasAttributes.dh1,
          Signature.canvasAttributes.dw,
          text ? Signature.canvasAttributes.dh2Name : Signature.canvasAttributes.dh2Date
        );
        const multiurl = multi.toDataURL('image/png');
        callBack(multiurl);
      };
    };
  }
  dateAndNameCanvasDraw(sigImg: string, callBack, text: string): any {
    const nameToImageData = this.nameCanvas(text);
    const dateToImageData = this.dateCanvas();
    const multi = document.getElementById('combineCanvas') as HTMLCanvasElement;
    multi.height = Signature.canvasAttributes.combinedCanvasHeight;
    const multiCtx = this.getCombinedCanvasCtx(multi);
    const image = new Image();
    image.src = sigImg;
    image.onload = () => {
      multiCtx.drawImage(
        image,
        Signature.canvasAttributes.dx,
        Signature.canvasAttributes.dy,
        Signature.canvasAttributes.dw,
        Signature.canvasAttributes.dh1
      );
      const images = new Image();
      images.src = nameToImageData;
      images.onload = () => {
        multiCtx.drawImage(
          images,
          Signature.canvasAttributes.dx,
          Signature.canvasAttributes.dy2DnT,
          Signature.canvasAttributes.dw,
          Signature.canvasAttributes.dh2DnT
        );
        const dateImages = new Image();
        dateImages.src = dateToImageData;
        dateImages.onload = () => {
          multiCtx.drawImage(
            dateImages,
            Signature.canvasAttributes.dx,
            Signature.canvasAttributes.dy3DnT,
            Signature.canvasAttributes.dw,
            Signature.canvasAttributes.dh3DnT
          );
          const multiurl = multi.toDataURL('image/png');
          callBack(multiurl);
        };
      };
    };
  }
  combineCanvas(sigImg: any, callBack: any): any {
    if (isPresent(this.signatureTypeTagData)) {
      if (isPresent(this.dateWithSign) && this.dateWithSign.sender) {
        this.dateOrNameCanvasDraw(sigImg, callBack);
      } else {
        callBack(sigImg);
      }
      return;
    } else if (this.dateWithSign.recipient && Number(this.signaturesByUsers.userId) === Number(this.userData.userId)) {
      this.dateOrNameCanvasDraw(sigImg, callBack);
    } else if (
      Number(this.dateWithSign.associate) === 2 &&
      (Number(this.userData.userId) === Number(this.documentOwnerId) ||
        this.associateSignaturesByUsersId.includes(Number(this.userData.userId)))
    ) {
      this.dateOrNameCanvasDraw(sigImg, callBack);
    } else if (
      Number(this.dateWithSign.associate) === 3 &&
      (Number(this.userData.userId) === Number(this.documentOwnerId) ||
        this.associateSignaturesByUsersId.includes(Number(this.userData.userId)))
    ) {
      this.dateOrNameCanvasDraw(sigImg, callBack, this.associateSignatureByRoles.displayName);
    } else if (
      Number(this.dateWithSign.associate) === 4 &&
      (Number(this.userData.userId) === Number(this.documentOwnerId) ||
        this.associateSignaturesByUsersId.includes(Number(this.userData.userId)))
    ) {
      this.dateAndNameCanvasDraw(sigImg, callBack, this.associateSignatureByRoles.displayName);
    } else if (
      this.dateWithSign.patient &&
      Number(this.userData.userId) === Number(this.documentOwnerId) &&
      this.documentInfo.signatureStatus === Signature.signatureStatus.signaturePendingStatus
    ) {
      this.dateOrNameCanvasDraw(sigImg, callBack);
    } else {
      callBack(sigImg);
    }
  }
  getCombinedCanvasCtx(multi: any): any {
    const multiCtx = multi.getContext('2d');
    multiCtx.fillStyle = Signature.canvasAttributes.fillStyle;
    multiCtx.clearRect(
      Signature.canvasAttributes.offsetX,
      Signature.canvasAttributes.offsetY,
      Signature.canvasAttributes.width,
      Signature.canvasAttributes.height
    );
    multiCtx.fillRect(
      Signature.canvasAttributes.offsetX,
      Signature.canvasAttributes.offsetY,
      Signature.canvasAttributes.width,
      Signature.canvasAttributes.height
    );
    return multiCtx;
  }
  onResizing(event: any, index: number): void {
    this.addObjects.forEach((element) => {
      const elementIndex = this.addObjects.indexOf(element);
      if (index === elementIndex) {
        element.myLocation.height = `${event.size.height}${Constants.pixel}`;
        element.myLocation.width = `${event.size.width}${Constants.pixel}`;
        element.myLocation.fontSize = this.sharedService.getFontSize(parseInt(event.size.height), this.target?.offsetWidth);
      }
    });
  }
  signerSelected(selectedSigner: any, condition: boolean): void {
    const identifier = condition ? selectedSigner.detail.value : selectedSigner.identifier;
    this.documentSignersDetails.forEach((item) => {
      item.selected = false;
      if (item.identifier === identifier) {
        item.selected = true;
        this.selectedDocumentSignerDetails = item;
      }
    });
    const selectedFieldDes = {
      displayName: this.userData.displayName,
      selectedUserName: this.selectedDocumentSignerDetails.name,
      actualFileName: this.selectedFileDetails.documentDisplayText
    };
    this.sharedService.trackActivity({
      type: Activity.signatureRequest,
      name: Activity.setField,
      des: { data: selectedFieldDes, desConstant: Activity.setFieldDes },
      linkageId: this.userData.displayName
    });
  }
  openTextPad(index: number): void {
    this.paletteStatus =
      this.paletteStatus == this.paletteStatuses.confirmed ? this.paletteStatus : this.paletteStatuses.signing;
    if (!isBlank(this.dummyDroppedObjects)) {
      this.addObjects = this.dummyDroppedObjects;
    }
    this.addObjects.forEach((element) => {
      const elementIndex = this.addObjects.indexOf(element);

      if (index === elementIndex) {
        if (element.checkbox) {
          element.checkboxLabelValue = this.applyLineBreaks(element.id, Constants.checkboxLabelValue);
        } else {
          element.signature = this.applyLineBreaks(element.id, Constants.textValue);
          element.view = true;
          if (element.signature) {
            this.autoFilterItem();
          }
        }
        element.border = !isBlank(element.signature)
          ? Signature.backgroundColor.signedBorder
          : Signature.backgroundColor.toBeSignedBorder;
        element.boxShadow = !isBlank(element.signature)
          ? Signature.backgroundColor.signedBoxShadow
          : Signature.backgroundColor.boxShadow;
      }
    });
    if (!isBlank(this.dummyDroppedObjects)) {
      this.dummyDroppedObjects = this.addObjects;
      this.addObjects = [];
      this.validatePalette();
    }
  }

  applyLineBreaks(strTextAreaId: string, type: string): string {
    const newStrTextAreaId = `text_fill_${strTextAreaId}`;
    this.textareaPalette = document.getElementsByClassName(newStrTextAreaId)[0];

    if (newStrTextAreaId && newStrTextAreaId != '' && this.textareaPalette) {
      this.textareaPalette.setAttribute('wrap', 'off');
      const strRawValue = this.textareaPalette.value;
      this.textareaPalette.value = '';
      this.textareaPaletteEmptyWidth = this.textareaPalette.scrollWidth;
      let i = 0,
        j;
      let strNewValue = '';
      while (i < strRawValue.length) {
        const breakOffset = this.findNextBreakLength(strRawValue.substr(i), undefined);

        if (breakOffset === null) {
          strNewValue += strRawValue.substr(i);
          break;
        }
        let newLineLength = breakOffset - 1;
        for (j = newLineLength - 1; j >= 0; j--) {
          const curChar = strRawValue.charAt(i + j);
          if (curChar == ' ' || curChar == '-' || curChar == '+') {
            newLineLength = j + 1;
            break;
          }
        }
        strNewValue += strRawValue.substr(i, newLineLength) + '\n';
        i += newLineLength;
      }
      this.textareaPalette.value = strNewValue;
      this.textareaPalette.setAttribute('wrap', '');

      return this.textareaPalette.value;
    } else {
      if (type === Constants.checkboxLabelValue && isPresent(this.checkboxLabelValue[strTextAreaId])) {
        return this.checkboxLabelValue[strTextAreaId];
      } else if (type === Constants.textValue && isPresent(this.textValue[strTextAreaId])) {
        return this.textValue[strTextAreaId];
      } else {
        return '';
      }
    }
  }

  findNextBreakLength(strSource, nLeft, nRight = -1): number {
    let nCurrent;
    if (typeof nLeft === 'undefined' || typeof nLeft === undefined) {
      nLeft = 0;
      nRight = -1;
      nCurrent = 64;
    } else {
      if (nRight == -1) nCurrent = nLeft * 2;
      else if (nRight - nLeft <= 1) return Math.max(2, nRight);
      else nCurrent = nLeft + (nRight - nLeft) / 2;
    }
    const strText = strSource.substr(0, nCurrent);
    const bLonger = this.textBreak(strText);
    if (bLonger) nRight = nCurrent;
    else {
      if (nCurrent >= strSource.length) return null;
      nLeft = nCurrent;
    }
    return this.findNextBreakLength(strSource, nLeft, nRight);
  }

  textBreak(strText: string): boolean {
    this.textareaPalette.value = strText;
    return this.textareaPalette.scrollWidth > this.textareaPaletteEmptyWidth;
  }

  showErrorMessage(displayName: string): void {
    this.commonService.showMessage(
      this.commonService.getTranslateDataWithParam('ERROR_MESSAGES.NOT_HAVE_FIELDS', { userName: `${displayName}` })
    );
  }

  checkboxStatusChange(event: any, index: number): void {
    setTimeout(() => {
      //firoz
      if (!isBlank(this.dummyDroppedObjects) && isBlank(this.addObjects)) {
        this.addObjects = this.dummyDroppedObjects;
      }

      const checkBoxGroupAreMandatory =
        this.signatureTypeTagData && this.signatureTypeTagData.checkBoxGroupAreMandatory
          ? this.signatureTypeTagData.checkBoxGroupAreMandatory
          : this.displayTypeConditions && this.displayTypeConditions.checkBoxGroupAreMandatory
            ? this.displayTypeConditions.checkBoxGroupAreMandatory
            : 0;

      const exactlyOneRequiredCondition =
        checkBoxGroupAreMandatory == Signature.checkboxGroupValidationBehavior.exactlyOneRequiredValue;

      const alLeastRequiredCondition =
        checkBoxGroupAreMandatory === Signature.checkboxGroupValidationBehavior.atleastOneRequiredValue;

      const isCheckBoxGroupAllowed = this.signatureTypeTagData
        ? this.signatureTypeTagData.checkBoxGroupAllowed
        : this.displayTypeConditions.checkBoxGroupAllowed;

      const isCheckBoxMandatory = this.signatureTypeTagData
        ? this.signatureTypeTagData.checkBoxAreMandatory
        : this.displayTypeConditions.checkBoxAreMandatory;

      this.addObjects.map((element, elementIndex: number) => {
        const isGroup = element.group;
        const isSameGroup = this.addObjects[index].groupName == element.groupName;

        if (index === elementIndex) {
          this.addObjects[elementIndex].signature = event.target.checked;
          this.addObjects[elementIndex].view = true;

          this.addObjects[elementIndex].border =
            this.addObjects[elementIndex].signature === true || !this.addObjects[elementIndex].mandatory
              ? Signature.backgroundColor.signedBorder
              : Signature.backgroundColor.toBeSignedBorder;
          this.addObjects[elementIndex].boxShadow =
            this.addObjects[elementIndex].signature === true || !this.addObjects[elementIndex].mandatory
              ? Signature.backgroundColor.optionalBoxShadow
              : Signature.backgroundColor.boxShadow;

          if (isCheckBoxGroupAllowed && isGroup && isSameGroup) {
            if (exactlyOneRequiredCondition || alLeastRequiredCondition) {
              //firoz
              this.addObjects[elementIndex].mandatory = true;
            }
          } else {
            if (isCheckBoxMandatory === Signature.mandatoryValue) {
              this.addObjects[elementIndex].mandatory = event.target.checked;
            }
          }
        } else {
          if (isGroup && isSameGroup) {
            if (exactlyOneRequiredCondition) {
              this.addObjects[elementIndex].signature = false;
              if (event.target.checked) {
                //firoz
                this.addObjects[elementIndex].border = Signature.backgroundColor.signedBorder;
                this.addObjects[elementIndex].boxShadow = Signature.backgroundColor.signedBoxShadow;
              } else {
                this.addObjects[elementIndex].border = Signature.backgroundColor.toBeSignedBorder;
                this.addObjects[elementIndex].boxShadow = Signature.backgroundColor.boxShadow;
              }
              this.addObjects[elementIndex].mandatory = event.target.checked ? false : true;
              this.addObjects[elementIndex].view = true;
            } else if (alLeastRequiredCondition) {
              //firoz
              this.addObjects[elementIndex].mandatory = false;
              this.addObjects[elementIndex].view = true;
              this.addObjects[elementIndex].border = Signature.backgroundColor.signedBorder;
              this.addObjects[elementIndex].boxShadow = Signature.backgroundColor.signedBoxShadow;
            }
          }
        }
      });
      if (this.addObjects[index].group) {
        this.groupArrayStatusChange(this.addObjects[index].groupName);
      }
      this.checkStartSignWithTextPad();
      if (!isBlank(this.dummyDroppedObjects)) {
        this.dummyDroppedObjects = this.addObjects;

        this.addObjects = [];
        this.autoFilterItem();
        this.validatePalette();
      }
    }, 0);
  }

  groupArrayStatusChange(groupName): void  {
    const checkBoxGroupAreMandatory =
      this.signatureTypeTagData && this.signatureTypeTagData.checkBoxGroupAreMandatory
        ? this.signatureTypeTagData.checkBoxGroupAreMandatory
        : this.displayTypeConditions && this.displayTypeConditions.checkBoxGroupAreMandatory
          ? this.displayTypeConditions.checkBoxGroupAreMandatory
          : 0;
    if (
      checkBoxGroupAreMandatory == Signature.checkboxGroupValidationBehavior.exactlyOneRequiredValue ||
      checkBoxGroupAreMandatory == Signature.checkboxGroupValidationBehavior.atleastOneRequiredValue
    ) {
      let objectArray = this.dummyDroppedObjects;

      if (this.showSignDoc) {
        objectArray = this.addObjects;
      }

      this.signatureGroupDetails.forEach((item) => {
        if (item.groupName === groupName) {
          let palatteChecked = false;
          const groupPalateIndex = [];

          objectArray.forEach((element, index) => {
            const getPaletteType = this.showSignDoc ? element.name : element.paletteType;
            if (
              getPaletteType == Signature.paletteType.checkboxValue &&
              element.group &&
              item.groupName == element.groupName
            ) {
              if (element.signature) {
                palatteChecked = true;
              } else if (
                checkBoxGroupAreMandatory === Signature.checkboxGroupValidationBehavior.atleastOneRequiredValue ||
                checkBoxGroupAreMandatory === Signature.checkboxGroupValidationBehavior.exactlyOneRequiredValue
              ) {
                groupPalateIndex.push(index);
              }
            }
          });

          if (palatteChecked) {
            item.checked = palatteChecked;
            groupPalateIndex.forEach((item) => {
              objectArray[item].mandatory = false;
              objectArray[item].border = Signature.backgroundColor.signedBorder;
              objectArray[item].boxShadow = Signature.backgroundColor.signedBoxShadow;
            });
          } else if (
            (checkBoxGroupAreMandatory === Signature.checkboxGroupValidationBehavior.atleastOneRequiredValue ||
              checkBoxGroupAreMandatory === Signature.checkboxGroupValidationBehavior.exactlyOneRequiredValue) &&
            groupPalateIndex.length
          ) {
            if (!palatteChecked) {
              item.checked = false;
            }
            if (checkBoxGroupAreMandatory === Signature.checkboxGroupValidationBehavior.atleastOneRequiredValue) {
              groupPalateIndex.forEach((item) => {
                objectArray[item].border = Signature.backgroundColor.toBeSignedBorder;
                objectArray[item].boxShadow = Signature.backgroundColor.boxShadow;
              });
            }
          }
        }
      });
    }
  }

  getScrollPosition(event: any): void {
    this.scrollTopPosition = event.detail.scrollTop;
  }

  onDragStart(isAdd: boolean): void {
    if (this.sharedService.platform.is('capacitor')) {
      const getAllClass = (<any>document).getElementsByClassName('view-document-page-ion-content')[0];
      if (getAllClass) {
        if (isAdd) {
          getAllClass.classList.add('disabled-scroll-on-drag-start');
        } else {
          getAllClass.classList.remove('disabled-scroll-on-drag-start');
        }
      }
    }
  }

  resetDivTransform(id: string): void {
    const value = 'translate(0px, 0px)';
    if (document.getElementById(`drop-${id}`)) {
      document.getElementById(`drop-${id}`).style.transform = value;
    }
  }

  onDragEnd(data: any, ev: any, index: number): void {
    const scrollLeft = this.signDocument.nativeElement.scrollLeft;
    let scrollTop = this.scrollTopPosition;
    const draggedObjPosition = data.getBoundingClientRect();
    const getContetntHeight = (<any>document).getElementsByClassName('view-document-page-ion-content')[0].offsetHeight;
    let top = draggedObjPosition.y;

    if (this.target.height < getContetntHeight) {
      scrollTop = 0;
    }

    let setAdjustHeight = 0;
    if (this.sharedService.platform.is('iphone') && this.sharedService.platform.is('capacitor')) {
      setAdjustHeight = Signature.adjustHeightiOS + 30;
    } else if (this.sharedService.platform.is('ipad') && this.sharedService.platform.is('capacitor')) {
      setAdjustHeight = Signature.adjustHeightiOS;
    } else {
      setAdjustHeight = Signature.adjustHeight;
    }

    top = top + scrollTop - setAdjustHeight;
    let left = draggedObjPosition.left;
    let width = ev.myLocation.width;
    let height = ev.myLocation.height;
    left = scrollLeft + left;
    const orginalFileInfo = this.sharedService.getDocumentWidthAndHeight(
      left,
      top,
      parseInt(width, 10),
      parseInt(height, 10),
      false,
      '',
      this.target,
      this.signDocument
    );
    if (width < ConfigValues.messages.configurations.signatureDocumentPalette.minWidth) {
      width = ConfigValues.messages.configurations.signatureDocumentPalette.minWidth;
    }
    if (height < ConfigValues.messages.configurations.signatureDocumentPalette.minHeight) {
      height = ConfigValues.messages.configurations.signatureDocumentPalette.minHeight;
    }
    if (ev.signpad == true) {
      if (width > ConfigValues.messages.configurations.signatureDocumentPalette.signwidth) {
        width = ConfigValues.messages.configurations.signatureDocumentPalette.signwidth;
      }
      if (height > ConfigValues.messages.configurations.signatureDocumentPalette.signheight) {
        height = ConfigValues.messages.configurations.signatureDocumentPalette.signheight;
      }
    } else {
      if (width > ConfigValues.messages.configurations.signatureDocumentPalette.maxWidthForText) {
        width = ConfigValues.messages.configurations.signatureDocumentPalette.maxWidthForText;
      }
      if (height > ConfigValues.messages.configurations.signatureDocumentPalette.maxHeightForText) {
        height = ConfigValues.messages.configurations.signatureDocumentPalette.maxHeightForText;
      }
    }
    const myLocation = {
      fixTop: ev.myLocation.fixTop,
      fixLeft: ev.myLocation.fixLeft,
      top: `${top < 0 ? 2 : top}${Constants.pixel}`,
      left: `${left}${Constants.pixel}`,
      fitLeft: orginalFileInfo.left,
      fitTop: orginalFileInfo.top,
      width,
      height,
      fitWidth: orginalFileInfo.width,
      fitHeight: orginalFileInfo.height,
      fontSize: this.sharedService.getFontSize(parseInt(height), this.target?.offsetWidth)
    };

    ev.myLocation = myLocation;
    this.addObjects[index].myLocation = myLocation;
    this.onDragStart(false);
  }

  findNext(fromButtonClick: any): void {
    let triggerValue = false;
    if (fromButtonClick) {
      this.startSign = true;
    }
    if (this.autoNavigateItem.length <= 0) {
      this.autoFilterItem();
    }
    if (this.documentInfo) {
      this.resetDroppedObjectColor();
    }

    if (
      this.autoNavigateItem.length <= 0 &&
      !this.showSignDoc &&
      this.paletteStatus !== this.paletteStatuses.notSigned
    ) {
      this.autoFilterItem();
      this.validatePalette();
    }
    this.autoNavigateItem.forEach((element, index) => {
      if (!triggerValue) {
        if (element.signatureDocumentPageNumber === this.currentDocPage) {
          triggerValue = true;
          this.resetDroppedObjectColor(element.id);
          if (document.getElementById(`text-${element.id}`)) {
            document.getElementById(`text-${element.id}`).focus();
          } else if (document.getElementById(`text${element.id}`)) {
            document.getElementById(`text${element.id}`).focus();
          }
          delete this.autoNavigateItem[index];
          if (
            element.paletteType === Signature.paletteType.signValue ||
            element.paletteType === Signature.paletteType.checkboxValue
          ) {
            this.setObjectASViewed(element.id);
            // validatePalette called at start of function for document with id, because actionsheet coming immediately after focusing to last palette.
            if (!this.documentInfo) {
              // validatePalette call reverted to avoid any imapact
              this.validatePalette();
            }
          }
        } else if (
          element.signatureDocumentPageNumber > this.currentDocPage ||
          element.signatureDocumentPageNumber < this.currentDocPage
        ) {
          triggerValue = true;
          this.resetDroppedObjectColor(element.id);
          if (fromButtonClick) {
            this.next(element.signatureDocumentPageNumber);
          }
        }
        this.filterAutoNavigateObject();
      }
    });
  }
  resetDroppedObjectColor(id?: any): void {
    this.dummyDroppedObjects.forEach((element, index) => {
      if (element.mandatory && (!element.signature || element.signature == '')) {
        element.border = Signature.backgroundColor.toBeSignedBorder;
        element.boxShadow = Signature.backgroundColor.boxShadow;
      } else if (!element.mandatory) {
        element.border = Signature.backgroundColor.signedBorder; //blue color
        element.boxShadow = Signature.backgroundColor.signedBoxShadow;
      }
      if (id === element.id) {
        element.border = Signature.backgroundColor.navigateBorder; //red color
        element.boxShadow = Signature.backgroundColor.navigateBoxShadow; // Change boxShadow to signedBoxShadow
      }
    });
  }
  private shouldSkipPrePopulatedField(element): boolean {
    if (this.displayTypeConditions?.editablePrePopulatedFields || !element.signature || !element.isDisableForPatient) {
      return false;
    }

    return element.paletteType === Signature.paletteType.checkboxValue || element.paletteType === Signature.paletteType.textValue;
  }

  autoFilterItem(): void {
    this.autoNavigateItem = [];
    this.dummyDroppedObjects.forEach((element) => {
      if (typeof element.signature != 'object') {
        if ((!element.view || (element.mandatory && !element.signature)) && element.mySignNeed && !this.shouldSkipPrePopulatedField(element)) {
          this.autoNavigateItem.push(element);
        }
      }
    });
    this.filterAutoNavigateObject();
  }
  filterAutoNavigateObject(): void {
    this.autoNavigateItem = this.autoNavigateItem.filter((n) => {
      return n != undefined;
    });
  }
  setObjectASViewed(id: any): void {
    this.dummyDroppedObjects.forEach((element) => {
      if (element.id == id) {
        element.view = true;
        if (!element.mandatory && !element.signature && element.paletteType === Signature.paletteType.signValue) {
          element.signature = '';
        }
      }
    });
  }

  validatePalette(): void {
    const signedObjects = [];
    let checkAssociateSign = false;
    let count = 0;
    let deletePalette;
    let unfilledFileds = true; //firoz
    const checkBoxGroupAreMandatory =
      this.signatureTypeTagData && this.signatureTypeTagData.checkBoxGroupAreMandatory
        ? this.signatureTypeTagData.checkBoxGroupAreMandatory
        : this.displayTypeConditions && this.displayTypeConditions.checkBoxGroupAreMandatory
          ? this.displayTypeConditions.checkBoxGroupAreMandatory
          : 0;

    if (
      checkBoxGroupAreMandatory === Signature.checkboxGroupValidationBehavior.exactlyOneRequiredValue ||
      checkBoxGroupAreMandatory === Signature.checkboxGroupValidationBehavior.atleastOneRequiredValue
    ) {
      this.signatureGroupDetails.forEach((item, index) => {
        if (Number(item.userId) !== Number(this.userData.userId) || item.approver) {
          delete this.signatureGroupDetails[index];
        }
      });
    }

    this.dummyDroppedObjects.forEach((element) => {
      if (
        (parseInt(element.fieldForUser, 10) === parseInt(this.userData.userId, 10) &&
          element.mandatory &&
          element.mySignNeed) ||
        (element.fieldForUser === 0 && element.mandatory && element.mySignNeed)
      ) {
        signedObjects.push(element);
      }
      if (
        (parseInt(element.fieldForUser, 10) === parseInt(this.userData.userId, 10) ||
          parseInt(element.fieldForUser, 10) === 0) &&
        element.mySignNeed &&
        !element.signature &&
        !element.mandatory &&
        element.view == false
      ) {
        signedObjects.push(element);
      }
      deletePalette = this.snapAndSignApproval && Number(element.fieldForUser) !== 0;
      if (!(element.signaturePaletteLock && deletePalette)) {
        if (
          this.snapAndSignApproval &&
          element.pendingApproveUser &&
          Number(element.fieldForUser) === Number(this.userData.userId)
        ) {
          this.snapSignPendingApproveUser = true;
        }
      }
      if (element.mySignNeed && (!element.view || (element.mandatory && !element.signature)) && !this.shouldSkipPrePopulatedField(element)) {
        unfilledFileds = false;
        this.finishSign = false;
        this.paletteStatus = this.paletteStatuses.signing;
      }
    });
    const length = signedObjects.length;
    signedObjects.forEach((data) => {
      count = (data.mandatory && data.signature) || (!data.mandatory && data.view) || this.shouldSkipPrePopulatedField(data) ? count + 1 : count;
    });
    if (
      checkBoxGroupAreMandatory === Signature.checkboxGroupValidationBehavior.exactlyOneRequiredValue ||
      checkBoxGroupAreMandatory === Signature.checkboxGroupValidationBehavior.atleastOneRequiredValue
    ) {
      let groupExist = false;
      this.signatureGroupDetails.forEach((grpDatas) => {
        if (!grpDatas.checked) {
          groupExist = true;
        }
      });

      unfilledFileds = groupExist ? false : true;
    }
    if (count === length) {
      this.approverSignPending = false;
      this.initiateResign = false;
      this.finishButtonName = this.commonService.getTranslateData('BUTTONS.FINISH');
      if (
        (unfilledFileds && this.paletteStatus === this.paletteStatuses.signing) ||
        this.paletteStatus === this.paletteStatuses.confirmed
      ) {
        this.paletteStatus = this.paletteStatuses.signed;
      } else if (unfilledFileds && this.paletteStatus === this.paletteStatuses.notSigned && this.snapAndSignApproval) {
        this.paletteStatus = this.paletteStatuses.confirmed;
      }
      if (
        this.displayTypeConditions?.allowAssociateRoles &&
        this.associateSignaturesByUsersId !== 0 &&
        this.userData.userId === this.documentOwnerId
      ) {
        checkAssociateSign = true;
      }
      if (checkAssociateSign) {
        if (
          this.displayTypeConditions.allowRecipientRoles ||
          (this.displayTypeConditions.allowAssociateRoles && !this.displayTypeConditions.obtainSignature)
        ) {
          this.finishButtonName = this.commonService.getTranslateData('BUTTONS.SEND');
        } else {
          this.finishButtonName = this.commonService.getTranslateData('BUTTONS.SUBMIT');
        }
      }
      if (this.snapAndSignApproval) {
        if (this.snapSignPendingApproveUser && !this.sharedService.consoloLoader) {
          // need to implement
        } else {
          if (!this.sharedService.consoloLoader) {
            this.represntativeConfirmSignature = true;
          }
        }
      }

      if (unfilledFileds) {
        //firoz
        this.finishSign = true;
        if (this.paletteStatuses.notConfirmed !== this.paletteStatus) {
          this.paletteStatus = this.paletteStatuses.signed;
        }
      }
      if (
        unfilledFileds &&
        !this.represntativeConfirmSignature &&
        this.finishSign &&
        !this.initiateResign &&
        this.paletteStatuses.signed === this.paletteStatus &&
        this.documentInfo
      ) {
        this.finishSign = false;
        this.initiateResign = true;
        this.paletteStatus = this.paletteStatuses.notConfirmed;
        this.finishButtonName = this.commonService.getTranslateData('BUTTONS.FINISH');
      } else {
        // Hide next button
        if (this.snapAndSignApproval) {
          this.finishSign = true;
        }
      }
    } else {
      this.finishSign = false;
      this.approverSignPending = true;
      if (this.represntativeConfirmSignature) {
        this.paletteStatus = this.paletteStatuses.confirmed;
      }
    }
    if (this.finishButtonName === this.commonService.getTranslateData('BUTTONS.FINISH')) {
      this.checkStartSignWithTextPad();
    }
  }

  async presentResignActionSheet(isCheckBoxGroupConfirm = false): Promise<void>  {
    if (!isCheckBoxGroupConfirm) {
      isCheckBoxGroupConfirm = this.checkEveryFieldAsGroupBox(this.dummyDroppedObjects);
    }

    this.finishButtonName = this.commonService.getTranslateData('BUTTONS.FINISH');
    this.finishSign = false;
    this.paletteStatus = this.paletteStatuses.notConfirmed;
    const cancelOption = {
      text: this.commonService.getTranslateData('BUTTONS.CANCEL'),
      id: 'action-cancel',
      role: 'cancel',
      handler: () => {
        this.findNext(false);
        this.actionSheetController.dismiss(null, '', 'resign-actionsheet');
      }
    };
    const signOption = {
      text: this.commonService.getTranslateData('BUTTONS.CONFIRM_SIGNATURE'),
      id: 'action-confirm',
      handler: () => {
        this.initiateResign = false;
        this.finishSign = true;
        this.actionSheetController.dismiss(null, '', 'resign-actionsheet');
        this.submitDocument(this.displayTypeConditions?.obtainSignature, isCheckBoxGroupConfirm);
      }
    };
    const buttons = [signOption, cancelOption];
    const actionSheet = await this.actionSheetController.create({
      mode: 'ios',
      id: 'resign-actionsheet',
      buttons
    });
    await actionSheet.present();
  }

  pushGroupObjectToArray(groupData: any): void {
    if (this.signatureGroupDetails && !this.signatureGroupDetails.length) {
      this.signatureGroupDetails.push(groupData);
    } else {
      let allready = false;
      this.signatureGroupDetails.forEach((element) => {
        if (element.groupName === groupData.groupName) {
          allready = true;
        }
      });

      if (!allready) {
        this.signatureGroupDetails.push(groupData);
      }
    }
  }

  submitDocument(isSignSubmit = false, isCheckBoxGroupConfirm = false): void {
    this.sharedService.isLoading = true;
    let completeSignatureProcess = !isSignSubmit;
    let approveSignatureProcess = true;
    let snapAndSaveApprove = !isSignSubmit;
    let associateSignatureProcess = this.documentInfo.associateSignatureProcess;
    let signatureData;
    const signatureDataDetails = [];
    this.dummyDroppedObjects.forEach((value) => {
      let checkBoxLabel = '';
      if (
        value.signaturePaletteLock !== Signature.paletteLock &&
        (value.signature || (!value.signature && !value.mandatory) || isCheckBoxGroupConfirm === true)
      ) {
        if (value.associateUser && associateSignatureProcess) {
          associateSignatureProcess = false;
        }
        let sign = '';
        let checkboxLabelCondition = true;
        if (
          !value.mandatory &&
          !value.signature &&
          (value.paletteType === Signature.paletteType.signValue ||
            value.paletteType === Signature.paletteType.textValue)
        ) {
          value.signature = '';
        }
        if (
          value.paletteType === Signature.paletteType.signValue &&
          typeof value.signature === Signature.variableTypes.stringType
        ) {
          sign = value.signature.replace('data:image/png;base64,', '');
        } else if (
          value.paletteType === Signature.paletteType.textValue &&
          typeof value.signature === Signature.variableTypes.stringType
        ) {
          sign = value.signature.replace(/(?:\r\n|\r|\n)/g, '<br/>');
        } else if (value.paletteType === Signature.paletteType.checkboxValue) {
          checkboxLabelCondition = value.checkboxLabel;
          sign =
            typeof value.signature === Signature.variableTypes.booleanType
              ? String(value.signature)
              : typeof value.signature === Signature.variableTypes.objectType
                ? value.signature
                : 'false';
          checkBoxLabel = value.checkBoxPaletteLabel;
        }
        let snapAndSignApprove = false;
        if (this.snapAndSignApproval) {
          // Fix for: The approver should be able to submit the document if the approver takes the document which the patient has already signed from the pending section.
          completeSignatureProcess = snapAndSaveApprove = true;
          isSignSubmit = false;
          sign = value.signature.signatureImage || sign;
          if (typeof value.signature === Signature.variableTypes.objectType && value.signature.signatureImage) {
            snapAndSignApprove = true;
          } else {
            snapAndSignApprove = false;
          }
        }
        const horizontalSpaceLeft = value.orginalHorizontalSpaceLeft;
        const verticalSpaceTop = value.orginalVerticalSpaceTop;
        const orginalSignaturePaletteHeight = value.orginalSignaturePaletteHeight;
        const orginalSignaturePaletteWidth = value.orginalSignaturePaletteWidth;
        const baseURL = `${environment.apiServer}${Urls.writableFilePath}`;
        // Check sign type string to fixed document submit issue.
        if (sign && typeof sign === Signature.variableTypes.stringType) {
          if (sign.includes(baseURL)) {
            sign = sign.replace(baseURL, '');
          }
        }
        signatureData = {
          snapAndSignApprove,
          documentId: value.id,
          checkboxLabel: checkboxLabelCondition,
          checkBoxPaletteLabel: checkBoxLabel,
          paletteType: value.paletteType,
          signatureDocumentPageNumber: value.signatureDocumentPageNumber,
          horizontalSpaceLeft,
          signaturePaletteHeight: orginalSignaturePaletteHeight,
          signaturePaletteWidth: orginalSignaturePaletteWidth,
          verticalSpaceTop,
          signature: sign
        };
        if (Number(value.fieldForUser) === Number(this.userData.userId) || Number(value.fieldForUser) === 0) {
          signatureDataDetails.push(signatureData);
        }
      } else {
        if (!value.signature) {
          if (value.pendingApproveUser) {
            approveSignatureProcess = false;
          }
          completeSignatureProcess = false;
        }
      }
    });

    const documentpageSize = this.displayTypeConditions.documentpageSize;
    let params: any = {
      snapAndSaveApprove,
      contentID: this.documentInfo.id,
      pageCount: this.totalDocsCount,
      associateSignatureProcess,
      checkboxImageBehavior: this.displayTypeConditions.checkboxImageBehavior,
      completeSignatureProcess,
      approveSignatureProcess,
      documentOwner: this.documentOwnerId,
      documentName: this.documentInfo.document.displayText,
      signatureData: signatureDataDetails,
      documentpageSize
    };
    if (!isBlank(this.signatureCrossTenant)) {
      params.signatureCrossTenant = this.signatureCrossTenant;
    }
    if (
      !this.siteIdForFolderFetch &&
      this.sharedService.getConfigValue(Config.documentExchangeMode) === Constants.documentExchangeFC
    ) {
      this.fetchSiteIdForFolder();
    }
    this.graphqlService.signDocument(params).subscribe(
      (result) => {
        this.sharedService.isLoading = false;
        const signDocumentRes = result.data.signDocument;
        if (signDocumentRes != null && !isSignSubmit) {
          let owner = parseInt(this.ownerIdCopyFilingCenter);
          if (signDocumentRes && Number(signDocumentRes?.id) > 0) {
            if (
              isPresent(this.folderNameCopyFilingCenter) &&
              signDocumentRes.signatureStatus === Signature.signatureStatus.signatureSignedStatus
            ) {
              let copyToFc = true;
              const admissionId = this.documentInfo?.admissionId;
              this.graphqlService.getExternalSystemsByPatient(owner).valueChanges.subscribe(({ data }) => {
                if (
                  isBlank(data.getSessionTenant) ||
                  isBlank(data.getSessionTenant.externalSystems) ||
                  data.getSessionTenant.externalSystems.length <= 0 ||
                  isBlank(data.getSessionTenant.externalSystems[0].filenameExpression)
                ) {
                  copyToFc = false;
                }
                if (copyToFc) {
                  let patientId = signDocumentRes.associatedP;
                  if (isBlank(patientId) || patientId === 0) {
                    if (isPresent(this.caregiverAssociatePatient)) {
                      patientId = this.caregiverAssociatePatient;
                    } else {
                      patientId = signDocumentRes?.signatureByUsers?.userId;
                    }
                  }
                  let esi = '';
                  this.graphqlService.getExternalSystemsByPatient(parseInt(patientId), admissionId).valueChanges.subscribe(({ data }) => {
                    if (
                      !isBlank(data.getSessionTenant) &&
                      !isBlank(data.getSessionTenant.externalSystems) &&
                      data.getSessionTenant.externalSystems.length > 0
                    ) {
                      const externalSystems = data.getSessionTenant.externalSystems;
                      esi = this.sharedService.getEsiFromExternalSystem(externalSystems[0]);
                    }
                    const iduser = signDocumentRes.id;
                    const docNameDocument = signDocumentRes.document.displayText;
                    const fileUrlDownload = `${environment.apiServer}${Urls.writableFilePath}${this.ownerIdCopyFilingCenter}${Urls.documentSignedpdfPath}${docNameDocument}-${iduser}.${Constants.documentTypes.pdf}`;
                    const patientDataList = {
                      displayName: signDocumentRes.signatureByUsers.displayName
                        ? signDocumentRes.signatureByUsers.displayName
                        : '',
                      firstName: signDocumentRes.signatureByUsers.firstName
                        ? signDocumentRes.signatureByUsers.firstName
                        : '',
                      lastName: signDocumentRes.signatureByUsers.lastName
                        ? signDocumentRes.signatureByUsers.lastName
                        : ''
                    }
                    if (!isBlank(signDocumentRes.document.associatePatient)) {
                      patientDataList.displayName = signDocumentRes.document.associatePatient;
                      patientDataList.firstName = signDocumentRes.document.associatePatientFirstName;
                      patientDataList.lastName = signDocumentRes.document.associatePatientLastName;
                    }
                    const fileNameDetails = {
                      resultDocument: signDocumentRes,
                      esi,
                      typeName: signDocumentRes.type.name,
                      associatePatient: patientDataList
                    };
                    const fileTargetNameUpdated = this.sharedService.setDocFilename(fileNameDetails);
                    if (isBlank(esi)) {
                      copyToFc = false;
                    }
                    if (copyToFc) {
                      const param = {
                        fileUrlDownload,
                        fileTargetNameUpdated,
                        ownerIdCopyFilingCenter: parseInt(this.ownerIdCopyFilingCenter, 10),
                        exchangeData: signDocumentRes.exchangeData,
                        folderNameCopyFilingCenter: this.folderNameCopyFilingCenter,
                        enableMultisite: this.sharedService.isEnableConfig(Config.enableMultiSite),
                        siteId: isPresent(this.siteIdForFolderFetch) ? this.siteIdForFolderFetch : 0
                      };
                      this.graphqlService.copyToFilingCenterAfterSignature(param).subscribe(() => {
                        const copiedSignedSignatureToFilingCenterDes = {
                          displayName: this.userData.displayName
                        };
                        this.sharedService.trackActivity({
                          type: Activity.signatureRequest,
                          name: Activity.copiedSignedSignatureToFilingCenter,
                          des: {
                            data: copiedSignedSignatureToFilingCenterDes,
                            desConstant: Activity.copiedSignedSignatureToFilingCenterDes
                          },
                          linkageId: Activity.signatureRequest
                        });
                      });
                    } else {
                      this.sharedService.trackActivity({
                        type: Activity.signatureRequest,
                        name: Activity.copiedSignedSignatureToFilingCenterError,
                        des: {
                          data: {
                            reason: JSON.stringify(data),
                            displayName: this.userData.displayName,
                            documentId: signDocumentRes.id
                          },
                          desConstant: Activity.copiedSignedSignatureToFilingCenterErrorDes
                        },
                        linkageId: this.documentDisplayText
                      });
                    }
                  });
                } else {
                  this.sharedService.trackActivity({
                    type: Activity.signatureRequest,
                    name: Activity.copiedSignedSignatureToFilingCenterError,
                    des: {
                      data: {
                        reason: JSON.stringify(data),
                        displayName: this.userData.displayName,
                        documentId: signDocumentRes.id
                      },
                      desConstant: Activity.copiedSignedSignatureToFilingCenterErrorDes
                    },
                    linkageId: this.documentDisplayText
                  });
                }
              });
            }
            let approverFlow = false;

            if (
              signDocumentRes.signatureStatus === Signature.signatureStatus.signatureSignedStatus &&
              signDocumentRes.type.allowPendingApproveSignature
            ) {
              approverFlow = true;
            }
            if (!approverFlow) {
              const obtainSignPollingData: any = {};
              obtainSignPollingData.environment = environment.alias;
              obtainSignPollingData.serverBaseUrl = environment.apiServer;
              obtainSignPollingData.apiVersion = ConfigValues.config.apiVersion;
              obtainSignPollingData.tenantId = this.userData.tenantId;
              obtainSignPollingData.tenantName = this.userData.tenantName;
              obtainSignPollingData.previousSignatureStatus = this.documentInfo.signatureStatus;
              const signDocumentNotifyOnSubmitSignatureUsers = parseNumbers(signDocumentRes.notifyOnSubmitSignatureUsers);
              const signDocumentNotifyOnSubmitSignatureRoles = parseNumbers(signDocumentRes.notifyOnSubmitSignatureRoles);
              if (signDocumentNotifyOnSubmitSignatureUsers.length) {
                obtainSignPollingData.notifyOnSubmitSignatureUsers = signDocumentNotifyOnSubmitSignatureUsers;
              } 
              else if (signDocumentNotifyOnSubmitSignatureRoles.length) {
                obtainSignPollingData.notifyOnSubmitSignatureRoles = signDocumentNotifyOnSubmitSignatureRoles;
              }
              obtainSignPollingData.notificationMessageData = signDocumentRes;
              obtainSignPollingData.notificationMessageData.isRead = false;
              obtainSignPollingData.documentId = signDocumentRes.id;
              obtainSignPollingData.signatureStatus = signDocumentRes.signatureStatus;
              obtainSignPollingData.signedOn = signDocumentRes.signedOn;
              obtainSignPollingData.signed = completeSignatureProcess ? true : false;
              if (
                !this.displayTypeConditions.obtainSignature &&
                this.ownerId &&
                signDocumentRes.id &&
                signDocumentRes.signatureStatus
              ) {
                obtainSignPollingData.documentId = signDocumentRes.id;
                obtainSignPollingData.user =
                  signDocumentRes.signatureStatus === Signature.signatureStatus.signatureApprovalStatus &&
                    this.documentInfo.signatureStatus === Signature.signatureStatus.signaturePendingStatus
                    ? signDocumentRes.ownerId
                    : this.documentInfo.signatureStatus === Signature.signatureStatus.signatureApprovalStatus
                      ? signDocumentRes.signatureByUsers && signDocumentRes.signatureByUsers.userId
                        ? signDocumentRes.signatureByUsers.userId
                        : signDocumentRes.ownerId
                      : signDocumentRes.ownerId;
                obtainSignPollingData.signedOn = signDocumentRes.signedOn;
                obtainSignPollingData.signatureStatus = signDocumentRes.signatureStatus;
                this.socketService.emitEvent(Socket.obtainSignPollingToServer, obtainSignPollingData);
              } else {
                if (signDocumentRes.signatureStatus === Signature.signatureStatus.signatureSignedStatus) {
                  if (
                    this.displayTypeConditions.allowRecipientRoles &&
                    this.displayTypeConditions.allowAssociateRoles
                  ) {
                    obtainSignPollingData.documentId = signDocumentRes.id;
                    obtainSignPollingData.user = [this.ownerId, this.associateSignatureByRoles.userId];
                    obtainSignPollingData.signedOn = signDocumentRes.signedOn;
                    obtainSignPollingData.signatureStatus = signDocumentRes.signatureStatus;
                  } else if (
                    this.displayTypeConditions.allowRecipientRoles &&
                    !this.displayTypeConditions.allowAssociateRoles
                  ) {
                    obtainSignPollingData.documentId = signDocumentRes.id;
                    obtainSignPollingData.user =
                      signDocumentRes.signatureStatus === Signature.signatureStatus.signatureApprovalStatus
                        ? signDocumentRes.signatureByUsers && signDocumentRes.signatureByUsers.userId
                          ? signDocumentRes.signatureByUsers.userId
                          : signDocumentRes.ownerId
                        : signDocumentRes.ownerId;
                    obtainSignPollingData.signedOn = signDocumentRes.signedOn;
                    obtainSignPollingData.signatureStatus = signDocumentRes.signatureStatus;
                  }
                } else {
                  obtainSignPollingData.notificationMessageData = signDocumentRes;
                  obtainSignPollingData.user = signDocumentRes.signatureByUsers.userId;
                }
                this.socketService.emitEvent(Socket.obtainSignPollingToServer, obtainSignPollingData);
              }
            }
            const sentSignedDocDes = {
              displayName: this.userData.displayName
            };
            this.sharedService.trackActivity({
              type: Activity.signatureRequest,
              name: Activity.signedSignatureRequest,
              des: {
                data: sentSignedDocDes,
                desConstant: Activity.sentSignedDocDes
              },
              linkageId: Activity.signatureRequest
            });
            if (!this.sharedService.isAppLessHomeLoggedIn()) {
              if (completeSignatureProcess) {
                this.commonService.showMessage(
                  this.commonService.getTranslateData('SUCCESS_MESSAGES.SUBMITTED_SIGNED_DOCUMENT')
                );
                this.sharedService.trackActivity({
                  type: Activity.signatureRequest,
                  name: Activity.confirmSubmit,
                  des: {
                    data: {
                      displayName: this.sharedService.userData.displayName,
                      actualFileName: this.documentInfo.type.name
                    },
                    desConstant: Activity.representiveConfirm
                  },
                  linkageId: this.documentInfo.type.name
                });
              } else {
                this.commonService.showMessage(
                  this.commonService.getTranslateData('SUCCESS_MESSAGES.SENT_DOCUMENT_TO_RECIPIENT')
                );
              }
            }
            if (this.sharedService.isAppLessHomeLoggedIn()) {
              this.sharedService.appLessHomeNext.next({ type: 'document', id: this.documentId, message: this.commonService.getTranslateData(
                'SUCCESS_MESSAGES.APPLESS_DOCUMENT_SUBMIT_SUCCESS'),
                status: ''
              });
              this.commonService.redirectToPage(`.${PageRoutes.appLess}/home`);
            } else if (
              this.sharedService.isEnableConfig(Config.signatureServerside) &&
              !this.applessDocumentWorkflow &&
              isBlank(this.sharedService.automaticLinkedItems)
            ) {
              this.commonService.redirectToPage('/document-center/pending-documents');
            } else if (this.applessDocumentWorkflow) {
              this.applessDocumentWorkFlowComplete = true;
              this.applessDocumentWorkFlowCompletedMessage = this.commonService.getTranslateData(
                'SUCCESS_MESSAGES.APPLESS_DOCUMENT_SUBMIT_SUCCESS'
              );
              this.graphqlService.expireToken().subscribe((status) => {
                if (status) {
                  //success
                }
              });
              this.commonService.showMessage(this.applessDocumentWorkFlowCompletedMessage);
            } else if (!isBlank(this.sharedService.automaticLinkedItems)) {
              this.consoloWorkFlow = true;
              this.applessDocumentWorkFlowCompletedMessage = this.commonService.getTranslateData(
                'SUCCESS_MESSAGES.CONSOLO_WORKFLOW_SUCCESS'
              );
              this.sharedService.consoloAppExit();
            }
          } else {
            const signatureRequestFailureDes = {
              displayName: this.userData.displayName
            };
            this.sharedService.trackActivity({
              type: Activity.signatureRequest,
              name: Activity.signatureRequestFailure,
              des: {
                data: signatureRequestFailureDes,
                desConstant: Activity.signatureRequestFailureDes
              },
              linkageId: Activity.signatureRequest
            });
          }
        } else if (isSignSubmit) {
          this.sharedService.trackActivity({
            type: Activity.signatureRequest,
            name: Activity.finishSigning,
            des: {
              data: { documentName: this.documentInfo.document.displayText },
              desConstant: Activity.finishSigningDes
            },
            linkageId: this.documentInfo.document.displayText
          });
          if (isBlank(this.sharedService.automaticLinkedItems)) {
            this.commonService.showMessage(
              this.commonService.getTranslateData(ConfigValues.messages.confirmSignatureRequestPatient)
            );
          }
          this.initiateResign = false;
          this.finishSign = true;
          this.paletteStatus = this.paletteStatuses.confirmed;
          this.represntativeConfirmSignature = true;
          this.reSignDocument(false, true);
        } else {
          if (
            result.errors[0].message == this.commonService.getTranslateData('ERROR_MESSAGES.DOCUMENT_ALREADY_SUBMIT')
          ) {
            this.applessDocumentWorkFlowComplete = true;
            this.applessDocumentWorkFlowCompletedMessage = this.commonService.getTranslateData(
              'SUCCESS_MESSAGES.APPLESS_DOCUMENT_SUBMIT_SUCCESS'
            );
            this.commonService.showMessage(
              this.commonService.getTranslateData('SUCCESS_MESSAGES.APPLESS_DOCUMENT_SUBMIT_SUCCESS')
            );
            const sentSignedDocDes = {
              displayName: this.userData.displayName
            };
            const activityName = this.applessDocumentWorkflow ? Activity.applessFlow : '';
            this.sharedService.trackActivity({
              type: Activity.signatureRequest,
              name: `${Activity.signatureRequestFailure}${activityName}`,
              des: {
                data: sentSignedDocDes,
                desConstant: Activity.applessFailedSubmit
              },
              linkageId: Activity.signatureRequest
            });
          } else if (
            result.errors[0].message == this.commonService.getTranslateData('ERROR_MESSAGES.DOCUMENT_ALREADY_CANCELED')
          ) {
            this.applessDocumentWorkFlowComplete = true;
            this.applessDocumentWorkFlowCompletedMessage = this.commonService.getTranslateData(
              'ERROR_MESSAGES.APPLESS_CANCELED_DOCUMENT'
            );
            this.commonService.showMessage(
              this.commonService.getTranslateData('ERROR_MESSAGES.APPLESS_CANCELED_DOCUMENT')
            );
            const sentSignedDocDes = {
              displayName: this.userData.displayName
            };
            this.sharedService.trackActivity({
              type: Activity.signatureRequest,
              name: Activity.signatureRequestFailure + this.applessDocumentWorkflow ? Activity.applessFlow : '',
              des: {
                data: sentSignedDocDes,
                desConstant: Activity.applessCanceldDocumentSubmit
              },
              linkageId: Activity.signatureRequest
            });
          }
        }
      },
      () => {
        this.sharedService.isLoading = false;
      }
    );
  }
  startSigningProcess(fromButtonClick: any = false): void {
    this.validatePalette();
    setTimeout(() => {
      this.findNext(fromButtonClick);
    }, 200);
  }
  ngOnDestroy(): void  {
    if (!isBlank(this.dummyDroppedObjects)) {
      this.dummyDroppedObjects.forEach((element) => {
        element.focus = false;
      });
      this.dummyDroppedObjects = [];
    }
    this.documentInfo = undefined;
    this.signatureDocumentDetails = undefined;
    if (this.isMobilePlatform) {
      window.screen.orientation.unlock();
    }
  }
  reSignDocument(skipValidate = false, resetSign = false): void {
    const allowPendingApproveSignature = this.signatureTypeTagData
      ? this.signatureTypeTagData.allowPendingApproveSignature
      : this.displayTypeConditions && this.displayTypeConditions.allowPendingApproveSignature
        ? this.displayTypeConditions.allowPendingApproveSignature
        : false;

    const obtainSignature = this.signatureTypeTagData
      ? this.signatureTypeTagData.obtainSignature
      : this.displayTypeConditions && this.displayTypeConditions.obtainSignature
        ? this.displayTypeConditions.obtainSignature
        : false;
    let needApproverSign = false;
    let enableSaveSignFlow = true;
    if (!skipValidate && !resetSign) {
      this.represntativeConfirmSignature = false;
      this.paletteStatus = this.paletteStatuses.signing;
      enableSaveSignFlow = false;
    } else {
      enableSaveSignFlow = allowPendingApproveSignature && obtainSignature;
    }
    sessionStorage.setItem('enableSaveSignFlow', JSON.stringify(enableSaveSignFlow));
    this.dummyDroppedObjects.forEach((value) => {
      const userCheck = Number(value.fieldForUser) === 0;
      value.setCursor = Signature.setCursor.notAllowed;
      value.border = Signature.backgroundColor.signedBorder;
      value.boxShadow = Signature.backgroundColor.signedBoxShadow;
      if (!this.represntativeConfirmSignature) {
        value.mySignNeed = false;
        value.signaturePaletteLock = Signature.paletteLock;
      }
      if (userCheck && !this.represntativeConfirmSignature) {
        value.signaturePaletteLock = Signature.paletteLockZero;
        value.mySignNeed = true;
      }

      if (
        this.represntativeConfirmSignature &&
        !skipValidate &&
        Number(value.fieldForUser) === Number(this.userData.userId) &&
        value.pendingApproveUser &&
        allowPendingApproveSignature
      ) {
        value.signaturePaletteLock = Signature.paletteLockZero;
        value.mySignNeed = true;
        this.approverSignPending = true;
        if (value.mandatory) {
          value.boxShadow = Signature.backgroundColor.boxShadow;
          value.border = Signature.backgroundColor.toBeSignedBorder;
        }
        value.setCursor = Signature.setCursor.pointer;
        if (!value.signature && !value.view) {
          needApproverSign = true;
        }
      } else if (
        !skipValidate &&
        this.paletteStatus === this.paletteStatuses.confirmed &&
        !value.pendingApproveUser &&
        this.represntativeConfirmSignature
      ) {
        value.mySignNeed = false;
      }
    });
    if (!needApproverSign && this.paletteStatus == this.paletteStatuses.confirmed) {
      this.paletteStatus = this.paletteStatuses.signed;
    }
    if (skipValidate || (!needApproverSign && obtainSignature)) {
      this.validatePalette();
    } else {
      this.paletteStatus = !allowPendingApproveSignature ? this.paletteStatuses.signing : this.paletteStatus;
      this.isSigned = false;
      this.startSign = true;
      this.finishSign = false;
    }
    if (!resetSign) {
      this.initiateResign = true;
    }
  }

  fetchSiteIdForFolder(siteid?: number): void {
    const siteId = siteid
      ? siteid
      : isPresent(this.documentRootInfo?.siteId)
        ? this.documentRootInfo.siteId
        : isPresent(this.documentInfo?.siteId)
          ? this.documentInfo.siteId
          : '';
    const typeId = isPresent(this.documentRootInfo?.type?.id)
      ? this.documentRootInfo.type.id
      : isPresent(this.documentInfo?.type?.id)
        ? this.documentInfo.type.id
        : '';
    this.siteIdForFolderFetch = isPresent(siteId) && typeof Number(siteId) === 'number' ? Number(siteId) : 0;
    if (isPresent(siteId) && isPresent(typeId) && isBlank(this.folderNameCopyFilingCenter)) {
      this.fetchSites(typeId);
    } else if (
      (isPresent(this.sharedService.userData.mySites) && this.sharedService.userData.mySites.length === 1) ||
      isPresent(this.sharedService.userData.siteid)
    ) {
      const mySite = this.sharedService.userData.siteid || this.sharedService.userData?.mySites[0].id;
      this.siteIdForFolderFetch = typeof Number(mySite) === 'number' ? Number(mySite) : 0;
      if (mySite !== 0 && isBlank(this.folderNameCopyFilingCenter)) {
        this.fetchSites(typeId);
      } else {
        this.fetchSiteListbyUserId();
      }
    } else {
      this.fetchSiteListbyUserId();
    }
  }
  fetchSites(typeId): void {
    // associated_user coming in appless for alternate/ caregiver flow
    const patientId =
      isPresent(this.sharedService.userData?.associated_user) &&
        this.sharedService.userData?.associated_user[0]?.userid &&
        typeof Number(this.sharedService.userData?.associated_user[0]?.userid) === 'number'
        ? Number(this.sharedService.userData?.associated_user[0]?.userid)
        : undefined;
    if (isPresent(typeId)) {
      this.graphqlService.fetchSites(this.siteIdForFolderFetch, typeId, patientId, this.documentInfo.admissionId).then((data) => {
        if (isPresent(data)) {
          this.folderNameCopyFilingCenter = data;
        }
      });
    }
  }
  fetchSiteListbyUserId(): void {
    if (
      isPresent(this.sharedService.userData?.crossTenantId) &&
      isPresent(this.sharedService.userData?.associated_user) &&
      this.sharedService.userData?.associated_user[0]?.userid
    ) {
      this.graphqlService
        .siteListByUserId(
          Number(this.sharedService.userData.crossTenantId),
          Number(this.sharedService.userData.associated_user[0]?.userid)
        )
        ?.subscribe((response: SiteListByUserIdResponse) => {
          if (isPresent(response?.data?.siteListByUserId?.data[0].id)) {
            const site = response.data.siteListByUserId.data[0];
            this.sharedService.userData.siteid = String(site.id);
            if (isBlank(this.sharedService.userData.mySites)) {
              this.sharedService.userData.mySites = [site];
            }
            this.siteIdForFolderFetch = site.id;
            if (this.sharedService.getConfigValue(Config.documentExchangeMode) === Constants.documentExchangeFC) {
              this.fetchSiteIdForFolder();
            }
          }
        });
    }
  }
  get isPlaceHolderImage(): boolean {
    return this.currentImagePath.includes('placeholder-image.jpg');
  }

  checkStartSignWithTextPad(): void {
    if (!this.startSign) {
      this.startSign = true;
    }
  }

  async openSignPadWithCondition(signData: any, obtainSignature): Promise<any> {
    const enableSaveSignFlow = sessionStorage.getItem('enableSaveSignFlow') ? JSON.parse(sessionStorage.getItem('enableSaveSignFlow')) : null;
    const buttons = [
      { text: this.commonService.getTranslateData('BUTTONS.NO'), confirm: false },
      { text: this.commonService.getTranslateData('BUTTONS.YES'), confirm: true }
    ];
    if (this.sharedService.platform.is('capacitor') && !this.isiPad) {
      this.sharedService.lockDevice(Constants.deviceOrientation.landscapePrimary);
    }
    const modal = await this.modalController.create({
      component: SignatureComponent
    });
    modal.onDidDismiss().then(({ data }) => {
      if (this.sharedService.platform.is('capacitor') && !this.isiPad) {
        this.sharedService.unlockDevice();
        this.lockDeviceOrientation();
      }

      if (data) {
        if (!isBlank(data.imageData)) {
          this.checkStartSignWithTextPad();
          if (!isBlank(this.dummyDroppedObjects)) {
            this.addObjects = this.dummyDroppedObjects;
          }
          const index = this.addObjects.indexOf(signData);
          if (index >= 0) {
            // CHP-14958 auto signature true but not added signature in profile page
            if (
              this.enableSaveSignatureToProfile &&
              isBlank(this.sharedService.userData.savedUserSignature) &&
              this.confirmSaveSignToProfile &&
              enableSaveSignFlow
            ) {
              this.commonService
                .showAlert({
                  message: this.commonService.getTranslateData('MESSAGES.WOULD_YOU_SAVE_SIGNATURE'),
                  header: this.commonService.getTranslateData('LABELS.SAVE_SIGNATURE'),
                  buttons,
                  backDrop: false,
                  cssClass: 'common-alert document-auto-sign'
                })
                .then((confirmation) => {
                  this.combineCanvas(data.imageData, (sign) => {
                    let savedUserSignature = '';
                    this.confirmSaveSignToProfile = confirmation;
                    if (confirmation) {
                      // CHP-14958 save this signature to profile
                      savedUserSignature = data.imageData.startsWith(Signature.signReplaceValue) ? data.imageData.replace(Signature.signReplaceValue, '') : data.imageData;
                    }
                    this.saveSignatureToProfile(savedUserSignature, undefined);
                    this.setSingleSign(index, sign);
                  });
                });
            } else {
              this.combineCanvas(data.imageData, (sign) => {
                this.setSingleSign(index, sign);
              });
            }
          }
          this.sharedService.trackActivity({
            type: Activity.signatureRequest,
            name: Activity.signatureSave,
            des: {
              data: {
                displayName: this.userData.displayName,
                pageNo: this.currentDocPage,
                documentName: this.documentDisplayText
              },
              desConstant: Activity.signatureSaveDes
            },
            linkageId: this.documentDisplayText
          });
        }
      }
    });
    await modal.present();
  }

  setSingleSign(index: number, sign: string): void {
    setTimeout(() => {
      this.addObjects[index].signature = sign;
      this.addObjects[index].view = true;
      this.addObjects[index].border = !isBlank(this.addObjects[index].signature)
        ? Signature.backgroundColor.signedBorder
        : '';
      this.addObjects[index].boxShadow = !isBlank(this.addObjects[index].signature)
        ? Signature.backgroundColor.signedBoxShadow
        : Signature.backgroundColor.boxShadow;
      if (!isBlank(this.dummyDroppedObjects) && this.dummyDroppedObjects.length !== 0) {
        this.dummyDroppedObjects = this.addObjects;
        this.addObjects = [];
        this.validatePalette();
      }
    }, 100);
  }
  enableSaveSignWorkFlow(documentDetails) {
    const docDetails = documentDetails;
    let enableSaveSignFlow = true;
    if (this.isUploadDocView) {
      if (this.signatureTypeTagData.obtainSignature && this.signatureTypeTagData.allowAssociatePatient) {
        enableSaveSignFlow =
          (this.signatureTypeTagData.allowPersonalSignature && docDetails.signatureStatus === Signature.signatureStatus.signatureDraftStatus) ||
          this.signatureTypeTagData.allowAssociateRoles;
      }
    } else if (this.displayTypeConditions.obtainSignature) {
      enableSaveSignFlow =
        this.displayTypeConditions.allowAssociateRoles ||
        (this.displayTypeConditions.allowPendingApproveSignature && this.snapAndSignApproval) ||
        (this.displayTypeConditions.allowPersonalSignature && docDetails.signatureStatus === Signature.signatureStatus.signatureApprovalStatus);
    } else if (this.displayTypeConditions.allowAssociateRoles) {
      enableSaveSignFlow = !(docDetails.signatureStatus === Signature.signatureStatus.signaturePendingStatus);
    }
    sessionStorage.setItem('enableSaveSignFlow', JSON.stringify(enableSaveSignFlow));
  }
  saveSignatureToProfile(sign: string, useSavedSign?): void {
    const signatureDetails: any = {
      useSavedSign,
      encodedUserSignature: sign,
      removeIsCaregiverKey: true
    };
    this.sharedService.isLoading = true;
    this.graphqlService.updateUser(signatureDetails)?.subscribe({
      next: (response: any) => {
        this.sharedService.isLoading = false;
        if (isPresent(response.data) && isPresent(response.data.updateUser)) {
          if (!isBlank(sign)) {
            this.sharedService.userData.savedUserSignature = sign;
          } else if (useSavedSign === undefined && isBlank(sign)) {
            this.sharedService.userData.savedUserSignature = ''
          }
        }
      }, error: () => {
        this.sharedService.isLoading = false;
        this.commonService.showToast({
          message: this.commonService.getTranslateData('ERROR_MESSAGES.SOMETHING_WENT_WRONG'),
          color: 'danger'
        });
      }
    });
  }

  showLoadSignature(signCombineData, index: number, signData, obtainSignature): void {
    const buttons = [
      { text: this.commonService.getTranslateData('BUTTONS.NO'), confirm: false },
      { text: this.commonService.getTranslateData('BUTTONS.YES'), confirm: true }
    ];
    // CHP-14958 Auto signature true and added signature in profile page
    const message = this.commonService.getTranslateData('MESSAGES.DO_YOU_WANT_USE_SAVED_SIGNATURE');
    this.commonService
      .showAlert({
        message,
        header: this.commonService.getTranslateData('LABELS.LOAD_SIGNATURE'),
        buttons,
        backDrop: false,
        cssClass: 'common-alert document-auto-sign'
      })
      .then((confirmation) => {
        this.confirmSaveSignToProfile = false;
        this.sharedService.userData.useSavedSign = confirmation;
        if (confirmation) {
          this.checkStartSignWithTextPad();
          this.combineCanvas(signCombineData, (sign) => {
            this.setSingleSign(index, sign);
          });
        } else {
          this.openSignPadWithCondition(signData, obtainSignature);
        }
        this.saveSignatureToProfile('', confirmation);
      });
  }
}
