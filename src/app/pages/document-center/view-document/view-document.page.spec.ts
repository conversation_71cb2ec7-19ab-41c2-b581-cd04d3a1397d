import { QueryURIPipe } from './../../../pipes/query-uri/query-uri.pipe';
import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { ReactiveFormsModule } from '@angular/forms';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { TranslateModule } from '@ngx-translate/core';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { Apollo } from 'apollo-angular';
import { RouterModule } from '@angular/router';
import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { ActionSheetController, IonicModule, ModalController, PopoverController } from '@ionic/angular';
import { ViewDocumentPage } from './view-document.page';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { of } from 'rxjs';
import { CommonService } from 'src/app/services/common-service/common.service';
import { TestConstants } from 'src/app/constants/test-constants';
import { Signature } from 'src/app/constants/signature';

describe('ViewDocumentPage', () => {
  let component: ViewDocumentPage;
  let fixture: ComponentFixture<ViewDocumentPage>;
  let graphqlService: GraphqlService;
  let sharedService: SharedService;
  let commonService: CommonService;
  const modalSpy = TestConstants.modalSpy;
  let modalController: ModalController;
  const actionSheetSpy = TestConstants.actionSheetSpy;
  let popoverController: PopoverController;
  const popupSpy = TestConstants.popoverSpy;
  let actionSheetController: ActionSheetController;
  let sharedServiceMock: jasmine.SpyObj<SharedService>;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [ViewDocumentPage, QueryURIPipe],
      imports: [
        IonicModule.forRoot(),
        TranslateModule.forRoot(),
        RouterModule.forRoot([]),
        HttpClientModule,
        HttpClientTestingModule,
        ReactiveFormsModule
      ],
      providers: [
        CommonService,
        ModalController,
        ActionSheetController,
        GraphqlService,
        Apollo,
        NgxPermissionsService,
        NgxPermissionsStore,
        SharedService,
        Idle,
        IdleExpiry,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    actionSheetController = TestBed.inject(ActionSheetController);
    spyOn(actionSheetController, 'create').and.callFake(() => {
      return actionSheetSpy;
    });
    actionSheetSpy.present.and.stub();
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    popoverController = TestBed.inject(PopoverController);
    spyOn(popoverController, 'create').and.callFake(() => {
      return popupSpy;
    });
    popupSpy.present.and.stub();
    // spyOn(popoverController, 'dismiss').and.stub();
    modalSpy.onDidDismiss.and.resolveTo({ data: { imageData: 'fakeData' } });
    sharedService = TestBed.inject(SharedService);
    commonService = TestBed.inject(CommonService);
    spyOn(commonService, 'redirectToPage').and.stub();
    spyOn(commonService, 'showMessage').and.stub();
    Object.defineProperty(sharedService, 'userData', { value: TestConstants.userData });
    graphqlService = TestBed.inject(GraphqlService);
    fixture = TestBed.createComponent(ViewDocumentPage);
    sharedServiceMock = TestBed.inject(SharedService) as jasmine.SpyObj<SharedService>;

    component = fixture.componentInstance;
    fixture.detectChanges();
  });
  // used to hide dom element from screen
  afterEach(() => {
    if (fixture.nativeElement && 'remove' in fixture.nativeElement) {
      (fixture.nativeElement as HTMLElement).remove();
    }
  });
  it('should create', () => {
    expect(component).toBeTruthy();
  });
  it('execute submitDocument ', () => {
    component.documentInfo = { associateSignatureProcess: true, document: { displayText: '' } };
    component.dummyDroppedObjects = [
      { signaturePaletteLock: 1, signature: 'asdd', associateUser: 'fg', paletteType: 'Sign' }
    ];
    component.displayTypeConditions = { documentpageSize: '' };
    const signDocument = of({
      data: {
        signDocument: {
          displayText: {
            text: 's-fgd',
            document: 'gd'
          },
          document: {
            displayText: {
              text: 's-fgd',
              document: 'gd'
            }
          },
          associatedP: 0,
          signatureByUsers: { userId: 2 },
          type: { name: '', fileSavingFormatText: '' },
          notifyOnSubmitSignatureUsers: JSON.stringify({}),
          notifyOnSubmitSignatureRoles: JSON.stringify({})
        }
      }
    });
    component.folderNameCopyFilingCenter = 'path';
    spyOn(graphqlService, 'signDocument').and.returnValue(signDocument);
    spyOn(graphqlService, 'copyToFilingCenterAfterSignature').and.returnValue(signDocument);
    const getExternalSystemsByPatient = of({ data: { getSessionTenant: { externalSystems: 'fd' } } });
    spyOn(graphqlService, 'getExternalSystemsByPatient').and.returnValue({
      valueChanges: getExternalSystemsByPatient
    });
    component.submitDocument();
    expect(component.submitDocument).toBeTruthy();
  });
  it('execute reSignDocument', () => {
    component.dummyDroppedObjects = [{ fieldForUser: 0, signature: undefined, mandatory: true }];
    component.reSignDocument();
    expect(component.reSignDocument).toBeTruthy();
  });
  it('execute presentResignActionSheet', fakeAsync(() => {
    component.dummyDroppedObjects = [{ fieldForUser: 0, signature: undefined, mandatory: true }];
    component.presentResignActionSheet();
    expect(component.presentResignActionSheet).toBeTruthy();
  }));
  it('execute validatePalette', fakeAsync(() => {
    component.dummyDroppedObjects = [{ fieldForUser: 0, signature: 'fds', mandatory: true, mySignNeed: true }];
    component.displayTypeConditions = { allowAssociateRoles: true };
    component.associateSignaturesByUsersId = 1;
    component.documentOwnerId = '3';
    component.finishSign = true;
    component.initiateResign = false;
    component.paletteStatus = 'SIGNING';
    component.snapAndSignApproval = true;
    component.snapSignPendingApproveUser = false;
    sharedService.consoloLoader = false;
    component.validatePalette();
    expect(component.validatePalette).toBeTruthy();
  }));
  it('execute validatePalette: signing needed', fakeAsync(() => {
    component.dummyDroppedObjects = [{ fieldForUser: 0, signature: '', mandatory: true, mySignNeed: true }];
    component.displayTypeConditions = { allowAssociateRoles: true };
    component.associateSignaturesByUsersId = 1;
    component.documentOwnerId = '3';
    component.validatePalette();
    expect(component.validatePalette).toBeTruthy();
  }));

  it('execute validatePalette: optional pre-populated field should not block completion', fakeAsync(() => {
    // Test case for CHP-20354: Optional pre-populated fields should not prevent FINISH button
    component.dummyDroppedObjects = [
      {
        fieldForUser: 0,
        signature: 'pre-populated-value',
        mandatory: false,
        mySignNeed: true,
        view: false,
        isDisableForPatient: true,
        paletteType: Signature.paletteType.checkboxValue
      }
    ];
    component.displayTypeConditions = {
      allowAssociateRoles: false,
      editablePrePopulatedFields: false
    };
    component.userData = { userId: 0 };
    component.validatePalette();

    // Should allow finish since optional pre-populated field should not block completion
    expect(component.finishSign).toBe(true);
    expect(component.validatePalette).toBeTruthy();
  }));

  it('execute validatePalette: mandatory pre-populated field should not block completion', fakeAsync(() => {
    // Test case for CHP-20354: Mandatory auto-fill fields should not prevent FINISH button
    component.dummyDroppedObjects = [
      {
        fieldForUser: 0,
        signature: 'John Doe', // Auto-fill patient name
        mandatory: true,
        mySignNeed: true,
        view: false,
        isDisableForPatient: true,
        paletteType: Signature.paletteType.textValue
      }
    ];
    component.displayTypeConditions = {
      allowAssociateRoles: false,
      editablePrePopulatedFields: false
    };
    component.userData = { userId: 0 };
    component.validatePalette();

    // Should allow finish since mandatory pre-populated field should not block completion
    expect(component.finishSign).toBe(true);
    expect(component.validatePalette).toBeTruthy();
  }));

  it('execute validatePalette: mandatory field should still block completion', fakeAsync(() => {
    // Test case to ensure mandatory fields still work correctly
    component.dummyDroppedObjects = [
      {
        fieldForUser: 0,
        signature: '',
        mandatory: true,
        mySignNeed: true,
        view: false,
        isDisableForPatient: false,
        paletteType: Signature.paletteType.checkboxValue
      }
    ];
    component.displayTypeConditions = {
      allowAssociateRoles: false,
      editablePrePopulatedFields: false
    };
    component.userData = { userId: 0 };
    component.validatePalette();

    // Should not allow finish since mandatory field is not completed
    expect(component.finishSign).toBe(false);
    expect(component.validatePalette).toBeTruthy();
  }));

  it('execute autoFilterItem: pre-populated field should not be in navigation', fakeAsync(() => {
    // Test case for CHP-20354: Pre-populated fields should not be in auto navigation
    component.dummyDroppedObjects = [
      {
        signature: 'pre-populated-value',
        mandatory: false,
        mySignNeed: true,
        view: false,
        isDisableForPatient: true,
        paletteType: Signature.paletteType.checkboxValue
      }
    ];
    component.displayTypeConditions = {
      editablePrePopulatedFields: false
    };
    component.autoFilterItem();

    // Should not include pre-populated field in navigation
    expect(component.autoNavigateItem.length).toBe(0);
    expect(component.autoFilterItem).toBeTruthy();
  }));

  it('execute autoFilterItem: mandatory pre-populated field should not be in navigation', fakeAsync(() => {
    // Test case for CHP-20354: Mandatory auto-fill fields should not be in auto navigation
    component.dummyDroppedObjects = [
      {
        signature: 'John Doe', // Auto-fill patient name
        mandatory: true,
        mySignNeed: true,
        view: false,
        isDisableForPatient: true,
        paletteType: Signature.paletteType.textValue
      }
    ];
    component.displayTypeConditions = {
      editablePrePopulatedFields: false
    };
    component.autoFilterItem();

    // Should not include mandatory pre-populated field in navigation
    expect(component.autoNavigateItem.length).toBe(0);
    expect(component.autoFilterItem).toBeTruthy();
  }));

  it('execute autoFilterItem: mandatory field should be in navigation', fakeAsync(() => {
    // Test case to ensure mandatory fields are still included in navigation
    component.dummyDroppedObjects = [
      {
        signature: '',
        mandatory: true,
        mySignNeed: true,
        view: false,
        isDisableForPatient: false,
        paletteType: Signature.paletteType.checkboxValue
      }
    ];
    component.displayTypeConditions = {
      editablePrePopulatedFields: false
    };
    component.autoFilterItem();

    // Should include mandatory field in navigation
    expect(component.autoNavigateItem.length).toBe(1);
    expect(component.autoFilterItem).toBeTruthy();
  }));

  it('execute autoFilterItem: field with signature but editable should be in navigation', fakeAsync(() => {
    // Test case for field with signature but not disabled for patient
    component.dummyDroppedObjects = [
      {
        signature: 'value',
        mandatory: false,
        mySignNeed: true,
        view: false,
        isDisableForPatient: false, // Not disabled for patient
        paletteType: Signature.paletteType.checkboxValue
      }
    ];
    component.displayTypeConditions = {
      editablePrePopulatedFields: false
    };
    component.autoFilterItem();

    // Should include field in navigation since it's editable
    expect(component.autoNavigateItem.length).toBe(1);
    expect(component.autoFilterItem).toBeTruthy();
  }));

  it('execute autoFilterItem: unsupported field type should be in navigation', fakeAsync(() => {
    // Test case for field with unsupported palette type
    component.dummyDroppedObjects = [
      {
        signature: 'signature-data',
        mandatory: false,
        mySignNeed: true,
        view: false,
        isDisableForPatient: true,
        paletteType: Signature.paletteType.signValue // Unsupported type
      }
    ];
    component.displayTypeConditions = {
      editablePrePopulatedFields: false
    };
    component.autoFilterItem();

    // Should include field in navigation since it's unsupported type
    expect(component.autoNavigateItem.length).toBe(1);
    expect(component.autoFilterItem).toBeTruthy();
  }));

  // Comprehensive regression tests to ensure existing functionality is preserved
  it('execute validatePalette: existing behavior for normal mandatory fields', fakeAsync(() => {
    component.dummyDroppedObjects = [
      {
        fieldForUser: 0,
        signature: 'signed',
        mandatory: true,
        mySignNeed: true,
        view: true,
        isDisableForPatient: false,
        paletteType: Signature.paletteType.signValue
      }
    ];
    component.displayTypeConditions = {
      allowAssociateRoles: false,
      editablePrePopulatedFields: true // Normal case
    };
    component.userData = { userId: 0 };
    component.validatePalette();

    // Should allow finish for completed mandatory field
    expect(component.finishSign).toBe(true);
  }));

  it('execute validatePalette: existing behavior for incomplete mandatory fields', fakeAsync(() => {
    component.dummyDroppedObjects = [
      {
        fieldForUser: 0,
        signature: '',
        mandatory: true,
        mySignNeed: true,
        view: false,
        isDisableForPatient: false,
        paletteType: Signature.paletteType.signValue
      }
    ];
    component.displayTypeConditions = {
      allowAssociateRoles: false,
      editablePrePopulatedFields: true
    };
    component.userData = { userId: 0 };
    component.validatePalette();

    // Should not allow finish for incomplete mandatory field
    expect(component.finishSign).toBe(false);
  }));

  it('execute validatePalette: existing behavior for optional viewed fields', fakeAsync(() => {
    component.dummyDroppedObjects = [
      {
        fieldForUser: 0,
        signature: '',
        mandatory: false,
        mySignNeed: true,
        view: true, // User has viewed/interacted with the field
        isDisableForPatient: false,
        paletteType: Signature.paletteType.checkboxValue
      }
    ];
    component.displayTypeConditions = {
      allowAssociateRoles: false,
      editablePrePopulatedFields: true
    };
    component.userData = { userId: 0 };
    component.validatePalette();

    // Should allow finish for viewed optional field (existing behavior)
    expect(component.finishSign).toBe(true);
  }));

  it('execute validatePalette: existing behavior when editablePrePopulatedFields is true', fakeAsync(() => {
    // When editablePrePopulatedFields is true, our new logic should NOT apply
    component.dummyDroppedObjects = [
      {
        fieldForUser: 0,
        signature: 'pre-populated-value',
        mandatory: false,
        mySignNeed: true,
        view: false,
        isDisableForPatient: true,
        paletteType: Signature.paletteType.checkboxValue
      }
    ];
    component.displayTypeConditions = {
      allowAssociateRoles: false,
      editablePrePopulatedFields: true // This should disable our new logic
    };
    component.userData = { userId: 0 };
    component.validatePalette();

    // Should NOT allow finish because editablePrePopulatedFields=true means normal validation applies
    expect(component.finishSign).toBe(false);
  }));

  it('execute validatePalette: field with signature but editable should block completion', fakeAsync(() => {
    // Test case for field with signature but not disabled for patient
    component.dummyDroppedObjects = [
      {
        fieldForUser: 0,
        signature: 'value',
        mandatory: false,
        mySignNeed: true,
        view: false,
        isDisableForPatient: false, // Not disabled for patient
        paletteType: Signature.paletteType.checkboxValue
      }
    ];
    component.displayTypeConditions = {
      allowAssociateRoles: false,
      editablePrePopulatedFields: false
    };
    component.userData = { userId: 0 };
    component.validatePalette();

    // Should not allow finish since field is editable and not viewed
    expect(component.finishSign).toBe(false);
  }));

  it('execute validatePalette: unsupported field type should block completion', fakeAsync(() => {
    // Test case for field with unsupported palette type
    component.dummyDroppedObjects = [
      {
        fieldForUser: 0,
        signature: 'signature-data',
        mandatory: false,
        mySignNeed: true,
        view: false,
        isDisableForPatient: true,
        paletteType: Signature.paletteType.signValue // Unsupported type
      }
    ];
    component.displayTypeConditions = {
      allowAssociateRoles: false,
      editablePrePopulatedFields: false
    };
    component.userData = { userId: 0 };
    component.validatePalette();

    // Should not allow finish since field type is not supported by our skip logic
    expect(component.finishSign).toBe(false);
  }));
  it('execute submitDocument: not completing process', () => {
    component.documentInfo = { associateSignatureProcess: true, document: { displayText: '' } };
    component.dummyDroppedObjects = [
      { signaturePaletteLock: 1, signature: 'asdd', associateUser: 'fg', paletteType: 'Sign' }
    ];
    component.displayTypeConditions = { documentpageSize: '' };
    const signDocument = of({
      data: {
        signDocument: {
          displayText: {
            text: 's-fgd',
            document: 'gd'
          },
          document: {
            displayText: {
              text: 's-fgd',
              document: 'gd'
            }
          },
          associatedP: 0,
          signatureByUsers: { userId: 2 },
          type: { name: '', fileSavingFormatText: '' },
          notifyOnSubmitSignatureUsers: JSON.stringify({}),
          notifyOnSubmitSignatureRoles: JSON.stringify({})
        }
      }
    });
    spyOn(graphqlService, 'signDocument').and.returnValue(signDocument);
    component.submitDocument(true);
    expect(component.submitDocument).toBeTruthy();
  });

  it('execute buildImageUrl', () => {
    component.signatureDocumentDetails = { ownerId: '3', document: { displayText: 'text' } };
    component.buildImageUrl();
    expect(component.buildImageUrl).toBeTruthy();
  });

  it('execute pushToDocSignerDetails', () => {
    component.documentSignersDetails = [];
    component.pushToDocSignerDetails(1, '', true, 1, '');
    expect(component.pushToDocSignerDetails).toBeTruthy();
  });

  it('execute next', () => {
    component.signatureDocumentDetails = { ownerId: '3', document: { displayText: 'text' } };
    component.next();
    expect(component.next).toBeTruthy();
  });

  it('execute previous', () => {
    component.signatureDocumentDetails = { ownerId: '3', document: { displayText: 'text' } };
    component.previous();
    expect(component.previous).toBeTruthy();
  });

  it('execute back', () => {
    component.back();
    expect(component.back).toBeTruthy();
  });

  it('execute back: create', () => {
    sharedService.automaticLinkedItems = { sample: 'data' };
    component.back();
    expect(component.back).toBeTruthy();
  });

  it('execute back: showConfirmBoxOnBack', () => {
    spyOn(commonService, 'showAlert').and.resolveTo(true);
    component.dummyDroppedObjects = [
      { signaturePaletteLock: 1, signature: 'asdd', associateUser: 'fg', paletteType: 'Sign' }
    ];
    if (component.dummyDroppedObjects) {
      const checkPaletteSigned = component.isSignedPalette(component.dummyDroppedObjects);
      if (checkPaletteSigned) {
        component.back();
        expect(component.back).toBeTruthy();
      }
    }
  });

  it('execute back: navigateToBack', () => {
    component.dummyDroppedObjects = [
      { signaturePaletteLock: 1, signature: '', associateUser: 'fg', paletteType: 'Sign' }
    ];
    if (component.dummyDroppedObjects) {
      const checkPaletteSigned = component.isSignedPalette(component.dummyDroppedObjects);
      if (!checkPaletteSigned) {
        component.back();
        expect(component.back).toBeTruthy();
      }
    }
  });

  it('execute downloadDoc', () => {
    component.documentInfo = { displayText: '' };
    component.downloadDoc();
    expect(component.downloadDoc).toBeTruthy();
  });

  it('execute openSignPad', fakeAsync(() => {
    component.documentInfo = { displayText: '' };
    component.dateWithSign = { recipient: true };
    component.signaturesByUsers = { signaturesByUsers: 3 };
    component.dummyDroppedObjects = [{ a: 1 }];
    component.displayTypeConditions = { allowAssociateRoles: '' };
    component.openSignPad(component.dummyDroppedObjects[0]);
    tick(200);
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.openSignPad).toBeTruthy();
  }));

  it('execute dateCanvas', fakeAsync(() => {
    component.documentInfo = { displayText: '' };
    component.dateWithSign = { recipient: true };
    component.dummyDroppedObjects = [{ a: 1 }];
    component.dateCanvas();
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.dateCanvas).toBeTruthy();
  }));

  it('execute nameCanvas', fakeAsync(() => {
    component.documentInfo = { displayText: '' };
    component.dateWithSign = { recipient: true };
    component.dummyDroppedObjects = [{ a: 1 }];
    component.nameCanvas('');
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.nameCanvas).toBeTruthy();
  }));

  it('execute dateOrNameCanvasDraw', fakeAsync(() => {
    component.documentInfo = { displayText: '' };
    component.dateWithSign = { recipient: true };
    component.dummyDroppedObjects = [{ a: 1 }];
    component.dateOrNameCanvasDraw('', () => undefined, '');
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.dateOrNameCanvasDraw).toBeTruthy();
  }));

  it('execute dateAndNameCanvasDraw', fakeAsync(() => {
    component.documentInfo = { displayText: '' };
    component.dateWithSign = { recipient: true };
    component.dummyDroppedObjects = [{ a: 1 }];
    component.dateAndNameCanvasDraw('', () => undefined, '');
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.dateAndNameCanvasDraw).toBeTruthy();
  }));

  it('execute combineCanvas: recipient', fakeAsync(() => {
    component.dateWithSign = { recipient: true };
    component.signaturesByUsers = { userId: 3 };
    component.userData = { userId: 3 };
    component.combineCanvas('', () => undefined);
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.combineCanvas).toBeTruthy();
  }));

  it('execute combineCanvas: Associate type 2', fakeAsync(() => {
    component.documentOwnerId = '35';
    component.dateWithSign = { associate: 2 };
    component.userData = { userId: 35 };
    component.combineCanvas('', () => undefined);
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.combineCanvas).toBeTruthy();
  }));

  it('execute combineCanvas: Associate type 3', fakeAsync(() => {
    component.documentOwnerId = '35';
    component.dateWithSign = { associate: 3 };
    component.userData = { userId: 35 };
    component.associateSignatureByRoles = { displayName: '' };
    component.combineCanvas('', () => undefined);
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.combineCanvas).toBeTruthy();
  }));

  it('execute combineCanvas: Associate type 4', fakeAsync(() => {
    component.documentOwnerId = '35';
    component.dateWithSign = { associate: 4 };
    component.userData = { userId: 35 };
    component.associateSignatureByRoles = { displayName: '' };
    component.combineCanvas('', () => undefined);
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.combineCanvas).toBeTruthy();
  }));

  it('execute combineCanvas: patient', fakeAsync(() => {
    component.documentInfo = { displayText: '', signatureStatus: 'PENDING' };
    component.documentOwnerId = '35';
    component.dateWithSign = { patient: true };
    component.userData = { userId: 35 };
    component.combineCanvas('', () => undefined);
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.combineCanvas).toBeTruthy();
  }));
  it('execute startSigningProcess', () => {
    component.startSigningProcess();
    expect(component.startSigningProcess).toBeTruthy();
  });


  it('execute pushGroupObjectToArray', () => {
    component.pushGroupObjectToArray('');
    expect(component.pushGroupObjectToArray).toBeTruthy();
  });

  it('execute checkEveryFieldAsGroupBox', () => {
    component.dummyDroppedObjects = [
      {
        signaturePaletteLock: 1,
        signature: 'asdd',
        associateUser: 'fg',
        paletteType: 'Checkbox',
        mandatory: false,
        group: true
      }
    ];
    component.checkEveryFieldAsGroupBox(component.dummyDroppedObjects);
    expect(component.checkEveryFieldAsGroupBox).toBeTruthy();
  });

  it('execute isSignedPalette', () => {
    component.dummyDroppedObjects = [
      { signaturePaletteLock: 1, signature: 'asdd', associateUser: 'fg', paletteType: 'Sign', mandatory: true }
    ];
    component.isSignedPalette(component.dummyDroppedObjects);
    expect(component.isSignedPalette).toBeTruthy();
  });

  it('execute bindPaletteDetailsFromTag', () => {
    component.bindPaletteDetailsFromTag();
    expect(component.bindPaletteDetailsFromTag).toBeTruthy();
  });
  it('execute bindPaletteDetailsFromTag', () => {
    component.signatureTypeTagData = { signaturePalette: [{ paletteType: 'test' }] };
    component.bindPaletteDetailsFromTag();
    expect(component.bindPaletteDetailsFromTag).toBeTruthy();
  });

  it('execute checkboxClick', () => {
    component.checkboxClick();
    expect(component.checkboxClick).toBeTruthy();
  });

  it('execute navigateToBack', () => {
    component.navigateToBack();
    expect(component.navigateToBack).toBeTruthy();
  });

  it('execute cancel', () => {
    spyOn(commonService, 'showAlert').and.resolveTo(false);
    component.cancel();
    expect(component.cancel).toBeTruthy();
  });

  it('execute showConfirmBoxOnBack', () => {
    spyOn(commonService, 'showAlert').and.resolveTo(false);
    component.showConfirmBoxOnBack();
    fixture.whenStable().then((confirmation) => {
      if (confirmation) component.navigateToBack();
    });
    expect(component.showConfirmBoxOnBack).toBeTruthy();
  });

  it('execute getScrollPosition', () => {
    const event = {
      detail: {
        scrollTop: 0
      }
    };
    component.getScrollPosition(event);
    expect(component.getScrollPosition).toBeTruthy();
  });

  it('execute changeOrientationMode', () => {
    spyOn(window, 'addEventListener').and.callThrough();
    component.changeOrientationMode();
    expect(component.changeOrientationMode).toBeTruthy();
  });

  it('execute IncrementPalateCount ', () => {
    component.incrementOrDecrementPalateCount('Demo', true);
    expect(component.incrementOrDecrementPalateCount).toBeTruthy();
  });

  it('execute DecrementPalateCount ', () => {
    component.incrementOrDecrementPalateCount('Demo', false);
    expect(component.incrementOrDecrementPalateCount).toBeTruthy();
  });

  it('execute morePaletteButton ', () => {
    popupSpy.onWillDismiss.and.resolveTo({ data: { groupObj: '' } });
    component.morePaletteButton();
    expect(component.morePaletteButton).toBeTruthy();
  });
  it('execute morePaletteButton groupObj', () => {
    popupSpy.onWillDismiss.and.resolveTo({ data: { groupObj: 'gfdfd' } });
    component.morePaletteButton();
    expect(component.morePaletteButton).toBeTruthy();
  });
  it('execute documentSend', () => {
    component.documentSend();
    expect(component.documentSend).toBeTruthy();
  });
  it('execute morePaletteButton checkboxCondition', () => {
    popupSpy.onWillDismiss.and.resolveTo({ data: { checkboxCondition: 'gfdfd', checkboxGroupDetails: [{ test: 1 }] } });
    spyOn(sharedService, 'getDocumentWidthAndHeight').and.returnValue('');
    spyOn(sharedService, 'createPaletteButton').and.returnValue();
    component.morePaletteButton();
    expect(component.morePaletteButton).toBeTruthy();
  });

  it('execute removePaletteOption ', () => {
    component.removePaletteOption({ groupName: 'Demo' }, 1);
    expect(component.removePaletteOption).toBeTruthy();
  });

  it('execute groupArrayStatusChange ', () => {
    component.groupArrayStatusChange('Demo');
    expect(component.groupArrayStatusChange).toBeTruthy();
  });

  it('execute onDragStart true', () => {
    component.onDragStart(true);
    expect(component.onDragStart).toBeTruthy();
  });

  it('execute onDragStart false', () => {
    component.onDragStart(false);
    expect(component.onDragStart).toBeTruthy();
  });

  it('execute resetDivTransform', () => {
    component.resetDivTransform('id-123343434');
    expect(component.resetDivTransform).toBeTruthy();
  });

  it('execute fetchSiteIdForFolder', () => {
    component.fetchSiteIdForFolder(1234);
    expect(component.fetchSiteIdForFolder).toBeTruthy();
  });

  it('execute fetchSites', () => {
    spyOn(graphqlService, 'fetchSites').and.resolveTo('test');
    component.documentInfo = { admissionId: '43543' };
    component.fetchSites(2);
    expect(component.fetchSites).toBeTruthy();
  });

  it('execute fetchSiteListbyUserId', () => {
    const returnData = {
      data: {
        siteListByUserId: {
          data: [
            {
              id: 2,
              name: 'demo',
              __typename: 'UserSites'
            }
          ]
        }
      }
    };
    sharedService.userData.config.documet_exchange_mode = 'fc';
    spyOn(graphqlService, 'siteListByUserId').and.returnValue(of(returnData));
    component.fetchSiteListbyUserId();
    expect(component.fetchSiteListbyUserId).toBeTruthy();
  });

  it('should increment currentDocPage by 1 when pageNumber is 0', () => {
    component.currentDocPage = 5; // Set initial currentDocPage value
    spyOn(component, 'buildImageUrl');
    component.next(0);
    expect(component.currentDocPage).toBe(6);
    expect(component.buildImageUrl).toHaveBeenCalled();
  });

  it('should set currentDocPage to the provided pageNumber when pageNumber is not 0', () => {
    spyOn(component, 'buildImageUrl');
    component.next(3);
    expect(component.currentDocPage).toBe(3);
    expect(component.buildImageUrl).toHaveBeenCalled();
  });

  it('should update fixLeft and fixTop properties of elements in addObjects array', () => {
    component.addObjects = [
      {
        id: 'drop-1',
        page: 1,
        myLocation: {
          left: 100,
          top: 200,
          fixLeft: 0,
          fixTop: 0
        }
      },
      {
        id: 'drop-2',
        page: 2,
        myLocation: {
          left: 300,
          top: 400,
          fixLeft: 0,
          fixTop: 0
        }
      }
    ];

    component.currentDocPage = 1;
    spyOn(component, 'resetDivTransform');
    component.setTopAndLeftDynamic();
    expect(component.addObjects[0].myLocation.fixLeft).toBe(100);
    expect(component.addObjects[0].myLocation.fixTop).toBe(200);
    expect(component.resetDivTransform).toHaveBeenCalledWith(`drop-1`);
    expect(component.addObjects[1].myLocation.fixLeft).toBe(0);
    expect(component.addObjects[1].myLocation.fixTop).toBe(0);
  });

  it('should not update fixLeft and fixTop properties when addObjects array is empty', () => {
    component.addObjects = [];
    spyOn(component, 'resetDivTransform');
    component.setTopAndLeftDynamic();
    expect(component.resetDivTransform).not.toHaveBeenCalled();
  });

  it('should increment the totalField of the matching groupName when condition is true', () => {
    component.signatureGroupDetails = [
      { groupName: 'Group A', totalField: 2 },
      { groupName: 'Group B', totalField: 0 }
    ];
    const groupName = 'Group A';
    const condition = true;
    component.incrementOrDecrementPalateCount(groupName, condition);

    expect(component.signatureGroupDetails[0].totalField).toBe(3);
    expect(component.signatureGroupDetails[1].totalField).toBe(0);
  });

  it('should decrement the totalField of the matching groupName when condition is false and totalField is not 0', () => {
    component.signatureGroupDetails = [
      { groupName: 'Group A', totalField: 3 },
      { groupName: 'Group B', totalField: 1 }
    ];
    const groupName = 'Group A';
    const condition = false;
    component.incrementOrDecrementPalateCount(groupName, condition);
    expect(component.signatureGroupDetails[0].totalField).toBe(2);
    expect(component.signatureGroupDetails[1].totalField).toBe(1);
  });

  it('should not decrement the totalField of the matching groupName when condition is false and totalField is already 0', () => {
    component.signatureGroupDetails = [
      { groupName: 'Group A', totalField: 0 },
      { groupName: 'Group B', totalField: 2 }
    ];
    const groupName = 'Group A';
    const condition = false;
    component.incrementOrDecrementPalateCount(groupName, condition);
    expect(component.signatureGroupDetails[0].totalField).toBe(0);
    expect(component.signatureGroupDetails[1].totalField).toBe(2);
  });

  it('should not modify any totalField when no matching groupName is found', () => {
    component.signatureGroupDetails = [
      { groupName: 'Group A', totalField: 2 },
      { groupName: 'Group B', totalField: 1 }
    ];
    const groupName = 'Group C';
    const condition = true;
    component.incrementOrDecrementPalateCount(groupName, condition);
    expect(component.signatureGroupDetails[0].totalField).toBe(2);
    expect(component.signatureGroupDetails[1].totalField).toBe(1);
  });

  it('should call createPaletteButton with correct parameters', () => {
    const field = { name: 'Field 1' };
    const checkboxLabelCondition = true;
    const groupObject = { groupName: 'Group A' };
    const spyOnCreatePaletteButton = spyOn(sharedService, 'createPaletteButton');
    component.createPaletteButton(field, checkboxLabelCondition, groupObject);
    expect(spyOnCreatePaletteButton).toBeDefined();
  });

  it('should update the height, width, and fontSize properties of the element at the specified index', () => {
    component.addObjects = [
      {
        myLocation: {
          height: '100px',
          width: '200px',
          fontSize: '12px'
        }
      },
      {
        myLocation: {
          height: '80px',
          width: '150px',
          fontSize: '10px'
        }
      }
    ];
    const event = {
      size: {
        height: 150,
        width: 300
      }
    };
    const index = 1;
    component.onResizing(event, index);

    expect(component.addObjects[0].myLocation.height).toBe('100px');
    expect(component.addObjects[0].myLocation.width).toBe('200px');
    expect(component.addObjects[0].myLocation.fontSize).toBe('12px');

    expect(component.addObjects[1].myLocation.height).toBe('150px');
    expect(component.addObjects[1].myLocation.width).toBe('300px');
    expect(component.addObjects[1].myLocation.fontSize).toBe(14);
  });

  it('should not update any properties if the specified index is not found', () => {
    component.addObjects = [
      {
        myLocation: {
          height: '100px',
          width: '200px',
          fontSize: '12px'
        }
      },
      {
        myLocation: {
          height: '80px',
          width: '150px',
          fontSize: '10px'
        }
      }
    ];
    const event = {
      size: {
        height: 150,
        width: 300
      }
    };
    const index = 2;
    component.onResizing(event, index);

    expect(component.addObjects[0].myLocation.height).toBe('100px');
    expect(component.addObjects[0].myLocation.width).toBe('200px');
    expect(component.addObjects[0].myLocation.fontSize).toBe('12px');

    expect(component.addObjects[1].myLocation.height).toBe('80px');
    expect(component.addObjects[1].myLocation.width).toBe('150px');
    expect(component.addObjects[1].myLocation.fontSize).toBe('10px');
  });

  it('should set the "selected" property of the matching signer and update the "selectedDocumentSignerDetails" when condition is true', () => {
    const selectedSigner = {
      detail: {
        value: 'signer1'
      }
    };
    const condition = true;
    component.documentSignersDetails = [
      { identifier: 'signer1', selected: false },
      { identifier: 'signer2', selected: false }
    ];
    component.selectedFileDetails = {
      documentDisplayText: 'demo-demo'
    };
    component.signerSelected(selectedSigner, condition);
    expect(component.documentSignersDetails[0].selected).toBe(true);
    expect(component.documentSignersDetails[1].selected).toBe(false);
    expect(component.selectedDocumentSignerDetails).toEqual(component.documentSignersDetails[0]);
  });
  it('should set the "selected" property of the matching signer and update the "selectedDocumentSignerDetails" when condition is false', () => {
    const selectedSigner = {
      identifier: 'signer2'
    };
    const condition = false;
    component.documentSignersDetails = [
      { identifier: 'signer1', selected: false },
      { identifier: 'signer2', selected: false }
    ];
    component.selectedFileDetails = {
      documentDisplayText: 'demo-demo'
    };
    component.signerSelected(selectedSigner, condition);
    expect(component.documentSignersDetails[0].selected).toBe(false);
    expect(component.documentSignersDetails[1].selected).toBe(true);
    expect(component.selectedDocumentSignerDetails).toEqual(component.documentSignersDetails[1]);
  });
  it('should not modify any "selected" properties or "selectedDocumentSignerDetails" when no matching signer is found', () => {
    const selectedSigner = {
      detail: {
        value: 'signer3'
      }
    };
    const condition = true;
    component.documentSignersDetails = [
      { identifier: 'signer1', selected: false },
      { identifier: 'signer2', selected: false }
    ];
    component.selectedFileDetails = {
      documentDisplayText: 'demo-demo'
    };
    component.selectedDocumentSignerDetails = {
      name: 'demo'
    };
    component.signerSelected(selectedSigner, condition);
    expect(component.documentSignersDetails[0].selected).toBe(false);
    expect(component.documentSignersDetails[1].selected).toBe(false);
  });

  it('should set the value of textareaPalette', () => {
    component.textareaPalette = {
      value: 'Short text.'
    };
    component.textBreak('Short text.');
    fixture.detectChanges();
    expect(component.textBreak).toBeDefined();
  });

  it('should set the value of textareaPalette', () => {
    component.showErrorMessage('demo');
    fixture.detectChanges();
    expect(component.showErrorMessage).toBeDefined();
  });

  it('should set the transform property of the element with the specified id to the provided value', () => {
    const id = 'elementId';
    const value = 'translate(0px, 0px)';
    const mockElement = document.createElement('div');
    mockElement.id = `drop-${id}`;
    spyOn(document, 'getElementById').and.returnValue(mockElement);
    component.resetDivTransform(id);
    expect(document.getElementById(`drop-${id}`).style.transform).toBe(value);
  });

  it('should not set the transform property if the element with the specified id does not exist', () => {
    const id = 'nonExistentId';
    const value = 'translate(0px, 0px)';
    spyOn(document, 'getElementById').and.returnValue(null);
    component.resetDivTransform(id);
    expect(document.getElementById(`drop-${id}`)).toBeNull();
  });

  it('should update the border and boxShadow properties of the dropped objects based on the conditions', () => {
    const mockDroppedObjects = [
      {
        id: 1,
        mandatory: true,
        signature: '',
        border: '1px dotted #f00',
        boxShadow: 'rgba(221, 0, 0, 0.52) 0px 0px 2px 1px'
      },
      {
        id: 2,
        mandatory: false,
        signature: '',
        border: '1px dotted rgb(53, 171, 221)',
        boxShadow: 'rgba(0, 171, 221, 0.7) 0px 0px 2px 0.5px'
      },
      {
        id: 3,
        mandatory: false,
        signature: 'signed',
        border: '1px dotted rgb(53, 171, 221)',
        boxShadow: 'rgba(0, 171, 221, 0.7) 0px 0px 2px 0.5px'
      }
    ];
    component.dummyDroppedObjects = mockDroppedObjects;
    component.resetDroppedObjectColor();

    expect(component.dummyDroppedObjects[0].border).toBe('1px dotted #f00');
    expect(component.dummyDroppedObjects[0].boxShadow).toBe('rgba(221, 0, 0, 0.52) 0px 0px 2px 1px');
    expect(component.dummyDroppedObjects[1].border).toBe('1px dotted rgb(53, 171, 221)');
    expect(component.dummyDroppedObjects[1].boxShadow).toBe('rgba(0, 171, 221, 0.7) 0px 0px 2px 0.5px');
    expect(component.dummyDroppedObjects[2].border).toBe('1px dotted rgb(53, 171, 221)');
    expect(component.dummyDroppedObjects[2].boxShadow).toBe('rgba(0, 171, 221, 0.7) 0px 0px 2px 0.5px');
  });

  it('should update the border and boxShadow properties of the dropped object with the specified id', () => {
    const mockDroppedObjects = [
      {
        id: 1,
        mandatory: true,
        signature: '',
        border: '1px dotted #f00',
        boxShadow: 'rgba(221, 0, 0, 0.52) 0px 0px 2px 1px'
      },
      {
        id: 2,
        mandatory: false,
        signature: '',
        border: '1px dotted rgb(53, 171, 221)',
        boxShadow: 'rgba(0, 171, 221, 0.7) 0px 0px 2px 0.5px'
      },
      {
        id: 3,
        mandatory: false,
        signature: 'signed',
        border: '1px dotted rgb(53, 171, 221)',
        boxShadow: 'rgba(0, 171, 221, 0.7) 0px 0px 2px 0.5px'
      }
    ];
    component.dummyDroppedObjects = mockDroppedObjects;
    const id = 2;
    component.resetDroppedObjectColor(id);

    expect(component.dummyDroppedObjects[0].border).toBe('1px dotted #f00');
    expect(component.dummyDroppedObjects[0].boxShadow).toBe('rgba(221, 0, 0, 0.52) 0px 0px 2px 1px');
    expect(component.dummyDroppedObjects[1].border).toBe('1px solid rgb(220 166 23)'); // CHP-15069 Update highlights color
    expect(component.dummyDroppedObjects[1].boxShadow).toBe('rgb(220 166 23 / 80%) 0px 0px 2px 1px'); // CHP-15069 Update highlights color
  });

  it('should unlock device when platform is capacitor and iPad || Tablet', () => {
    component.isMobilePlatform = true;
    spyOn(sharedService.platform, 'is').and.returnValue(true);
    component.lockDeviceOrientation();
    expect(sharedServiceMock.unlockDevice).toBeDefined();
  });

  it('should lock device when in portrait mode and unlock device when platform is capacitor and iPad || Tablet', () => {
    component.isMobilePlatform = true;
    spyOn(sharedService.platform, 'is').and.returnValue(true);
    component.lockDeviceOrientation();
    const isLandScapeMode = window.orientation === -90 || window.orientation === 90;
    if (!isLandScapeMode) {
      expect(sharedServiceMock.lockDevice).toBeDefined();
    }
    expect(sharedServiceMock.unlockDevice).toBeDefined();
  });
  it('should lock device orientation to portrait', () => {
    component.isMobilePlatform = true;
    spyOn(sharedService.platform, 'is').and.returnValue(false);
    component.lockDeviceOrientation();
    expect(component.lockDeviceOrientation).toBeTruthy();
  });
  it('execute imageLoaded', () => {
    const mockDroppedObjects = [
      {
        id: 1,
        mandatory: true,
        signature: '',
        border: '1px dotted #f00',
        boxShadow: 'rgba(221, 0, 0, 0.52) 0px 0px 2px 1px'
      }
    ];
    component.dummyDroppedObjects = mockDroppedObjects;
    component.imageLoaded('');
    expect(component.imageLoaded).toBeTruthy();
  });
  it('execute imageLoaded: isUploadDocView', () => {
    component.isUploadDocView = true;
    const mockDroppedObjects = [
      {
        id: 1,
        mandatory: true,
        signature: '',
        border: '1px dotted #f00',
        boxShadow: 'rgba(221, 0, 0, 0.52) 0px 0px 2px 1px'
      }
    ];
    component.dummyDroppedObjects = mockDroppedObjects;
    component.imageLoaded('');
    expect(component.imageLoaded).toBeTruthy();
  });

  it('should not lock or unlock device orientation if conditions are not met', () => {
    component.isMobilePlatform = false;
    const sharedServiceSpy = jasmine.createSpyObj('SharedService', ['lockDevice', 'unlockDevice']);
    component.lockDeviceOrientation();

    expect(sharedServiceSpy.lockDevice).not.toHaveBeenCalled();
    expect(sharedServiceSpy.unlockDevice).not.toHaveBeenCalled();
  });

  describe('filterAutoNavigateObject', () => {
    it('should filter undefined items', () => {
      component.autoNavigateItem = [1, undefined, 3, undefined, 5];
      component.filterAutoNavigateObject();
      expect(component.autoNavigateItem).toEqual([1, 3, 5]);
    });
    it('should handle empty array', () => {
      component.autoNavigateItem = [];
      component.filterAutoNavigateObject();
      expect(component.autoNavigateItem).toEqual([]);
    });
    it('should handle array with no undefined elements', () => {
      component.autoNavigateItem = [1, 2];
      component.filterAutoNavigateObject();
      expect(component.autoNavigateItem).toEqual([1, 2]);
    });
  });

  it('should set element as viewed and update properties', () => {
    component.dummyDroppedObjects = [
      { id: 1, view: false, mandatory: true, signature: 'abc', paletteType: 'signValue' },
      { id: 2, view: false, mandatory: false, signature: '', paletteType: 'Sign' },
      { id: 3, view: false, mandatory: false, signature: 'ghi', paletteType: 'otherType' }
    ];
    const idToView = 2;
    component.setObjectASViewed(idToView);
    expect(component.dummyDroppedObjects[1].view).toBe(true);
    expect(component.dummyDroppedObjects[1].signature).toBe('');
  });

  it('should not change any elements if id is not found', () => {
    component.dummyDroppedObjects = [
      { id: 1, view: false, mandatory: true, signature: 'abc', paletteType: 'Sign' },
      { id: 2, view: false, mandatory: false, signature: 'def', paletteType: 'Sign' },
      { id: 3, view: false, mandatory: false, signature: 'ghi', paletteType: 'otherType' }
    ];
    const idToView = 4;
    component.setObjectASViewed(idToView);
    expect(component.dummyDroppedObjects).toEqual([
      { id: 1, view: false, mandatory: true, signature: 'abc', paletteType: 'Sign' },
      { id: 2, view: false, mandatory: false, signature: 'def', paletteType: 'Sign' },
      { id: 3, view: false, mandatory: false, signature: 'ghi', paletteType: 'otherType' }
    ]);
  });
  describe('pushGroupObjectToArray', () => {
    it('should push group data to empty array', () => {
      component.signatureGroupDetails = [];
      const groupData = { groupName: 'Group A', data: 'some data' };
      component.pushGroupObjectToArray(groupData);
      expect(component.signatureGroupDetails).toEqual([groupData]);
    });
    it('should not push group data if it already exists in the array', () => {
      component.signatureGroupDetails = [
        { groupName: 'Group A', data: 'some data' },
        { groupName: 'Group B', data: 'some other data' }
      ];
      const groupData = { groupName: 'Group A', data: 'new data' };
      component.pushGroupObjectToArray(groupData);
      expect(component.signatureGroupDetails.length).toBe(2);
    });
    it('should push group data if it does not already exist in the array', () => {
      component.signatureGroupDetails = [
        { groupName: 'Group A', data: 'some data' },
        { groupName: 'Group B', data: 'some other data' }
      ];
      const groupData = { groupName: 'Group C', data: 'some more data' };
      component.pushGroupObjectToArray(groupData);
      expect(component.signatureGroupDetails.length).toBe(3);
      expect(component.signatureGroupDetails[2]).toEqual(groupData);
    });
  });
  it('execute startSigningProcess', () => {
    component.startSigningProcess(true);
    expect(component.startSigningProcess).toBeTruthy();
  });
  describe('openTextPad', () => {
    it('execute openTextPad', () => {
      component.openTextPad(1);
      expect(component.openTextPad).toBeDefined();
    });
    it('should open text pad and update properties correctly', () => {
      component.paletteStatus = component.paletteStatuses.confirmed;
      component.dummyDroppedObjects = [
        {
          id: 'drop-1',
          page: 1,
          myLocation: {
            left: 100,
            top: 200,
            fixLeft: 0,
            fixTop: 0
          }
        }
      ];
      component.addObjects = [
        {
          id: 'drop-1',
          page: 1,
          myLocation: {
            left: 100,
            top: 200,
            fixLeft: 0,
            fixTop: 0
          }
        }
      ];
      const indexToOpen = 1;
      component.openTextPad(indexToOpen);
      expect(component.addObjects).toEqual([]);
    });
  });
  it('should call applyLineBreaks', () => {
    component.applyLineBreaks('id-12340-2323', 'textValue');
    expect(component.applyLineBreaks).toBeTruthy();
  });
  it('should call checkboxStatusChange', () => {
    component.checkboxStatusChange({ target: { checked: true } }, 1);
    expect(component.checkboxStatusChange).toBeDefined();
  });
  it('should enable save sign flow when isUploadDocView is true and conditions are met', () => {
    component.isUploadDocView = true;
    component.signatureTypeTagData = {
      obtainSignature: true,
      allowAssociatePatient: true,
      allowPersonalSignature: true
    };
    const documentDetails = {
      signatureStatus: Signature.signatureStatus.signatureDraftStatus
    };
    component.enableSaveSignWorkFlow(documentDetails);
    const enableSaveSignFlow = JSON.parse(sessionStorage.getItem('enableSaveSignFlow') as string);
    expect(enableSaveSignFlow).toBeTrue();
  });

  it('should disable save sign flow when isUploadDocView is true and conditions are not met', () => {
    component.isUploadDocView = true;
    component.signatureTypeTagData = {
      obtainSignature: true,
      allowAssociatePatient: true,
      allowPersonalSignature: false,
      allowAssociateRoles: false
    };
    const documentDetails = {
      signatureStatus: Signature.signatureStatus.signatureDraftStatus
    };
    component.enableSaveSignWorkFlow(documentDetails);
    const enableSaveSignFlow = JSON.parse(sessionStorage.getItem('enableSaveSignFlow') as string);
    expect(enableSaveSignFlow).toBeFalse();
  });

  it('should enable save sign flow when displayTypeConditions.obtainSignature is true and conditions are met', () => {
    component.isUploadDocView = false;
    component.displayTypeConditions = {
      obtainSignature: true,
      allowAssociateRoles: true
    };
    const documentDetails = {
      signatureStatus: Signature.signatureStatus.signatureApprovalStatus
    };
    component.enableSaveSignWorkFlow(documentDetails);
    const enableSaveSignFlow = JSON.parse(sessionStorage.getItem('enableSaveSignFlow') as string);
    expect(enableSaveSignFlow).toBeTrue();
  });

  it('should disable save sign flow when displayTypeConditions.obtainSignature is true and conditions are not met', () => {
    component.isUploadDocView = false;
    component.displayTypeConditions = {
      obtainSignature: true,
      allowAssociateRoles: false,
      allowPendingApproveSignature: false,
      allowPersonalSignature: false
    };
    const documentDetails = {
      signatureStatus: Signature.signatureStatus.signatureApprovalStatus
    };
    component.enableSaveSignWorkFlow(documentDetails);
    const enableSaveSignFlow = JSON.parse(sessionStorage.getItem('enableSaveSignFlow') as string);
    expect(enableSaveSignFlow).toBeFalse();
  });

  it('should enable save sign flow when displayTypeConditions.allowAssociateRoles is true', () => {
    component.isUploadDocView = false;
    component.displayTypeConditions = {
      obtainSignature: false,
      allowAssociateRoles: true
    };
    const documentDetails = {
      signatureStatus: Signature.signatureStatus.signatureDraftStatus
    };
    component.enableSaveSignWorkFlow(documentDetails);
    const enableSaveSignFlow = JSON.parse(sessionStorage.getItem('enableSaveSignFlow') as string);
    expect(enableSaveSignFlow).toBeTrue();
  });

  it('should disable save sign flow when displayTypeConditions.allowAssociateRoles is false and signatureStatus is signaturePendingStatus', () => {
    component.isUploadDocView = false;
    component.displayTypeConditions = {
      obtainSignature: false,
      allowAssociateRoles: true
    };
    const documentDetails = {
      signatureStatus: Signature.signatureStatus.signaturePendingStatus
    };
    component.enableSaveSignWorkFlow(documentDetails);
    const enableSaveSignFlow = JSON.parse(sessionStorage.getItem('enableSaveSignFlow') as string);
    expect(enableSaveSignFlow).toBeFalse();
  });

  describe('bindPaletteDetailsFromTag - prepopulation logic', () => {
    beforeEach(() => {
      component.signatureDocumentDetails = {
        prepopulationData: JSON.stringify({
          patientMrn: 'MRN123456',
          patient: {
            id: '1',
            firstName: 'John',
            lastName: 'Doe',
            dateOfBirth: '1990-01-01',
            mobile: '555-1234',
            email: '<EMAIL>',
            gender: 'Male'
          }
        })
      };
      
      component.prepopulationData = {
        patientMrn: 'MRN123456',
        patient: {
          id: '1',
          firstName: 'John',
          lastName: 'Doe',
          dateOfBirth: '1990-01-01',
          mobile: '555-1234',
          email: '<EMAIL>',
          gender: 'Male'
        }
      };
      
      component.userData = { userId: 1 };
      component.displayTypeConditions = {};
      
      component.signatureTypeTagData = {
        signaturePalette: [{
          paletteType: Signature.paletteType.textField,
          textFieldActAs: 'firstName',
          signatureDocumentPageNumber: 1,
          mandatory: false
        }]
      };
      
      component.data = {
        recipient: { id: 1 },
        associate: { id: 2 },
        patient: { id: 3 }
      };
      
      component.addObjects = [];
    });

    it('should prepopulate firstName field correctly', () => {
      component.signatureTypeTagData.signaturePalette[0].textFieldActAs = 'firstName';
      
      component.bindPaletteDetailsFromTag();
      
      expect(component.prepopulationData).toBeDefined();
      expect(component.prepopulationData.patient.firstName).toBe('John');
    });

    it('should prepopulate lastName field correctly', () => {
      component.signatureTypeTagData.signaturePalette[0].textFieldActAs = 'lastName';
      
      component.bindPaletteDetailsFromTag();
      
      expect(component.prepopulationData.patient.lastName).toBe('Doe');
    });

    it('should prepopulate full name (flname) field correctly', () => {
      component.signatureTypeTagData.signaturePalette[0].textFieldActAs = 'flname';
      
      component.bindPaletteDetailsFromTag();
      
      const expectedValue = `${component.prepopulationData.patient.firstName} ${component.prepopulationData.patient.lastName}`;
      expect(expectedValue).toBe('John Doe');
    });

    it('should prepopulate last name first (lfname) field correctly', () => {
      component.signatureTypeTagData.signaturePalette[0].textFieldActAs = 'lfname';
      
      component.bindPaletteDetailsFromTag();
      
      const expectedValue = `${component.prepopulationData.patient.lastName} ${component.prepopulationData.patient.firstName}`;
      expect(expectedValue).toBe('Doe John');
    });

    it('should prepopulate MRN field correctly', () => {
      component.signatureTypeTagData.signaturePalette[0].textFieldActAs = 'mrn';
      
      component.bindPaletteDetailsFromTag();
      
      expect(component.prepopulationData.patientMrn).toBe('MRN123456');
    });

    it('should prepopulate date of birth field correctly', () => {
      component.signatureTypeTagData.signaturePalette[0].textFieldActAs = 'dob';
      
      component.bindPaletteDetailsFromTag();
      
      expect(component.prepopulationData.patient.dateOfBirth).toBe('1990-01-01');
    });

    it('should prepopulate mobile field correctly', () => {
      component.signatureTypeTagData.signaturePalette[0].textFieldActAs = 'mobile';
      
      component.bindPaletteDetailsFromTag();
      
      expect(component.prepopulationData.patient.mobile).toBe('555-1234');
    });

    it('should prepopulate email field correctly', () => {
      component.signatureTypeTagData.signaturePalette[0].textFieldActAs = 'email';
      
      component.bindPaletteDetailsFromTag();
      
      expect(component.prepopulationData.patient.email).toBe('<EMAIL>');
    });

    it('should prepopulate gender field correctly', () => {
      component.signatureTypeTagData.signaturePalette[0].textFieldActAs = 'gender';
      
      component.bindPaletteDetailsFromTag();
      
      expect(component.prepopulationData.patient.gender).toBe('Male');
    });

    it('should not prepopulate when paletteType is not textField', () => {
      component.signatureTypeTagData.signaturePalette[0].paletteType = Signature.paletteType.signValue;
      
      component.bindPaletteDetailsFromTag();
      
      expect(component.prepopulationData).toBeDefined();
    });

    it('should handle missing prepopulation data gracefully', () => {
      component.signatureDocumentDetails.prepopulationData = null;
      
      expect(() => {
        component.bindPaletteDetailsFromTag();
      }).not.toThrow();
    });

    it('should handle invalid JSON in prepopulation data', () => {
      component.signatureDocumentDetails.prepopulationData = 'invalid-json';
      
      expect(() => {
        component.bindPaletteDetailsFromTag();
      }).not.toThrow();
    });

    it('should handle empty patient ID', () => {
      component.prepopulationData = {
        patientMrn: 'MRN123456',
        patient: {
          id: '',
          firstName: 'John',
          lastName: 'Doe'
        }
      };
      component.bindPaletteDetailsFromTag();
      expect(component.prepopulationData.patient.id).toBe('');
    });

    it('should handle patient ID as 0', () => {
      component.prepopulationData = {
        patientMrn: 'MRN123456',
        patient: {
          id: 0,
          firstName: 'John',
          lastName: 'Doe'
        }
      };
      component.bindPaletteDetailsFromTag();
      expect(component.prepopulationData.patient.id).toBe(0);
    });
  });
});
