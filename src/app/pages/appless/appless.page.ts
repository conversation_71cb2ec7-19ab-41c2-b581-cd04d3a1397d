/* eslint-disable @typescript-eslint/no-explicit-any */
import { Component, NgZone, OnInit } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { CommonService } from 'src/app/services/common-service/common.service';
import { isBlank, isPresent } from 'src/app/utils/utils';
import { APIs } from 'src/app/constants/apis';
import { Activity } from 'src/app/constants/activity';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { HttpService } from 'src/app/services/http-service/http.service';
import { ActivatedRoute, NavigationExtras, Router } from '@angular/router';
import { ConfigValues } from 'src/assets/config/config';
import { ApplessResponse, ApplessErrorResponse, LoginResponse } from 'src/app/interfaces/login';
import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { Constants, UseBaseAs, UseBaseTypes, UserGroup } from 'src/app/constants/constants';
import { Config } from 'src/app/constants/config';
import { PageRoutes } from 'src/app/constants/page-routes';
import { catchError, map } from 'rxjs/operators';
import { SessionService } from 'src/app/services/session-service/session.service';
import { Observable, of } from 'rxjs';
import { getValueFromSession, localStorageGetItemWithExpiry, localStorageSetItemWithExpiry, setKeyToSession } from 'src/app/utils/storage-utils';
import * as moment from 'moment';
import { AuthService } from 'src/app/services/auth-service/auth.service';
import { FormWorkListPayload } from 'src/app/interfaces/common-interface';
import { Permissions } from 'src/app/constants/permissions';
import { PermissionService } from 'src/app/services/permission-service/permission.service';
import { ApplessNavigationService } from 'src/app/services/appless-navigation-service/appless-navigation.service';
import { FormService } from '../form-center/services/form.service';

export interface ApplessConfiguration {
  magicLinkVerificationExpiryTime: string;
  magicLinkVerificationTokenExpirationTime: string;
  enableAppLessHome: string;
  enablePatientIdentityValidation: string;
  patientIdentityValidationFields: string;
  otpSingleFieldEntry: string;
  userId: string;
  tenantId: string;
  sendId: string;
  chatRoomId: string;
  documentId: string;
  applessHomeNotificationsFrequency: string;
  navigateToApplessHome: string;
}

@Component({
  selector: 'app-verify-appless',
  templateUrl: './appless.page.html',
  styleUrls: ['./appless.page.scss']
})
export class ApplessPage implements OnInit {
  appLessForm: UntypedFormGroup;
  tokenData: string;
  verificationMessage: string;
  otpThroughMobile = false;
  verificationCodeExpired = false;
  verificationMobilePopupShow = false;
  verificationTimer;
  mobileVerificationTimerText = ' ';
  verificationCodeSendMessage = '';
  magicLinkVerificationExpiryTime: string;
  verifyTokenResponse: ApplessResponse;
  appLessWorkFlow: string;
  rememberConfigTime = Constants.defaultTokenRememberDefaultTime;
  accessGranted = false;
  notificationMode: string;
  subTitle = '';
  formDetails: any;
  errorInfoMessage = false;
  additionalFields: string[] = [];
  isOtpSent = false;
  isOtpRequired = false;
  isIdentityValidationEnabled = false;
  otpSessionId;
  resendOtp;
  expiryInterval;
  id: string;
  userId: string;
  isCampaignForm = false;
  otpSingleFieldEntry = false;
  otpSize = 6;
  isAppLessHomeEnabled = false;
  sendId: string;
  tenantId: string;
  chatRoomId: string;
  documentId: string;
  applessHomeNotificationsFrequency: string;
  navigateToApplessHome: string;

  constructor(
    private readonly formBuilder: UntypedFormBuilder,
    public readonly commonService: CommonService,
    public readonly sharedService: SharedService,
    private readonly graphqlService: GraphqlService,
    public readonly httpService: HttpService,
    public readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly authService: AuthService,
    public sessionService: SessionService,
    private readonly formService: FormService,
    private readonly permissionService: PermissionService,
    public readonly ngZone: NgZone,
    private readonly applessNavigationService: ApplessNavigationService
  ) {
    if (!isBlank(this.route.snapshot.data.applessFor)) {
      this.appLessWorkFlow = this.route.snapshot.data.applessFor;
    }
    this.route.queryParamMap.subscribe((params) => {
      this.id = params.get('id');
      if (!isBlank(this.id)) {
        this.httpService.doGet({ endpoint: APIs.configurations.replace(/{notificationId}/g, this.id) }).subscribe({
          next: (response: ApplessConfiguration) => {
            // Track AppLess link access after getting configuration
            this.trackAppLessMagicLinkAccess();
            this.verifyAndShowValidations(response);
            this.sessionService.sessionLoader = false;
          },
          error: (error) => {
            this.commonService.showMessage(error);
            this.sessionService.sessionLoader = false;
          }
        });
      } else {
        this.tokenData = params.get('token');
        // Handle the case when token or notificationMode is missing or when page redirection is restricted by AuthGuard.
        if (isBlank(this.tokenData) || (this.appLessWorkFlow === Constants.applessFlowMode.message && isBlank(params.get('notificationMode')))) {
          this.sessionService.sessionLoader = false;
          this.errorInfoMessage = true;
          //  add track activity for token missing or page redirection restricted by AuthGuard.
          this.sharedService.trackActivity({
            name: Activity.pageNavigation,
            des: {
              data: {
                applessFlow: this.appLessWorkFlow
              },
              desConstant: this.appLessWorkFlow === Constants.applessFlowMode.general ? Activity.authGuardSessionApplessDes : Activity.linkBrokenDes
            }
          });
        } else {
          this.decodeToken();
          this.sessionService.applessMessagingFlow = false;
          if (this.appLessWorkFlow === Constants.applessFlowMode.message) {
            this.notificationMode = params.get('notificationMode');
            this.sessionService.applessMessagingFlow = true;
          }
        }
      }
    });
    this.appLessForm = this.formBuilder.group({});
    this.sharedService.disableSideMenu = true;
  }
  ngOnInit(): void {
    if (isBlank(this.id)) {
      this.oldAppLessFlow();
    }
  }
  get isTokenValidateAPI(): boolean {
    return [Constants.applessFlowMode.download, Constants.applessFlowMode.userConsent].includes(this.appLessWorkFlow);
  }
  oldAppLessFlow() {
    if (isBlank(this.tokenData)) {
      // token is missing
      return;
    }
    this.sessionService.sessionLoader = false;
    this.subTitle = this.getSubTitle();
    let useBaseAs: UseBaseAs = UseBaseTypes.DEFAULT;
    this.verificationMessage = this.commonService.getTranslateData('VALIDATION_MESSAGES.APPLESS_TOKEN_VERIFICATION');
    let version = Constants.apiVersions.apiV4;
    let endPoint = '';
    if (this.appLessWorkFlow === Constants.applessFlowMode.document) {
      endPoint = APIs.verifyApplessDocumentToken;
    } else if (this.appLessWorkFlow === Constants.applessFlowMode.form) {
      endPoint = APIs.verifyApplessFormToken;
    } else if (this.isTokenValidateAPI) {
      endPoint = APIs.tokenValidate;
    } else if (this.appLessWorkFlow === Constants.applessFlowMode.visitView) {
      endPoint = APIs.verifyApplessVisitToken;
      useBaseAs = UseBaseTypes.VISIT_SCHEDULE_ROOT;
    }
    let promise: Observable<any>;
    if (this.sessionService.applessMessagingFlow) {
      version = Constants.apiVersions.apiV5;
      this.sessionService.sessionLoader = true;
      this.verificationMessage = this.commonService.getTranslateData('VALIDATION_MESSAGES.APPLESS_MESSAGE_TOKEN_VALIDATE_LINK');
      endPoint = APIs.verifyApplessMessageToken;
      const extraParams = this.sessionService.setExtraParams();
      promise = this.httpService
        .doPost({
          endpoint: endPoint,
          hasAuthToken: false,
          payload: { ...extraParams, token: this.tokenData, notificationMode: this.notificationMode },
          version
        })
        .pipe(
          map((response) => {
            if (response.data.userSessionData.status === 505) {
              return response.data.userSessionData;
            }
            response.userData = response.data.userSessionData.response;
            response.userId = response.data.tokenData.userId;
            response.tenantId = String(response.data.tokenData.tenantId);
            response.tokenExpire = response.data.tokenData.tokenExpire;
            response.responseStatus = response.status;
            response.status = response.data.status;
            return response;
          }),
          catchError((err: ApplessErrorResponse) => {
            const response = Object.assign(err);
            if (err.data.errors) {
              response.tokenExpire = err.data.errors[0].tokenExpire;
              response.responseStatus = err.status;
              response.status = Constants.loginResponseStatus.invalid;
            }
            return of(response);
          })
        );
    } else {
      promise = this.httpService.doGet({
        endpoint: endPoint,
        hasAuthToken: false,
        extraParams: { token: this.tokenData },
        useBaseAs
      });
    }
    promise = promise.pipe(
      map((response) => {
        let result = response;
        if (this.isTokenValidateAPI) {
          if (response?.status?.code) {
            response.responseStatus = response.status;
          }
          result = isPresent(response?.data) ? response.data : response;
        }
        if (this.appLessWorkFlow === Constants.applessFlowMode.visitView) {
          response.userData = response?.data?.userData?.response;
          response.userId = response?.data?.userData?.response?.userId;
          response.tenantId = response?.data?.userData?.response?.tenantId;
          result = response;
        }
        return result;
      }),
      catchError((err: ApplessErrorResponse) => {
        if (this.appLessWorkFlow === Constants.applessFlowMode.visitView) {
          const response = Object.assign(err);
          return of(response);
        }
      })
    );
    this.sharedService.trackActivity({
      type: Activity.appless,
      name: Activity.urlFirstHit,
      des: {
        data: {
          flow: this.appLessWorkFlow,
          token: this.tokenData
        },
        desConstant: Activity.applessPageDesc
      },
      linkageId: this.tokenData
    });
    promise.subscribe({
      next: (response: ApplessResponse) => {
        if (response.status === 505) {
          this.verificationMessage = response.message;
          this.commonService.showMessage(this.verificationMessage);
          this.trackErrorLogin(this.verificationMessage);
        } else if (!isBlank(response.userId)) {
          if (response.tenantId && response.tenantId !== '') {
            this.verifyTokenResponse = response;
            this.setupUserData(response.userData);

            if (this.appLessWorkFlow === Constants.applessFlowMode.form) {
              this.formDetails = response.formDetails;
            } else if (this.sessionService.applessMessagingFlow) {
              this.verificationMessage = this.commonService.getTranslateData('VALIDATION_MESSAGES.APPLESS_MESSAGE_TOKEN_VALIDATE_SESSION');
              this.sessionService.tokenData = response.data.tokenData;
              this.sessionService.applessMessagingFlow = true;
            } else if (this.appLessWorkFlow === Constants.applessFlowMode.document) {
              this.sharedService.applessDocumentId = response.documentId;
            } else if (this.appLessWorkFlow === Constants.applessFlowMode.visitView) {
              this.sessionService.tokenData = response.data.tokenData;
            }
            const isPatientIdentityValidationApplicable =
              (this.appLessWorkFlow === Constants.applessFlowMode.form ||
                this.appLessWorkFlow === Constants.applessFlowMode.message ||
                this.appLessWorkFlow === Constants.applessFlowMode.document ||
                this.appLessWorkFlow === Constants.applessFlowMode.download) &&
              +this.sharedService.userData.group === UserGroup.PATIENT;
            const config = {
              magicLinkVerificationExpiryTime: this.sharedService.getSiteTenantConfigValue(Config.magiclinkVerificationExpiryTime),
              magicLinkVerificationTokenExpirationTime: this.sharedService.getSiteTenantConfigValue(Config.magiclinkVerificationTokenExpirationTime),
              enableAppLessHome:
                +this.sharedService.getSiteTenantConfigValue(Config.enableApplessHome) && isPatientIdentityValidationApplicable ? '1' : '0',
              enablePatientIdentityValidation: +this.sharedService.getSiteTenantConfigValue(Config.enablePatientIdentityValidation) && isPatientIdentityValidationApplicable ? '1' : '0',
              patientIdentityValidationFields: this.sharedService.getSiteTenantConfigValue(Config.patientIdentityValidationFields),
              otpSingleFieldEntry: this.sharedService.getSiteTenantConfigValue(Config.otpSingleFieldEntry),
              userId: this.sharedService.userData.userName,
              tenantId: null,
              sendId: null,
              chatRoomId: null,
              documentId: null,
              applessHomeNotificationsFrequency: null,
              navigateToApplessHome: null
            };
            if (
              response.device === Constants.accessCodeSentMedium.mobile.toLowerCase() ||
              (this.sessionService?.applessMessagingFlow && response?.data?.tokenData?.notificationMode === Constants.applessNotificationMode.sms)
            ) {
              this.otpThroughMobile = true;
              config.userId = this.sharedService.userData.countryCode + this.sharedService.userData.mobile;
            }
            this.verifyAndShowValidations(config);
          }
        } else {
          this.handleError(response);
        }
        this.sessionService.sessionLoader = false;
      },
      error: (error) => {
        this.handleError(error);
      }
    });
  }

  setupUserData(userData: LoginResponse): void {
    // eslint-disable-next-line no-param-reassign
    userData.userName = userData?.username;
    this.sharedService.userData = userData;
    this.sharedService.userData.appLessSession = true;
    this.sharedService.setPermissions(userData.privileges);
    const isVirtual =
      !this.sharedService.userData.isVirtual && this.sessionService.applessMessagingFlow ? Constants.falseAsString : Constants.trueAsString;
    sessionStorage.setItem(Constants.storageKeys.isVirtual, isVirtual);
    setKeyToSession({
      key: Constants.storageKeys.authToken,
      value: userData.authenticationToken
    });
    this.sharedService.joinAppSocket();
  }

  verifyAndShowValidations(config?: ApplessConfiguration) {
    this.magicLinkVerificationExpiryTime = config.magicLinkVerificationExpiryTime;
    const magiclinkVerificationTokenExpirationTime = config.magicLinkVerificationTokenExpirationTime;
    this.verificationCodeSendMessage = this.commonService.getTranslateData('VALIDATION_MESSAGES.VERIFICATION_DETAILS_AND_CODE_SEND_MESSAGE');
    if (magiclinkVerificationTokenExpirationTime && !isBlank(magiclinkVerificationTokenExpirationTime)) {
      this.sharedService.magiclinkTokenExpiry = Number(magiclinkVerificationTokenExpirationTime);
    } else {
      this.sharedService.magiclinkTokenExpiry = Constants.magiclinkTokenExpiryTime;
    }
    const { enablePatientIdentityValidation, enableAppLessHome, patientIdentityValidationFields } = config;
    this.otpSingleFieldEntry = config.otpSingleFieldEntry === '1';
    this.userId = config.userId;
    if ((+enablePatientIdentityValidation || +enableAppLessHome) && !isBlank(patientIdentityValidationFields)) {
      this.additionalFields = patientIdentityValidationFields.split(',').map((field) => field.trim());
      this.additionalFields.forEach((field) => {
        this.appLessForm.addControl(field, this.formBuilder.control('', Validators.required));
      });
      this.isIdentityValidationEnabled = enablePatientIdentityValidation === '1';
      this.isAppLessHomeEnabled = enableAppLessHome === '1' && !isBlank(this.id);
      this.verificationCodeSendMessage = this.commonService.getTranslateData('VALIDATION_MESSAGES.VERIFICATION_DETAILS');
    } else {
      this.additionalFields = [];
      this.isIdentityValidationEnabled = false;
      this.isAppLessHomeEnabled = false;
    }
    this.tenantId = config.tenantId;
    this.sendId = config.sendId;
    this.chatRoomId = config.chatRoomId;
    this.documentId = config.documentId;
    this.applessHomeNotificationsFrequency = config.applessHomeNotificationsFrequency;
    this.navigateToApplessHome = config.navigateToApplessHome;
    const applessTokenVerified = localStorageGetItemWithExpiry(Constants.storageKeys.applessDocTokenVerified);
    if (this.isAppLessHomeEnabled && !this.isTokenVerificationRequired(applessTokenVerified, config)) {
      this.accessGranted = true;
      this.verifyMobileOrEmail();
    } else if (
      this.magicLinkVerificationExpiryTime === Constants.magiclinkVerificationTokenExpirationTime &&
      !this.isIdentityValidationEnabled &&
      !this.isAppLessHomeEnabled
    ) {
      this.sharedService.trackActivity({
        type: Activity.appless,
        name: Activity.pageNavigation, // TODO need updated as Activity.applessTokenAutoVerified and add in the unauthenticatedActivities
        des: {
          data: {
            flow: this.appLessWorkFlow,
            token: this.tokenData,
            username: this.sharedService?.userData?.username
          },
          desConstant: Activity.applessTokenAutoVerifiedDes
        },
        linkageId: this.tokenData
      });
      localStorage.setItem(Constants.storageKeys.applessRememberDays, Constants.magiclinkVerificationTokenExpirationTime);
      this.accessGranted = true;
      this.verifyMobileOrEmail();
    } else if (this.appLessWorkFlow === Constants.applessFlowMode.userConsent) {
      this.accessGranted = true;
      this.verifyMobileOrEmail();
    } else if (
      this.magicLinkVerificationExpiryTime === Constants.magiclinkVerificationTokenExpirationTime
        ? (this.isAppLessHomeEnabled || this.isIdentityValidationEnabled) && this.additionalFields.length === 1
        : this.isTokenVerificationRequired(applessTokenVerified, config)
    ) {
      localStorage.removeItem(Constants.storageKeys.applessDocTokenVerified);
      localStorage.setItem(Constants.storageKeys.applessRememberDays, this.magicLinkVerificationExpiryTime);
      this.isOtpRequired = true;
      this.sendVerification().then((response) => {
        if (response.status) {
          this.showExpiryTimer(response);
          this.commonService.showMessage(response.message);
          this.verificationMobilePopupShow = true;
          this.isOtpSent = true;
          this.appLessForm.addControl(
            'otp',
            this.formBuilder.control('', [
              Validators.required,
              Validators.pattern(`^[0-9]{${this.otpSize}}$`),
              Validators.pattern(Constants.numericPatternText)
            ])
          );
          if (this.isIdentityValidationEnabled) {
            this.verificationCodeSendMessage = this.commonService.getTranslateData('VALIDATION_MESSAGES.VERIFICATION_DETAILS_AND_CODE_SEND_MESSAGE');
          } else {
            this.verificationCodeSendMessage = this.commonService.getTranslateData('VALIDATION_MESSAGES.VERIFICATION_CODE_SEND_MESSAGE');
          }
        } else {
          this.verificationMessage = response.message;
        }
      });
    } else if (!this.isAppLessHomeEnabled && !this.isIdentityValidationEnabled) {
      this.sharedService.trackActivity({
        type: Activity.appless,
        name: Activity.pageNavigation, // TODO need updated as Activity.applessTokenVerified and add in the unauthenticatedActivities
        des: {
          data: {
            flow: this.appLessWorkFlow,
            token: this.tokenData,
            username: this.sharedService?.userData?.username
          },
          desConstant: Activity.applessTokenVerifiedDes
        },
        linkageId: this.tokenData
      });
      this.accessGranted = true;
      this.verifyMobileOrEmail();
      this.isOtpSent = false;
    } else {
      this.verificationMobilePopupShow = true;
    }
  }

  /**
   * Determines if token verification is required based on stored token and configuration
   * @param applessTokenVerified The stored token value from localStorage
   * @param config The appless configuration object
   * @returns boolean indicating whether verification is required
   */
  private isTokenVerificationRequired(applessTokenVerified: string, config: ApplessConfiguration): boolean {
    return (
      isBlank(applessTokenVerified) ||
      applessTokenVerified !== config.userId ||
      this.magicLinkVerificationExpiryTime === Constants.magicLinkExpiryDefaultTime ||
      this.magicLinkVerificationExpiryTime !== localStorage.getItem(Constants.storageKeys.applessRememberDays)
    );
  }

  decodeToken() {
    const token = this.tokenData?.split('.')[1]?.replace(/-/g, '+')?.replace(/_/g, '/');
    const formData = JSON.parse(atob(token));
    this.isCampaignForm = +formData?.isCampaign === 1;
  }

  handleError(response) {
    if (response.status === Constants.loginResponseStatus.invalid && response.tokenExpire === true) {
      this.verificationMessage = this.commonService.getTranslateData(
        this.sessionService.applessMessagingFlow ? 'ERROR_MESSAGES.APPLESS_MESSAGE_LINK_EXPIRED' : 'ERROR_MESSAGES.APPLESS_LINK_EXPIRED'
      );
      this.commonService.showMessage(this.verificationMessage);
      this.trackErrorLogin(this.verificationMessage);
    } else if (response.status === Constants.loginResponseStatus.invalid && response.tokenExpire === false) {
      let message = 'ERROR_MESSAGES.INVALID_APPLESS_TOKEN';
      switch (this.appLessWorkFlow) {
        case Constants.applessFlowMode.form:
          message = 'ERROR_MESSAGES.INVALID_APPLESS_TOKEN_FORM';
          break;
        case Constants.applessFlowMode.document:
          message = 'ERROR_MESSAGES.INVALID_APPLESS_TOKEN_DOCUMENT';
          break;
        case Constants.applessFlowMode.download:
          message = 'ERROR_MESSAGES.INVALID_APPLESS_TOKEN_DOWNLOAD';
          break;
        case Constants.applessFlowMode.userConsent:
          message = 'ERROR_MESSAGES.INVALID_APPLESS_TOKEN_USER_CONSENT';
          break;
        default:
      }
      this.verificationMessage = response.message && this.isCampaignForm ? response.message : this.commonService.getTranslateData(message);
      if (!this.isCampaignForm) {
        this.commonService.showMessage(this.verificationMessage);
      }
      this.trackErrorLogin(this.verificationMessage);
    } else if (
      response.userData?.code === Constants.loginResponseStatus.invalid ||
      response.userData?.code === Constants.loginResponseStatus.Unauthorized ||
      response?.status?.code === Constants.loginResponseStatus.Unauthorized
    ) {
      this.verificationMessage =
        isPresent(response?.error?.message) && typeof response?.error?.message === 'string'
          ? response.error.message
          : this.commonService.getTranslateData('ERROR_MESSAGES.APPLESS_LINK_EXPIRED');
      this.commonService.showMessage(this.verificationMessage);
      this.trackErrorLogin(this.verificationMessage);
    } else if (response?.status === Constants.loginResponseStatus.pendingActivation) {
      this.commonService.showMessage(ConfigValues.messages.pendingAccountActivation);
      this.trackErrorLogin(response.userData?.message || '');
    } else if (response?.status === Constants.loginResponseStatus.disable || response?.status === Constants.loginResponseStatus.disableAccount) {
      const disabledMessage = this.commonService.getTranslateData('VALIDATION_MESSAGES.ACCOUNT_DISABLED_CONTACT');
      this.commonService.showMessage(disabledMessage);
      this.trackErrorLogin(response.userData?.message || '');
    } else if (response?.status === Constants.loginResponseStatus.dischargeUser) {
      this.commonService.showMessage(ConfigValues.messages.dischargedUser);
      this.trackErrorLogin(response.userData?.message || '');
    } else if ((response.status !== undefined || !response.userId) && !isBlank(response.message)) {
      // Handle the error case
      // Show message from API user not exists
      this.commonService.showMessage(response.userData?.message || '');
      this.trackErrorLogin(response.userData?.message || '');
    } else if (this.isTokenValidateAPI && response.responseStatus?.code === Constants.invalidCode) {
      const message =
        this.appLessWorkFlow === Constants.applessFlowMode.download
          ? 'ERROR_MESSAGES.INVALID_APPLESS_TOKEN_DOWNLOAD'
          : 'ERROR_MESSAGES.INVALID_APPLESS_TOKEN_USER_CONSENT';
      this.verificationMessage = this.commonService.getTranslateData(message);
      this.commonService.showMessage(this.verificationMessage);
      this.trackErrorLogin(this.verificationMessage);
    } else {
      const message = typeof response === 'string' ? 'ERROR_MESSAGES.TECHNICAL_DIFFICULTY_TRY_AGAIN' : 'ERROR_MESSAGES.INVALID_APPLESS_TOKEN';
      this.verificationMessage = this.commonService.getTranslateData(message);
      this.commonService.showMessage(this.verificationMessage);
    }
    this.sessionService.sessionLoader = false;
    this.sharedService.errorHandler(response);
  }
  trackErrorLogin(message: string): void {
    const data = {
      userName: this.sharedService?.userData?.displayName || this.tokenData,
      message
    };
    this.sharedService.trackActivity({
      type: Activity.userAccess,
      name: Activity.authException,
      des: { data, desConstant: Activity.authExceptionDescription },
      linkageId: this.tokenData
    });
  }

  resendVerification() {
    clearInterval(this.expiryInterval);
    this.sendVerification(true).then((response) => {
      if (response.status) {
        this.showExpiryTimer(response);
        this.commonService.showMessage(response.message);
        this.verificationMobilePopupShow = true;
      }
    });
  }

  sendVerification(resend = false): Promise<any> {
    return new Promise((resolve) => {
      if (this.isAppLessHomeEnabled) {
        this.sendEmailOrMobileVerification(resend).then((response) => {
          resolve(response);
        });
        return;
      }
      this.handleAppLessVerification(resend).then((response) => {
        resolve(response);
      });
    });
  }

  handleAppLessVerification(resend): Promise<any> {
    return new Promise((resolve) => {
      const { userData } = this.sharedService;
      let documentActivityParams = Constants.documentActivityParams.throughApp;
      if (resend) {
        documentActivityParams = Constants.documentActivityParams.throughUser;
      }

      if ((this.otpThroughMobile && (!userData.countryCode || !userData.mobile)) || (!this.otpThroughMobile && !userData.userName)) {
        this.sharedService.trackActivity({
          type: Activity.appless,
          name: Activity.pageNavigation, // TODO need updated as Activity.applesstokenSendCodeFail and add in the unauthenticatedActivities
          des: {
            data: {
              flow: this.appLessWorkFlow,
              token: this.tokenData,
              username: this.sharedService?.userData?.username,
              sendMedium: documentActivityParams,
              sendThrough: this.otpThroughMobile ? Activity.mobile : Activity.email
            },
            desConstant: Activity.applesstokenSendCodeFailDes
          },
          linkageId: this.tokenData
        });
        resolve({
          status: false,
          message: this.commonService.getTranslateData(
            this.otpThroughMobile ? 'VALIDATION_MESSAGES.APPLESS_MOBILE_MISSING_MESSAGE' : 'VALIDATION_MESSAGES.APPLESS_EMAIL_MISSING_MESSAGE'
          )
        });
      } else {
        this.sharedService.trackActivity({
          type: Activity.appless,
          name: Activity.pageNavigation, // TODO need updated as Activity.applesstokenSendCode and add in the unauthenticatedActivities
          des: {
            data: {
              flow: this.appLessWorkFlow,
              token: this.tokenData,
              username: this.sharedService?.userData?.username,
              sendMedium: documentActivityParams,
              sendThrough: this.otpThroughMobile ? Activity.mobile : Activity.email
            },
            desConstant: Activity.applesstokenSendCodeDes
          },
          linkageId: this.tokenData
        });
        this.sendEmailOrMobileVerification(resend).then((response) => {
          resolve(response);
        });
        const params = {
          accessCodeSentMedium: this.otpThroughMobile ? Constants.accessCodeSentMedium.mobile : Constants.accessCodeSentMedium.email,
          activityName: documentActivityParams,
          sender: '',
          appLessWorkFlow: this.appLessWorkFlow
        };
        this.applessNavigationService.saveApplessDocumentHistory(params);
      }
    });
  }

  sendEmailOrMobileVerification(resend): Promise<any> {
    const apiUrl = resend ? APIs.identityValidationReSendOtp : APIs.identityValidationSendOtp;
    const headers = { notificationId: '', token: '' };
    if (this.isAppLessHomeEnabled) {
      headers.notificationId = this.id;
    } else {
      headers.token = this.tokenData;
    }
    return new Promise((resolve) => {
      this.httpService.doPost({
        endpoint: apiUrl,
        headers,
        payload: { username: this.sharedService.userData?.userName, sessionId: resend ? this.otpSessionId : '' }
      }).subscribe({
        next: (response) => {
          resolve(response);
          this.otpSessionId = response.sessionId;
        },
        error: (error) => {
          resolve({
            status: false,
            message: error
          });
        }
      });
    });
  }

  verifyMobileOrEmail() {
    const otpCode = this.appLessForm.value.otp;
    const additionalData = this.additionalFields.reduce((acc, field) => {
      let value = this.appLessForm.value[field];
      if (field === 'DOB' && value) {
        value = moment(value, 'MM/DD/YYYY').format('YYYY-MM-DD');
      }
      acc[this.formatString(field)] = value;
      return acc;
    }, {});
    if (this.isOtpSent) {
      additionalData['otp'] = otpCode;
      additionalData['sessionId'] = this.otpSessionId;
    }
    if (this.accessGranted) {
      if (this.isAppLessHomeEnabled) {
        this.sessionService.getSessionData(true).then((data) => {
          this.sessionService.sessionLoader = false;
          if (!data?.response) {
            localStorage.removeItem(Constants.storageKeys.applessDocTokenVerified);
            window.location.reload();
          } else {
            this.handleAppLessHomeSuccessfulVerification(additionalData, data.response);
          }
        });
      } else {
        this.handleSuccessfulVerification(additionalData);
      }
    } else if (this.appLessForm.valid && (this.additionalFields.length > 0 || this.isOtpSent)) {
      if (this.isAppLessHomeEnabled) {
        additionalData['userId'] = this.userId;
        additionalData['notificationId'] = this.id;
        this.authService.doLogin(additionalData).subscribe({
          next: (result: LoginResponse) => {
            if (!isBlank(result.userId)) {
              this.handleAppLessHomeSuccessfulVerification(additionalData, result);
            } else {
              this.handleVerificationFailed(additionalData, result);
            }
          }
        });
      } else {
        this.httpService
          .doPost({
            endpoint: APIs.identityValidation,
            hasAuthToken: false,
            payload: additionalData,
            headers: {
              token: this.tokenData
            }
          })
          .subscribe((response) => {
            if (response.status) {
              this.handleSuccessfulVerification(additionalData);
            } else {
              this.handleVerificationFailed(additionalData, response);
            }
          });
      }
    }
  }

  setTokenVerifiedData(additionalData, verifiedMedium) {
    if (!this.accessGranted) {
      this.magicLinkVerificationExpiryTime = !isBlank(this.magicLinkVerificationExpiryTime)
        ? this.magicLinkVerificationExpiryTime
        : String(this.rememberConfigTime);
      const expireTime = parseInt(this.magicLinkVerificationExpiryTime, 10);
      localStorageSetItemWithExpiry(Constants.storageKeys.applessDocTokenVerified, verifiedMedium, expireTime);
      this.sharedService.trackActivity({
        type: Activity.appless,
        name: Activity.pageNavigation, // TODO need updated as Activity.applessValidCode and add in the unauthenticatedActivities
        des: {
          data: {
            flow: this.appLessWorkFlow,
            token: this.tokenData,
            request: JSON.stringify(additionalData)
          },
          desConstant: Activity.applessValidCodeDes
        },
        linkageId: this.tokenData
      });
    }
    sessionStorage.setItem(Constants.storageKeys.applessDocTokenVerified, verifiedMedium);
  }

  handleAppLessHomeSuccessfulVerification(additionalData, userData: LoginResponse) {
    this.tokenData = userData?.authenticationToken;
    localStorage.setItem(Constants.storageKeys.authToken, userData.authenticationToken);
    sessionStorage.setItem(Constants.storageKeys.appLessHomeLoggedIn, Constants.trueAsString);
    this.setupUserData(userData);
    this.setTokenVerifiedData(additionalData, userData?.userUuid);

    if (this.shouldNavigateDirectly()) {
      this.handleDirectNavigation(additionalData);
    } else {
      this.applessNavigationService.navigateToApplessHome(additionalData);
    }
    const params = {
      activityName: Constants.applessLinkClickedByRecipient,
      accessCodeSentMedium: Constants.accessCodeSentMedium.email,
      sender: this.sharedService.userData?.userId,
      documentId: Number(this.documentId),
      appLessWorkFlow: this.appLessWorkFlow
    };
    if (this.appLessWorkFlow !== Constants.applessFlowMode.form && !this.sessionService.applessMessagingFlow) {
      this.applessNavigationService.saveApplessDocumentHistory(params);
    }
  }
  private shouldNavigateDirectly(): boolean {
    return this.navigateToApplessHome === Constants.NAVIGATE_TO_APPLESS_HOME;
  }

  private handleDirectNavigation(additionalData: any): void {
    // Include all parameters in additionalData object
    const enhancedAdditionalData = {
      ...additionalData,
      appLessWorkFlow: this.appLessWorkFlow,
      navigateToApplessHome: this.navigateToApplessHome,
      sendId: this.sendId,
      documentId: this.documentId,
      tenantId: this.tenantId,
      chatRoomId: this.chatRoomId
    };

    this.applessNavigationService.handleDirectNavigationFromAppless(enhancedAdditionalData);
  }

  handleSuccessfulVerification(additionalData) {
    let verifiedMedium = {
      medium: Constants.accessCodeSentMedium.mobile,
      value: `${this.sharedService.userData?.countryCode}${this.sharedService.userData?.mobile}`
    };
    const params = {
      activityName: Constants.applessVerificationSuccessName,
      accessCodeSentMedium: Constants.accessCodeSentMedium.mobile,
      sender: this.sharedService.userData?.userId,
      appLessWorkFlow: this.appLessWorkFlow
    };
    if (!this.otpThroughMobile) {
      params.accessCodeSentMedium = Constants.accessCodeSentMedium.email;
      verifiedMedium = { medium: Constants.accessCodeSentMedium.email, value: this.sharedService.userData?.userName };
    }
    if (this.appLessWorkFlow !== Constants.applessFlowMode.form && !this.sessionService.applessMessagingFlow) {
      this.applessNavigationService.saveApplessDocumentHistory(params);
    }
    this.setTokenVerifiedData(additionalData, verifiedMedium.value);
    this.navigateToNextPage(additionalData);
  }

  handleVerificationFailed(request, response) {
    this.sharedService.trackActivity({
      type: Activity.appless,
      name: Activity.pageNavigation, // TODO need updated as Activity.applessInvalidCode and add in the unauthenticatedActivities
      des: {
        data: {
          flow: this.appLessWorkFlow,
          token: this.tokenData,
          request: JSON.stringify(request)
        },
        desConstant: Activity.applessInvalidCodeDes
      },
      linkageId: this.tokenData
    });
    this.commonService.showMessage(response.message);
  }

  navigateToNextPage(additionalData) {
    const navigationExtras: NavigationExtras = {
      skipLocationChange: true,
      state: additionalData
    };
    this.sharedService.trackActivity({
      type: Activity.appless,
      name: Activity.pageNavigation,
      des: {
        data: {
          token: this.tokenData,
          appLessWorkFlow: this.appLessWorkFlow,
          sessionAuthToken: getValueFromSession(Constants.storageKeys.authToken),
          userDataAuthToken: this.sharedService.userData?.authenticationToken
        },
        desConstant: Activity.applessNavigationInitiated
      },
      linkageId: this.tokenData
    });
    switch (this.appLessWorkFlow) {
      case Constants.applessFlowMode.form:
        const form = this.formDetails;
        form.interactionChannel = Constants.appless;
        navigationExtras.state = {
          viewData: { form },
          appLessResponse: this.verifyTokenResponse,
          formStatus: Constants.formPendingStatus
        };
        this.router.navigate([`.${PageRoutes.viewForms}`], navigationExtras);
        break;
      case Constants.applessFlowMode.document:
        this.router.navigate(
          [`document-center/view-document/${this.sharedService.applessDocumentId}/${this.verifyTokenResponse?.senderTenant}`],
          navigationExtras
        );
        break;
      case Constants.applessFlowMode.download:
        let endPoint = '';
        const fileName = this.verifyTokenResponse?.completedFileName || '';
        if (Number(this.verifyTokenResponse?.sourceId) === Constants.sourceId.form) {
          endPoint = APIs.formDownload;
        } else if (Number(this.verifyTokenResponse?.sourceId) === Constants.sourceId.document) {
          endPoint = APIs.documentDownload;
        } else {
          this.sharedService.trackActivity({
            type: Activity.appless,
            name: Activity.pageNavigation,
            des: {
              data: {
                token: this.tokenData,
                sourceId: this.verifyTokenResponse?.sourceId
              },
              desConstant: Activity.applessInvalidSourceIdDes
            },
            linkageId: this.tokenData
          });
        }
        navigationExtras.state = {
          downloadLink: endPoint,
          extraParams: {
            applessFileToken: this.verifyTokenResponse?.fileToken
          },
          fileName,
          isExternalLink: false
        };
        this.router.navigate([PageRoutes.download], navigationExtras);
        break;
      case Constants.applessFlowMode.visitView:
        navigationExtras.state = {
          data: {
            visitKey: this.sessionService.tokenData?.visitKey,
            visitCalendarData: this.sessionService.tokenData,
            disableCalendarView: true
          }
        };
        this.router.navigate([`${PageRoutes.scheduleCenterVisitsView}/${this.sessionService.tokenData?.visitKey}`], navigationExtras);
        break;
      case Constants.applessFlowMode.userConsent:
        navigationExtras.state = {
          patientData: this.verifyTokenResponse?.patientData
        };
        this.router.navigate([PageRoutes.userConsent], navigationExtras);
        break;
      default:
        if (this.sessionService.applessMessagingFlow) {
          this.router.navigate([`/message-center/messages/active/chat/${this.sessionService.tokenData?.chatRoomId}`], {
            skipLocationChange: this.sharedService.userData?.isVirtual
          });
        }
    }
  }

  formatString(input: string): string {
    try {
      if (typeof input !== 'string') {
        throw new Error('Input is not a string');
      }
      return input.toLowerCase().replace(/_/g, '');
    } catch (error) {
      console.error('Error formatting string:', error.message);
      return String(input);
    }
  }

  getSubTitle(): string {
    let subTitle = '';
    switch (this.appLessWorkFlow) {
      case Constants.applessFlowMode.document:
        subTitle = 'TITLES.APPLESS_DOCUMENT_SUB_TITLE';
        break;
      case Constants.applessFlowMode.form:
        subTitle = 'TITLES.APPLESS_FORM_SUB_TITLE';
        break;
      case Constants.applessFlowMode.download:
        subTitle = 'TITLES.APPLESS_DOWNLOAD_SUB_TITLE';
        break;
      case Constants.applessFlowMode.message:
        subTitle = 'TITLES.APPLESS_MESSAGE_SUB_TITLE';
        break;
      case Constants.applessFlowMode.visitView:
        subTitle = 'TITLES.APPLESS_VISIT_SUB_TITLE';
        break;
      default:
    }
    return subTitle;
  }

  showExpiryTimer(res): void {
    let otpExpiryTime = res.otpExpiryTime;
    this.mobileVerificationTimerText = ''
    this.resendOtp = false;
    this.ngZone.runOutsideAngular(() => {
      this.ngZone.run(() => {
        this.expiryInterval = setInterval(() => {
          otpExpiryTime -= 1;
          const minutes: number = Math.floor(otpExpiryTime / 60);
          this.mobileVerificationTimerText = this.addZeroForMmSs(minutes) + ':' + this.addZeroForMmSs(otpExpiryTime - minutes * 60);
          if (otpExpiryTime === 0) {
            this.resendOtp = true;
            clearInterval(this.expiryInterval);
          }
        }, 1000);
      });
    });
  }

  addZeroForMmSs(value: number): string {
    return value < 10 ? `0${value}` : `${value}`;
  }

  handleOtpChange(event: any) {
    const otpArray = event;
    this.appLessForm.get('otp').setValue(otpArray);
  }

  onDateChange(event: any) {
    const date = event.detail.value;
    const formattedDate = moment(date).format('MM/DD/YYYY');
    this.appLessForm.get('DOB').setValue(formattedDate);
  }

  formatDateInput(event: any, field: string) {
    let value = event.target.value || '';
    const cursor = event.target.selectionStart;
    const prev = this.appLessForm.get(field)?.value || '';

    // Handle backspace
    if (event.inputType === 'deleteContentBackward') {
      // If backspacing after a slash, remove slash and previous digit
      if (prev[cursor - 1] === '/') {
        value = prev.slice(0, cursor - 2) + prev.slice(cursor);
        this.appLessForm.get(field).setValue(value);
        setTimeout(() => event.target.setSelectionRange(cursor - 2, cursor - 2));
        return;
      }
      // Regular backspace
      value = prev.slice(0, cursor - 1) + prev.slice(cursor);
      this.appLessForm.get(field).setValue(value);
      setTimeout(() => event.target.setSelectionRange(cursor - 1, cursor - 1));
      return;
    }

    // Strip non-digits
    value = value.replace(/\D/g, '');

    // Auto-insert slashes
    if (value.length > 2) {
      value = `${value.slice(0, 2)}/${value.slice(2)}`;
    }
    if (value.length > 5) {
      value = `${value.slice(0, 5)}/${value.slice(5)}`;
    }
    // Limit length to MM/DD/YYYY
    value = value.slice(0, 10);

    // Update control
    this.appLessForm.get(field).setValue(value);

    // Move cursor forward if slash was auto-inserted
    setTimeout(() => {
      let newPos = cursor;
      if ((value.length === 3 && prev.length === 2) || (value.length === 6 && prev.length === 5)) {
        newPos += 1;
      }
      event.target.setSelectionRange(newPos, newPos);
    });

    // Validate date only if full (10 characters) length is entered
    if (value.length === 10) {
      const date = moment(value, 'MM/DD/YYYY');
      if (!date.isValid()) {
        this.appLessForm.get(field).setErrors({ invalidDate: true });
      } else {
        this.appLessForm.get(field).setErrors(null);
      }
    } else if (value.length > 0 && value.length < 10) {
      this.appLessForm.get(field).setErrors({ invalidDate: true });
    }
  }

  handleKeyDownSubmit(event: any) {
    if (event.key === 'Enter') {
      this.verifyMobileOrEmail();
    }
  }

  trackAppLessMagicLinkAccess(): void {
    this.sharedService.trackActivity({
      type: Activity.appless,
      name: Activity.urlFirstHit,
      des: {
        data: {
          flow: this.appLessWorkFlow,
          notificationId: this.id
        },
        desConstant: Activity.applessPageDescId
      },
      linkageId: this.id
    });
  }
}
