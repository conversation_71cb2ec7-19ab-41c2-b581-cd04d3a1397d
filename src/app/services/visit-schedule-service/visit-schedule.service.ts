import { Injectable } from '@angular/core';
import { APIs } from 'src/app/constants/apis';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { HttpService } from '../http-service/http.service';
import { ApiResponse, AttachmentData } from 'src/app/interfaces/schedule-center';
import { isBlank } from 'src/app/utils/utils';

@Injectable({
  providedIn: 'root'
})
export class VisitScheduleService {
  constructor(private httpService: HttpService) { }

  manageVisit(reqBody?: any): Observable<any> {
    const url = APIs.manageVisit;
    const body = reqBody;
    return this.httpService.doPost({
      endpoint: url,
      payload: JSON.stringify(body),
      useBaseAs: 'visit-schedule'
    });
  }

  createAvailability(reqBody?: any): Observable<any> {
    const url = APIs.availability;
    const body = reqBody;
    return this.httpService.doPost({
      endpoint: url,
      payload: JSON.stringify(body),
      useBaseAs: 'visit-schedule'
    });
  }

  getAvailabilityData(reqBody?: any, singleSelect?: boolean): Observable<any> {
    let url;
    let body;
    if (singleSelect) {
      url = `${APIs.availability}${'/'}${reqBody}`;
      body = {};
    } else {
      url = APIs.availability;
      body = reqBody;
    }
    return this.httpService.doGet({
      endpoint: url,
      extraParams: body,
      useBaseAs: 'visit-schedule'
    });
  }

  manageAvailabilityData(id: any, param: any) {
    const url = `${APIs.availability}${'/'}${id}`;
    return this.httpService.doPut({
      endpoint: url,
      payload: param,
      useBaseAs: 'visit-schedule'
    });
  }

  getLocationTypes(reqBody?: any): Observable<any> {
    const url = APIs.visitTypes;
    return this.httpService.doGet({
      endpoint: url,
      useBaseAs: 'visit-schedule'
    });
  }

  getUserList(reqBody?: any): Observable<any> {
    const url = APIs.userList;
    const body = reqBody;
    return this.httpService.doGet({
      endpoint: url,
      extraParams: body,
      useBaseAs: 'visit-schedule'
    });
  }

  getVisits(reqBody?: any, visitKey?: string): Observable<any> {
    let url;
    let body;
    if (visitKey) {
      url = `${APIs.visits}${'/'}${visitKey}`;
      body = {};
    } else {
      url = APIs.visits;
      body = reqBody;
    }
    return this.httpService.doGet({
      endpoint: url,
      extraParams: body,
      useBaseAs: 'visit-schedule'
    });
  }

  fileAttach(reqBody: FormData): Observable<ApiResponse<AttachmentData[]>> {
    const url = APIs.attachment;
    const body = reqBody;
    return this.httpService.visitSchedulefileUpload(body, url);
  }

  manageVisitData(visitKey: any, param: any, loader = false) {
    const url = `${APIs.visits}${'/'}${visitKey}`;
    return this.httpService.doPut({
      endpoint: url,
      payload: param,
      useBaseAs: 'visit-schedule',
      loader
    });
  }

  getChatForVisitPaitent(visitKey: string): Observable<ApiResponse<any>> {
    const url = APIs.visitChat.replace('{{visitKey}}', visitKey);
    return this.httpService.doGet({
      endpoint: url,
      useBaseAs: 'visit-schedule'
    });
  }

  checkVisitAvailability(reqBody): Observable<any> {
    const url = `${APIs.visits}${'/'}${APIs.checkAvailability}`;
    const body = reqBody;
    return this.httpService.doPost({
      endpoint: url,
      payload: JSON.stringify(body),
      useBaseAs: 'visit-schedule'
    });
  }

  setDefaultView(view: string, viewPreference: string, timezone: string): Observable<any> {
    let url = `${APIs.userSettings}${'/'}${view}`;
    const queryParams: string[] = [];
    if (!isBlank(viewPreference)) {
      queryParams.push(`visitPreferenceSettings=${encodeURIComponent(viewPreference)}`);
    }
    if (!isBlank(timezone)) {
      queryParams.push(`timezonePreferenceSettings=${encodeURIComponent(timezone)}`);
    }
    if (queryParams.length > 0) {
      url += `?${queryParams.join('&')}`;
    }

    return this.httpService.doPut({
      endpoint: url,
      useBaseAs: 'visit-schedule'
    });
  }

  oldToNewSeries(param: any) {
    const url = `${APIs.oldToNewSeriesEndpoint}`;
    return this.httpService.doPut({
      endpoint: url,
      payload: param,
    });
  }

  searchVisitLocations(reqBody?: any): Observable<any> {
    const url = `${APIs.visitLocationLookup}`;
    return this.httpService.doPost({
      endpoint: url,
      payload: reqBody,
    });
  }

  searchVisitChairs(reqBody?: any): Observable<any> {
    const url = `${APIs.visitChairLookup.replace('{visitLocationId}', reqBody.visitLocationId)}`;
    return this.httpService.doPost({
      endpoint: url,
      payload: reqBody,
    });
  }

  getVisitTypeLocation(visitLocationId) {
    const url = `${APIs.getVisitLocation.replace('{id}', visitLocationId)}`;
    return this.httpService.doGet({
      endpoint: url,
    });
  }

  getVisitChair(visitChairId) {
    const url = `${APIs.getVisitChair.replace('{id}', visitChairId)}`;
    return this.httpService.doGet({
      endpoint: url,
    });
  }

  getTherapyTypes(): Observable<any> {
    const url = `${APIs.therapyTypesEndpoint}`;
    return this.httpService.doGet({
      endpoint: url,
      useBaseAs: 'visit-schedule'
    });
  }

  searchTherapyTypes(reqBody?: any): Observable<any> {
    const url = `${APIs.therapyTypesEndpoint}`;
    return this.httpService.doPost({
      endpoint: url,
      payload: reqBody,
    }).pipe(
      map((response) => {
        return {
          ...response,
          content: response.content.filter((item) => item.status)
        }
      })
    )
  }

  searchTherapies(reqBody?: any): Observable<any> {
    const url = `${APIs.therapiesEndpoint.replace('{therapyTypeId}', reqBody.therapyTypeId)}`;
    return this.httpService.doPost({
      endpoint: url,
      payload: reqBody,
    }).pipe(
      map((response) => {
        return {
          ...response,
          content: response.content.filter((item) => item.status)
        }
      })
    );
  }

  getTherapiesByType(therapyTypeId: number): Observable<any> {
    const url = `${APIs.therapiesEndpoint}`;
    const params = { therapyTypeId: therapyTypeId };
    return this.httpService.doGet({
      endpoint: url,
      extraParams: params,
      useBaseAs: 'visit-schedule'
    });
  }

  getTherapyTypeById(id: number): Observable<any> {
    const url = `${APIs.visitTherapyTypeByIdEndpoint.replace('{id}', id.toString())}`;
    return this.httpService.doGet({
      endpoint: url
    });
  }

  getTherapyById(id: number): Observable<any> {
    const url = `${APIs.visitTherapyByIdEndpoint.replace('{id}', id.toString())}`;
    return this.httpService.doGet({
      endpoint: url
    });
  }

  getTherapies(therapyTypeId: any): Observable<any> {
    // This is just an alias for getTherapiesByType for more consistent naming
    return this.getTherapiesByType(therapyTypeId);
  }

  startStopVisit(visitKey, reqBody) {
    const url = `${APIs.visitUpdateActualTime}${'/'}${visitKey}`;
    const body = reqBody;
    return this.httpService.doPut({
      endpoint: url,
      payload: JSON.stringify(body),
      useBaseAs: 'visit-schedule'
    });
  }

  /**
   * Get therapy type by id
   * @param therapyTypeId therapy type id
   * @returns ApiResponse<any>
   */
  getTherapyType(therapyTypeId: string): Observable<ApiResponse<any>> {
    const url = `${APIs.visitTherapyTypeByIdEndpoint.replace('{id}', therapyTypeId)}`;
    return this.httpService.doGet({
      endpoint: url,
    });
  }

  /**
   * Get dosage appointment by id
   * @param dosageAppointmentId dosage appointment id
   * @returns ApiResponse<any>
   */
  getDosageAppointment(dosageAppointmentId: string): Observable<ApiResponse<any>> {
    const url = `${APIs.visitTherapyByIdEndpoint.replace('{id}', dosageAppointmentId)}`;
    return this.httpService.doGet({
      endpoint: url,
    });
  }
}
