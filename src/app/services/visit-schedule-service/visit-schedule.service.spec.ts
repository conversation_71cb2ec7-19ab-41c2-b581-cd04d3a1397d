import { HttpClientModule } from '@angular/common/http';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { IonicModule } from '@ionic/angular';
import { Observable, of } from 'rxjs';
import { APIs } from 'src/app/constants/apis';
import { HttpService } from '../http-service/http.service';
import { VisitScheduleService } from './visit-schedule.service';

describe('VisitScheduleService', () => {
  let service: VisitScheduleService;
  let httpService: HttpService;
  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [HttpService],
      imports: [IonicModule.forRoot(), HttpClientModule, HttpClientTestingModule]
    });
    httpService = TestBed.inject(HttpService);
    service = TestBed.inject(VisitScheduleService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
  describe('manageVisit', () => {
    it('manageVisit function should be defined', () => {
      spyOn(httpService, 'doPost').and.returnValue(of(''));
      service.manageVisit({});
      expect(service.manageVisit).toBeDefined();
    });
  });
  describe('setDefaultView', () => {
    it('should send an HTTP PUT request with the correct URL and options', () => {
      const view = 'dashboard';
      spyOn(httpService, 'doPut').and.returnValue(of({}));
      service.setDefaultView(view, 'preference', 'timezone').subscribe();
      expect(httpService.doPut).toBeDefined();
    });

    it('should return an Observable', () => {
      const view = 'dashboard';
      spyOn(httpService, 'doPut').and.returnValue(of({}));
      const result = service.setDefaultView(view, 'preference', 'timezone');
      expect(result instanceof Observable).toBeTrue();
    });

    it('should construct the correct URL', () => {
      const view = 'dashboard';
      const viewPreference = 'preference';
      const timezone = 'timezone';
      const expectedUrl = `${APIs.userSettings}/${view}?visitPreferenceSettings=${encodeURIComponent(viewPreference)}&timezonePreferenceSettings=${encodeURIComponent(timezone)}`;
      spyOn(httpService, 'doPut').and.returnValue(of({}));
      service.setDefaultView(view, viewPreference, timezone).subscribe();
      expect(httpService.doPut).toHaveBeenCalledWith(jasmine.objectContaining({ endpoint: expectedUrl }));
    });
  });
  describe('createAvailability', () => {
    it('should send an HTTP POST request with the correct URL, payload, and options', () => {
      const reqBody = { name: 'John Doe', availability: '10 AM - 5 PM' };
      spyOn(httpService, 'doPost').and.returnValue(of({}));
      service.createAvailability(reqBody).subscribe();
      expect(httpService.doPost).toBeDefined();
    });

    it('should return an Observable', () => {
      const reqBody = { name: 'John Doe', availability: '10 AM - 5 PM' };
      spyOn(httpService, 'doPost').and.returnValue(of({}));
      const result = service.createAvailability(reqBody);
      expect(result instanceof Observable).toBeTrue();
    });

    it('should use the correct URL', () => {
      const reqBody = { name: 'John Doe', availability: '10 AM - 5 PM' };
      const expectedUrl = APIs.availability;
      spyOn(httpService, 'doPost').and.returnValue(of({}));
      service.createAvailability(reqBody).subscribe();
      expect(httpService.doPost).toHaveBeenCalledWith(jasmine.objectContaining({ endpoint: expectedUrl }));
    });

    it('should send the correct payload', () => {
      const reqBody = { name: 'John Doe', availability: '10 AM - 5 PM' };
      const expectedPayload = JSON.stringify(reqBody);
      spyOn(httpService, 'doPost').and.returnValue(of({}));
      service.createAvailability(reqBody).subscribe();
      expect(httpService.doPost).toHaveBeenCalledWith(jasmine.objectContaining({ payload: expectedPayload }));
    });
  });
  describe('checkVisitAvailability', () => {
    it('should send an HTTP POST request with the correct URL, payload, and options', () => {
      const reqBody = { visitId: '123', date: '2023-06-30' };
      spyOn(httpService, 'doPost').and.returnValue(of({}));
      service.checkVisitAvailability(reqBody).subscribe();
      expect(httpService.doPost).toBeDefined();
    });
    it('should return an Observable', () => {
      const reqBody = { visitId: '123', date: '2023-06-30' };
      spyOn(httpService, 'doPost').and.returnValue(of({}));
      const result = service.checkVisitAvailability(reqBody);
      expect(result instanceof Observable).toBeTrue();
    });
    it('should use the correct URL', () => {
      const reqBody = { visitId: '123', date: '2023-06-30' };
      const expectedUrl = `${APIs.visits}/${APIs.checkAvailability}`;
      spyOn(httpService, 'doPost').and.returnValue(of({}));
      service.checkVisitAvailability(reqBody).subscribe();
      expect(httpService.doPost).toHaveBeenCalledWith(jasmine.objectContaining({ endpoint: expectedUrl }));
    });
    it('should send the correct payload', () => {
      const reqBody = { visitId: '123', date: '2023-06-30' };
      const expectedPayload = JSON.stringify(reqBody);
      spyOn(httpService, 'doPost').and.returnValue(of({}));
      service.checkVisitAvailability(reqBody).subscribe();
      expect(httpService.doPost).toHaveBeenCalledWith(jasmine.objectContaining({ payload: expectedPayload }));
    });
  });
  describe('getChatForVisitPaitent', () => {
    it('should send an HTTP GET request with the correct URL', () => {
      const visitKey = 'abc123';
      const expectedUrl = APIs.visitChat.replace('{{visitKey}}', visitKey);

      spyOn(httpService, 'doGet').and.returnValue(of({}));

      service.getChatForVisitPaitent(visitKey).subscribe();

      expect(httpService.doGet).toHaveBeenCalledWith(jasmine.objectContaining({ endpoint: expectedUrl }));
    });
  });
  describe('manageVisitData', () => {
    it('should send an HTTP PUT request with the correct URL and payload', () => {
      const visitKey = 'abc123';
      const param = { status: 'completed' };
      const expectedUrl = `${APIs.visits}/${visitKey}`;
      const expectedPayload = param;
      spyOn(httpService, 'doPut').and.returnValue(of({}));
      service.manageVisitData(visitKey, param).subscribe();
      expect(httpService.doPut).toHaveBeenCalledWith(
        jasmine.objectContaining({ endpoint: expectedUrl, payload: expectedPayload })
      );
    });
    it('should return an Observable', () => {
      const visitKey = 'abc123';
      const param = { status: 'completed' };
      spyOn(httpService, 'doPut').and.returnValue(of({}));
      const result = service.manageVisitData(visitKey, param);
      expect(result).toBeDefined();
    });
  });
  describe('visitSchedulefileUpload', () => {
    it('should send an HTTP POST request with the correct URL and body', () => {
      const reqBody = new FormData();
      reqBody.append('file', new File(['sample content'], 'sample.txt'));
      const expectedUrl = APIs.attachment;
      const expectedBody = reqBody;
      spyOn(httpService, 'visitSchedulefileUpload').and.returnValue(of({}));
      service.fileAttach(reqBody).subscribe();
      expect(httpService.visitSchedulefileUpload).toHaveBeenCalledWith(expectedBody, expectedUrl);
    });
  });
  describe('getUserList', () => {
    it('should send an HTTP GET request with the correct URL and body', () => {
      const reqBody = { param1: 'value1', param2: 'value2' };
      const expectedUrl = APIs.userList;
      const expectedBody = reqBody;

      spyOn(httpService, 'doGet').and.returnValue(of({}));

      service.getUserList(reqBody).subscribe();

      expect(httpService.doGet).toHaveBeenCalledWith(
        jasmine.objectContaining({ endpoint: expectedUrl, extraParams: expectedBody })
      );
    });
  });
  describe('getLocationTypes', () => {
    it('should send an HTTP GET request with the correct URL', () => {
      const expectedUrl = APIs.visitTypes;
      spyOn(httpService, 'doGet').and.returnValue(of({}));
      service.getLocationTypes().subscribe();
      expect(httpService.doGet).toHaveBeenCalledWith(jasmine.objectContaining({ endpoint: expectedUrl }));
    });
  });
  describe('manageAvailabilityData', () => {
    it('should send an HTTP PUT request with the correct URL and payload', () => {
      const id = '12345';
      const param = { status: 'available' };
      const expectedUrl = `${APIs.availability}/${id}`;
      const expectedPayload = param;
      spyOn(httpService, 'doPut').and.returnValue(of({}));
      service.manageAvailabilityData(id, param).subscribe();
      expect(httpService.doPut).toHaveBeenCalledWith(
        jasmine.objectContaining({ endpoint: expectedUrl, payload: expectedPayload })
      );
    });
  });
  describe('getAvailabilityData', () => {
    it('should send an HTTP GET request with the correct URL and body when singleSelect is false', () => {
      const reqBody = { param1: 'value1', param2: 'value2' };
      const singleSelect = false;
      const expectedUrl = APIs.availability;
      const expectedBody = reqBody;

      spyOn(httpService, 'doGet').and.returnValue(of({}));

      service.getAvailabilityData(reqBody, singleSelect).subscribe();

      expect(httpService.doGet).toHaveBeenCalledWith(
        jasmine.objectContaining({ endpoint: expectedUrl, extraParams: expectedBody })
      );
    });

    it('should send an HTTP GET request with the correct URL and body when singleSelect is true', () => {
      const reqBody = '12345';
      const singleSelect = true;
      const expectedUrl = `${APIs.availability}/${reqBody}`;
      const expectedBody = {};
      spyOn(httpService, 'doGet').and.returnValue(of({}));
      service.getAvailabilityData(reqBody, singleSelect).subscribe();
      expect(httpService.doGet).toHaveBeenCalledWith(
        jasmine.objectContaining({ endpoint: expectedUrl, extraParams: expectedBody })
      );
    });
  });
  describe('getVisits', () => {
    it('should send an HTTP GET request with the correct URL and body when visitKey is provided', () => {
      const reqBody = { param1: 'value1', param2: 'value2' };
      const visitKey = '12345';
      const expectedUrl = `${APIs.visits}/${visitKey}`;
      const expectedBody = {};
      spyOn(httpService, 'doGet').and.returnValue(of({}));
      service.getVisits(reqBody, visitKey).subscribe();
      expect(httpService.doGet).toHaveBeenCalledWith(
        jasmine.objectContaining({ endpoint: expectedUrl, extraParams: expectedBody })
      );
    });

    it('should send an HTTP GET request with the correct URL and body when visitKey is not provided', () => {
      const reqBody = { param1: 'value1', param2: 'value2' };
      const expectedUrl = APIs.visits;
      const expectedBody = reqBody;
      spyOn(httpService, 'doGet').and.returnValue(of({}));
      service.getVisits(reqBody).subscribe();
      expect(httpService.doGet).toHaveBeenCalledWith(
        jasmine.objectContaining({ endpoint: expectedUrl, extraParams: expectedBody })
      );
    });
  });

  it('should send an HTTP PUT request with the correct data and options for oldToNewSeries', () => {
    const view = {
      newSeriesId: '12345',
      oldSeriesEndDate: '03-19-2024',
      oldSeriesId: 121211212
    };
    spyOn(httpService, 'doPut').and.returnValue(of({}));
    service.oldToNewSeries(view).subscribe();
    expect(httpService.doPut).toBeDefined();
  });

  it('should send an HTTP PUT request with the correct data and options  for startStopVisit', () => {
    const params = {
      editType: 2,
      actualTimeOut: '2024-03-14T10:45:00Z',
      timeOutLatLong: '36.778259,-119.417931',
      timeOutAddress: 'Ahmedabad'
    };
    spyOn(httpService, 'doPut').and.returnValue(of({}));
    service.startStopVisit(12121221212, params).subscribe();
    expect(httpService.doPut).toBeDefined();
  });

  it('should send an HTTP POST request with the correct data for searchVisitLocations', () => {
    const params = {
      currentPage: 0,
      rowsPerPage: 25,
      searchString: '',
      sortBy: '',
      sortDirection: 'ASC',
      status: 1,
      visitLocationId: 121212
    };
    spyOn(httpService, 'doPost').and.returnValue(of({}));
    service.searchVisitLocations(params).subscribe();
    expect(httpService.doPost).toBeDefined();
  });

  it('should send an HTTP POST request with the correct data for searchVisitChairs', () => {
    const params = {
      currentPage: 0,
      rowsPerPage: 25,
      searchString: '',
      sortBy: '',
      sortDirection: 'ASC',
      status: 1,
      visitLocationId: 121212
    };
    spyOn(httpService, 'doPost').and.returnValue(of({}));
    service.searchVisitChairs(params).subscribe();
    expect(httpService.doPost).toBeDefined();
  });

  it('should send an HTTP GET request with the correct data for getVisitTypeLocation', () => {
    spyOn(httpService, 'doGet').and.returnValue(of({}));
    service.getVisitTypeLocation(12121221212).subscribe();
    expect(httpService.doGet).toBeDefined();
  });

  it('should send an HTTP GET request with the correct data for getVisitChair', () => {
    spyOn(httpService, 'doGet').and.returnValue(of({}));
    service.getVisitChair(12121221212).subscribe();
    expect(httpService.doGet).toBeDefined();
  });
});
