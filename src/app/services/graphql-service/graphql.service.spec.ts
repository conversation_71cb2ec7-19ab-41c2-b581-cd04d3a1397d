import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import {
  NgxPermissionsService,
  NgxPermissionsStore,
  USE_PERMISSIONS_STORE
} from 'ngx-permissions';
import { TranslateTestingModule } from 'src/app/services/translate-testing.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { RouterModule } from '@angular/router';
import { IonicModule } from '@ionic/angular';
import { Apollo } from 'apollo-angular';
import { TestBed } from '@angular/core/testing';

import { GraphqlService } from './graphql.service';
import { ApolloTestingModule } from 'apollo-angular/testing';
import { TestConstants } from 'src/app/constants/test-constants';
import { Observable, of } from 'rxjs';
import { Config } from 'src/app/constants/config';

describe('GraphqlService', () => {
  let apollo: Apollo;
  let graphQlService: GraphqlService;
  let sharedService: SharedService;
  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        Apollo,
        NgxPermissionsService,
        NgxPermissionsStore,
        SharedService,
        Idle,
        IdleExpiry,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      imports: [
        IonicModule.forRoot(),
        RouterModule.forRoot([]),
        HttpClientModule,
        HttpClientTestingModule,
        TranslateTestingModule,
        ApolloTestingModule
      ]
    });
    sharedService = TestBed.inject(SharedService);
    Object.defineProperties(sharedService, TestConstants.sharedServiceTestProperties);
    graphQlService = TestBed.inject(GraphqlService);
    apollo = TestBed.inject(Apollo);
  });

  it('should be created', () => {
    expect(graphQlService).toBeTruthy();
  });

  it('should call getDocumentsCount', () => {
    graphQlService.getDocumentsCount();
    expect(apollo.use).toBeDefined();
    expect(graphQlService.getDocumentsCount).toBeDefined();
  });

  it('should call getDocumentsDetailedCounts', () => {
    graphQlService.getDocumentsDetailedCounts();
    expect(apollo.use).toBeDefined();
    expect(graphQlService.getDocumentsDetailedCounts).toBeDefined();
  });

  it('should call getDocuments', () => {
    graphQlService.getDocuments({});
    expect(apollo.use).toBeDefined();
    expect(graphQlService.getDocuments).toBeDefined();
  });

  it('should call getDocumentDetails', () => {
    graphQlService.getDocumentDetails(123, 124);
    expect(apollo.use).toBeDefined();
    expect(graphQlService.getDocumentDetails).toBeDefined();
  });

  it('should call signatureRequestTypesDisplay', () => {
    graphQlService.signatureRequestTypesDisplay();
    expect(apollo.use).toBeDefined();
    expect(graphQlService.signatureRequestTypesDisplay).toBeDefined();
  });

  it('should call folderFilingCenterContent', () => {
    graphQlService.folderFilingCenterContent('demo', 'test');
    expect(apollo.use).toBeDefined();
    expect(graphQlService.folderFilingCenterContent).toBeDefined();
  });

  it('should call getStaffsByRoles', () => {
    sharedService.userData = TestConstants.userData;
    graphQlService.getStaffsByRoles('demo');
    expect(apollo.use).toBeDefined();
    expect(graphQlService.getStaffsByRoles).toBeDefined();
  });

  it('should call getDelegatedRolesByRoleId', () => {
    sharedService.userData = TestConstants.userData;
    graphQlService.getDelegatedRolesByRoleId(12345);
    expect(apollo.use).toBeDefined();
    expect(graphQlService.getDelegatedRolesByRoleId).toBeDefined();
  });

  it('should call getExternalSystems', () => {
    graphQlService.getExternalSystems();
    expect(apollo.use).toBeDefined();
    expect(graphQlService.getExternalSystems).toBeDefined();
  });

  it('should call expireToken', () => {
    graphQlService.expireToken();
    expect(apollo.use).toBeDefined();
    expect(graphQlService.expireToken).toBeDefined();
  });

  it('should call deleteVideoData', () => {
    sharedService.userData = TestConstants.userData;
    graphQlService.deleteVideoData({});
    expect(apollo.use).toBeDefined();
    expect(graphQlService.deleteVideoData).toBeDefined();
  });

  it('should call createVideoRoom', () => {
    sharedService.userData = TestConstants.userData;
    graphQlService.createVideoRoom({});
    expect(apollo.use).toBeDefined();
    expect(graphQlService.createVideoRoom).toBeDefined();
  });

  it('should call siteDetails', () => {
    graphQlService.siteDetails(246);
    expect(apollo.use).toBeDefined();
    expect(graphQlService.siteDetails).toBeDefined();
  });

  it('should call siteDetails with admission id when multi admissions enabled', () => {
    sharedService.userData = TestConstants.userData;
    sharedService.userData.config[Config.enableMultiAdmissions] = '1';
    graphQlService.siteDetails(246, 'ae682e36-4938-4ba6-b490-f894b0203485');
    expect(apollo.use).toBeDefined();
    expect(graphQlService.siteDetails).toBeDefined();
  });

  it('should call resendSignatureDocument', () => {
    graphQlService.resendSignatureDocument(88190);
    expect(apollo.use).toBeDefined();
    expect(graphQlService.resendSignatureDocument).toBeDefined();
  });

  it('should call cancelSignatureDocumentTenant', () => {
    graphQlService.cancelSignatureDocumentTenant(88190, 'Canceling document');
    expect(apollo.use).toBeDefined();
    expect(graphQlService.cancelSignatureDocumentTenant).toBeDefined();
  });

  it('should call archiveSignatureDocumentTenant', () => {
    graphQlService.archiveSignatureDocumentTenant(88190, 7100, 'archive');
    expect(apollo.use).toBeDefined();
    expect(graphQlService.archiveSignatureDocumentTenant).toBeDefined();
  });

  it('should call getExternalSystemsByPatient', () => {
    sharedService.userData = TestConstants.userData;
    graphQlService.getExternalSystemsByPatient(881190);
    expect(apollo.use).toBeDefined();
    expect(graphQlService.getExternalSystemsByPatient).toBeDefined();
  });

  it('should return the expected value when siteFCMappings returns data', (done) => {
    const mockResponse = {
      data: {
        siteFcMappings: [
          {
            filingCenterSiteConfig: {
              fromFilingCenter: 'ExpectedValue'
            }
          }
        ]
      }
    };
    spyOn(graphQlService, 'siteFCMappings').and.returnValue(of(mockResponse as any));

    graphQlService.fcmMapping({}, 'typeId', 'patientId').then((result) => {
      expect(result).toEqual('ExpectedValue');
      done();
    });
  });

  it('should return an empty string when siteFCMappings does not return data', (done) => {
    const mockResponse = {
      data: {
        siteFcMappings: []
      }
    };
    spyOn(graphQlService, 'siteFCMappings').and.returnValue(of(mockResponse as any));

    graphQlService.fcmMapping({}, 'typeId', 'patientId').then((result) => {
      expect(result).toEqual('');
      done();
    });
  });

  it('should handle errors and return false', (done) => {
    spyOn(graphQlService, 'siteFCMappings').and.returnValue(
      new Observable((observer) => {
        observer.error('Error');
      })
    );
    graphQlService.fcmMapping({}, 'typeId', 'patientId').then((result) => {
      expect(result).toEqual(false);
      done();
    });
  });

  it('should call siteListByUserId', () => {
    sharedService.userData = TestConstants.userData;
    graphQlService.siteListByUserId(88190, 71003);
    expect(apollo.use).toBeDefined();
    expect(graphQlService.siteListByUserId).toBeDefined();
  });
  it('should call getFileList', () => {
    graphQlService.getFileList({});
    expect(apollo.use).toBeDefined();
    expect(graphQlService.getFileList).toBeDefined();
  });
  // signatureRequestTextsDisplaySearch
  describe('signatureRequestTextsDisplaySearch', () => {
    it('should call signatureRequestTextsDisplaySearch', () => {
      graphQlService.signatureRequestTextsDisplaySearch(
        {
          signatureRequestRecipients: {
            roles: '',
            searchValue: '',
            siteId: '',
            admissionId: '',
            filterByPatientId: 21
          }
        },
        ''
      );
      expect(apollo.use).toBeDefined();
      expect(graphQlService.signatureRequestTextsDisplaySearch).toBeDefined();
    });
  });
  // completeDocumentSignatureRequest
  describe('completeDocumentSignatureRequest', () => {
    it('should call completeDocumentSignatureRequest', () => {
      graphQlService.completeDocumentSignatureRequest(123, 124, true);
      expect(apollo.use).toBeDefined();
      expect(graphQlService.completeDocumentSignatureRequest).toBeDefined();
    });
  });
  // signatureRequestTextsDisplay
  describe('signatureRequestTextsDisplay', () => {
    it('should call signatureRequestTextsDisplay', () => {
      graphQlService.signatureRequestTextsDisplay({});
      expect(apollo.use).toBeDefined();
      expect(graphQlService.signatureRequestTextsDisplay).toBeDefined();
    });
  });
  // copyToFilingCenterAfterSignature
  describe('copyToFilingCenterAfterSignature', () => {
    it('should call copyToFilingCenterAfterSignature', () => {
      graphQlService.copyToFilingCenterAfterSignature({ enableMultisite: '1' });
      expect(apollo.use).toBeDefined();
      expect(graphQlService.copyToFilingCenterAfterSignature).toBeDefined();
    });
  });
});
