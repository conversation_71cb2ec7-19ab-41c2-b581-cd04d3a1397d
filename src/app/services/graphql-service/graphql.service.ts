import { Injectable } from '@angular/core';
import { Apollo } from 'apollo-angular';
import { ApolloQueryResult } from '@apollo/client/core';
import { Queries } from 'src/app/constants/graphql/queries.graphql';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { Constants } from 'src/app/constants/constants';
import { Mutations } from 'src/app/constants/graphql/mutations.graphql';
import { Observable } from 'rxjs';
import { SiteDetailsResponse, FCMappingsResponse, SignatureRequestRecipients } from 'src/app/interfaces/document-request-signature';
import { isBlank, isPresent } from 'src/app/utils/utils';
import { Config } from 'src/app/constants/config';

@Injectable({
  providedIn: 'root'
})
export class GraphqlService {
  constructor(
    private readonly apollo: Apollo,
    private readonly sharedService: SharedService
  ) { }

  getDocumentsCount(): any {
    return this.apollo.use('middleware')?.watchQuery<any>({
      query: Queries.getSignatureRequestUnreadCount,
      variables: { sessionId: this.sharedService.userData.authenticationToken },
      fetchPolicy: 'network-only'
    });
  }

  getDocumentsDetailedCounts(): any {
    return this.apollo.use('middleware')?.watchQuery<any>({
      query: Queries.mySignatureRequestCount,
      variables: {
        sessionId: this.sharedService.userData.authenticationToken,
        startDate: '',
        endDate: ''
      },
      fetchPolicy: 'network-only'
    });
  }

  getDocuments(params: any): Observable<ApolloQueryResult<any>> {
    if (params.notifyOnSubmit) {
      return this.apollo.use('middleware')?.query<any>({
        query: Queries.mySignatureRequestById(this.sharedService.isMultiAdmissionsEnabled),
        variables: {
          sessionId: this.sharedService.userData.authenticationToken,
          tenantId: params.tenantId,
          crossTenantId: params.crossTenantId,
          signatureRequestFilterInput: {
            signatureStatus: params.signatureStatus ? params.signatureStatus : 'SIGNED',
            searchText: ''
          },
          site_id: params.siteId,
          documentId: params.documentId
        },
        fetchPolicy: 'network-only'
      });
    }
    return this.apollo.use('middleware')?.query<any>({
      query: Queries.mySignatureRequest(this.sharedService.isMultiAdmissionsEnabled),
      variables: {
        sessionId: this.sharedService.userData.authenticationToken,
        signatureRequestFilterInput: params.signatureRequestFilterInput,
        paginationInput: params.paginationInput,
        site_id: params.siteId,
        crossTenantId: params.crossTenantId,
        tenantId: params.tenantId,
        startDate: params.startDate,
        endDate: params.endDate
      },
      fetchPolicy: 'network-only'
    });
  }

  getDocumentDetails(id: number, senderTenant: number): Observable<ApolloQueryResult<any>> {
    return this.apollo.use('middleware')?.query<any>({
      query: Queries.signatureRequest(this.sharedService.isMultiAdmissionsEnabled),
      variables: {
        sessionId: this.sharedService.userData.authenticationToken,
        id,
        crossTenantId: senderTenant
      },
      fetchPolicy: 'no-cache'
    });
  }

  signatureRequestTypesDisplay(): any {
    return this.apollo.use('middleware')?.watchQuery<any>({
      query: Queries.signatureRequestTypesDisplay,
      variables: {
        sessionId: this.sharedService.userData.authenticationToken,
        device: this.sharedService.platformValue === Constants.platform.web ? Constants.platform.desktop : Constants.platform.mobile
      },
      fetchPolicy: 'network-only'
    });
  }

  folderFilingCenterContent(folder: string, searchKey: string): any {
    return this.apollo.use('middlewareSync')?.watchQuery<any>({
      query: Queries.folderFilingCenterContent,
      variables: {
        sessionId: this.sharedService.userData.authenticationToken,
        type: Constants.incoming,
        folder,
        searchKey
      },
      fetchPolicy: 'network-only'
    });
  }

  getStaffsByRoles(roles: any): any {
    return this.apollo?.watchQuery<any>({
      query: Queries.getRoleBasedStaffs,
      variables: {
        sessionToken: this.sharedService.userData.authenticationToken,
        status: 0,
        type: 1,
        searchText: '',
        selectedBranchId: parseInt(this.sharedService.userData.tenantId, 10),
        ...roles
      },
      fetchPolicy: 'network-only'
    });
  }
  getExternalSystems(): any {
    return this.apollo?.watchQuery<any>({
      query: Queries.getExternalSystems,
      variables: {
        sessionToken: this.sharedService.userData?.authenticationToken
      }
    });
  }

  getDelegatedRolesByRoleId(id: number): any {
    return this.apollo?.watchQuery<any>({
      query: Queries.getDelegatedStaffRole,
      variables: {
        sessionToken: this.sharedService.userData.authenticationToken,
        roleId: id
      }
    });
  }

  resendSignatureDocument(id: number): any {
    return this.apollo.use('middleware')?.mutate<any>({
      mutation: Mutations.resendSignatureDocument,
      variables: {
        sessionId: this.sharedService.userData.authenticationToken,
        id
      }
    });
  }

  cancelSignatureDocumentTenant(id: number, reason: string | null) {
    const variables: any = {
      sessionId: this.sharedService.userData.authenticationToken,
      id,
      action: 'CANCEL'
    };

    // Only include cancellationReason if reason is provided
    if (reason) {
      variables.cancellationReason = reason;
    }

    return this.apollo.use('middleware')?.mutate<any>({
      mutation: Mutations.cancelSignatureDocumentTenant,
      variables
    });
  }

  archiveSignatureDocumentTenant(id: number, crossTenantId: number, action: string): any {
    return this.apollo.use('middleware')?.mutate<any>({
      mutation: Mutations.archiveSignatureDocumentTenant,
      variables: {
        sessionId: this.sharedService.userData.authenticationToken,
        id,
        action,
        crossTenantId
      }
    });
  }
  // Type = "OnBehalf", Type = "Associated", Type = "Recipients"
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  signatureRequestTextsDisplaySearch(params: { signatureRequestRecipients: SignatureRequestRecipients }, type: string): any {
    const variables: {
      sessionId: string;
      roles: string;
      search: string;
      site_id: string;
      admissionId: string;
      filterByPatientId?: number;
    } = {
      sessionId: this.sharedService.userData.authenticationToken,
      roles: params.signatureRequestRecipients.roles,
      search: params.signatureRequestRecipients.searchValue || '',
      site_id: params.signatureRequestRecipients.siteId || '0',
      admissionId: params.signatureRequestRecipients?.admissionId || ''
    };
    if (params.signatureRequestRecipients.filterByPatientId) {
      // if patient is associated, then if alternate contacts or patients are in recipient roles, then filter by patient id.
      variables.filterByPatientId = params.signatureRequestRecipients.filterByPatientId;
    }
    const showVirtualOnly =
      type !== Constants.signatureRequestActionType.onBehalf &&
      (this.sharedService.isEnableConfig(Config.enableApplessModel) || type === Constants.signatureRequestActionType.associated);
    const isTypeList = type !== Constants.signatureRequestActionType.associated && type !== Constants.signatureRequestActionType.onBehalf;
    // additional keys for isTypeList true : userName,mobile,countryCode,careGiver{ id displayName userName firstName lastName mobile dob }
    // TODO identify if there is any impacts if keys always returned from API instead of checking isTypeList
    const query = Queries.getSignatureRequestTextDisplaySearch(showVirtualOnly, isTypeList);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return this.apollo.use('middleware')?.query<any>({ query, variables, fetchPolicy: 'network-only' });
  }

  signatureRequestTextsDisplay(params: any): any {
    return this.apollo.use('middleware')?.query<any>({
      query: Queries.signatureRequestTextsDisplay,
      variables: {
        sessionId: this.sharedService.userData.authenticationToken,
        roles: params.roles,
        siteId: params.siteId,
        admissionId: params.admissionId
      },
      fetchPolicy: 'network-only'
    });
  }
  completeDocumentSignatureRequest(documentId: number, signaturePalette: any, isFileFromGallery?: boolean): any {
    const variables: any = {
      sessionId: this.sharedService.userData.authenticationToken,
      documentId,
      signaturePalette
    };

    // Only include isFileFromGallery if it's defined
    if (isFileFromGallery !== undefined) {
      variables.isFileFromGallery = isFileFromGallery;
    }

    return this.apollo.use('middleware')?.mutate<any>({
      mutation: Mutations.completeDocumentSignatureRequest,
      variables
    });
  }
  getExternalSystemsByPatient(patientId: number, admissionId = ''): any {
    return this.apollo?.watchQuery<any>({
      query: Queries.getExternalSystemsByPatient(this.sharedService.isMultiAdmissionsEnabled),
      variables: {
        sessionToken: this.sharedService.userData.authenticationToken,
        id: patientId,
        admissionId: this.sharedService.isMultiAdmissionsEnabled && !isBlank(admissionId) ? admissionId : ''
      }
    });
  }
  copyToFilingCenterAfterSignature(params: any): any {
    let variables: {
      sessionId: string;
      fileUrlDownload: string;
      fileTargetNameUpdated: string;
      ownerIdCopyFilingCenter: number;
      exchangeData: string;
      type: string;
      folderNameCopyFilingCenter: string;
      enableMultisite?: string;
      siteId?: number;
    } = {
      sessionId: this.sharedService.userData.authenticationToken,
      fileUrlDownload: params.fileUrlDownload,
      fileTargetNameUpdated: params.fileTargetNameUpdated,
      ownerIdCopyFilingCenter: params.ownerIdCopyFilingCenter,
      exchangeData: params.exchangeData,
      type: Constants.outgoing,
      folderNameCopyFilingCenter: params.folderNameCopyFilingCenter
    };
    if (params.enableMultisite) {
      variables = { ...variables, enableMultisite: Constants.enableMultiSite, siteId: params.siteId };
    }
    return this.apollo.use('middlewareSync')?.mutate<any>({
      mutation: params.enableMultisite ? Mutations.copyToFilingCenterAfterSignatureMultisite : Mutations.copyToFilingCenterAfterSignature,
      variables
    });
  }
  signDocument(params: any): any {
    return this.apollo.use('middleware')?.mutate<any>({
      mutation: Mutations.signDocument,
      variables: {
        sessionId: this.sharedService.userData.authenticationToken,
        snapAndSaveApprove: params.snapAndSaveApprove,
        contentID: params.contentID,
        pageCount: params.pageCount,
        associateSignatureProcess: params.associateSignatureProcess,
        checkboxImageBehavior: params.checkboxImageBehavior,
        completeSignatureProcess: params.completeSignatureProcess,
        approveSignatureProcess: params.approveSignatureProcess,
        documentOwner: params.documentOwner,
        documentName: params.documentName,
        signatureData: params.signatureData,
        documentpageSize: params.documentpageSize,
        signatureCrossTenant: params.signatureCrossTenant
      }
    });
  }
  patientExternalInfo(params: any): any {
    return this.apollo.use('middleware')?.watchQuery<any>({
      query: Queries.getPatientExternalInfo,
      variables: {
        sessionId: params.sessionId,
        crossTenantId: params.crossTenantId,
        startRow: params.startRow,
        endRow: params.endRow,
        sorting: params.sorting,
        rowGroups: params.rowGroups,
        filter: params.filter,
        groupKeys: params.groupKeys,
        widgetField: params.widgetField,
        accessSecurityType: this.sharedService.userData.accessSecurityType,
        accessSecurityIdentifierType: this.sharedService.userData.accessSecurityIdentifierType,
        accessSecurityEsiValue: this.sharedService.userData.accessSecurityEsiValue
      },
      fetchPolicy: 'network-only'
    });
  }

  patientDetails(params: any) {
    return this.apollo?.query<any>({
      query: Queries.getPatientDetails,
      variables: {
        sessionToken: params.sessionToken,
        patientUsersId: params.patientUsersId
      },
      fetchPolicy: 'network-only'
    });
  }

  patientDocuments(params: any) {
    return this.apollo?.query<any>({
      query: Queries.getPatientDocuments,
      variables: {
        sessionToken: params.sessionToken,
        patientId: params.patientId,
        docType: 'doc',
        startRow: params.startRow,
        endRow: params.endRow,
        sorting: [
          {
            colId: 'id',
            sort: 'desc'
          }
        ],
        filter: [
          {
            column: 'description',
            filter: params.searchText,
            type: 'contains',
            filterCondition: 'OR'
          },
          {
            column: 'docCategory',
            filter: params.searchText,
            type: 'contains',
            filterCondition: 'OR'
          },
          {
            column: 'fileName',
            filter: params.searchText,
            type: 'contains',
            filterCondition: 'OR'
          }
        ]
      },
      fetchPolicy: 'network-only'
    });
  }

  addressSearch(params: any) {
    return this.apollo?.query<any>({
      query: Queries.getAddressSearchData,
      variables: {
        admissionId: params?.admissionId || '',
        sessionToken: params.sessionId,
        patientId: params.patientId,
        tenantId: params.tenantId,
        startRow: params.startRow,
        endRow: params.endRow,
        sorting: [
          {
            colId: 'id',
            sort: 'desc'
          }
        ],
        filter: [
          {
            column: 'city',
            type: 'contains',
            filter: params.searchText,
            filterCondition: 'OR'
          },
          {
            column: 'line1',
            type: 'contains',
            filter: params.searchText,
            filterCondition: 'OR'
          },
          {
            column: 'line2',
            type: 'contains',
            filter: params.searchText,
            filterCondition: 'OR'
          },
          {
            column: 'city',
            type: 'contains',
            filter: params.searchText,
            filterCondition: 'OR'
          },
          {
            column: 'state',
            type: 'contains',
            filter: params.searchText,
            filterCondition: 'OR'
          },
          {
            column: 'country',
            type: 'contains',
            filter: params.searchText,
            filterCondition: 'OR'
          },
          {
            column: 'district',
            type: 'contains',
            filter: params.searchText,
            filterCondition: 'OR'
          },
          {
            column: 'zipCode',
            type: 'contains',
            filter: params.searchText,
            filterCondition: 'OR'
          }
        ]
      },
      fetchPolicy: 'network-only'
    });
  }

  siteDetails(siteID: any, admissionId = '') {
    return this.apollo.use('multisite')?.query<any>({
      query: Queries.getSiteDetails(this.sharedService.isMultiAdmissionsEnabled),
      variables: {
        siteId: siteID,
        admissionId: this.sharedService.isMultiAdmissionsEnabled ? admissionId : undefined,
        configs: Constants.siteDetailsConfigValues
      }
    });
  }

  siteFCMappings(params: any) {
    const query = isPresent(params.patient) ? Queries.getSiteFCMappingsPatient : Queries.getSiteFCMappings;
    return this.apollo.use('multisite')?.query<any>({
      query,
      variables: params
    });
  }

  siteTenantFilingCenterContent(params: any) {
    return this.apollo.use('middlewareSync')?.query<any>({
      query: Queries.getSiteTenantFilingCenterContent,
      variables: {
        sessionId: params.sessionId,
        type: params.type,
        folder: params.folder,
        guid: params.guid,
        searchKey: params.searchKey,
        fromCrud: params.fromCrud
      },
      fetchPolicy: 'network-only'
    });
  }

  createVideoRoom(params: any): any {
    return this.apollo?.mutate<any>({
      mutation: Mutations.createVideoRoom,
      variables: {
        sessionToken: this.sharedService.userData.authenticationToken,
        chatroomId: params.chatroomId,
        params: {
          user: params.user,
          tenant: params.tenant
        }
      }
    });
  }

  getMessageDeleteUndoHistory(params: { messageId: number }) {
    return this.apollo.query<any>({
      query: Queries.getDeleteUndoHistoryByMessageId,
      variables: params,
      fetchPolicy: 'network-only'
    });
  }

  sendAppLessVideoData(appLessParams: any): any {
    return this.apollo?.mutate<any>({
      mutation: Mutations.sendAppLessVideoRoom,
      variables: {
        sessionToken: this.sharedService.userData.authenticationToken,
        chatroomId: appLessParams.chatroomId,
        params: {
          chatroomId: appLessParams.chatroomId,
          roomKey: appLessParams.roomKey,
          roomPin: appLessParams.roomPin,
          roomName: appLessParams.roomName,
          roomEntity: appLessParams.roomEntity,
          host: appLessParams.host,
          action: appLessParams.action || '',
          videoId: appLessParams.videoId || 0,
          forwardUser: appLessParams.forwardUser || 0
        }
      }
    });
  }

  updateUser(params: any): Observable<any> {
    const isPatient = this.sharedService.loggedUserIsPatient();
    return this.apollo?.mutate<Observable<any>>({
      mutation: Mutations.updateUser(isPatient),
      variables: {
        id: Number(this.sharedService.userData.userId),
        mobile: params.mobile,
        countryCode: params.countryCode,
        countryIsoCode: params.countryIsoCode,
        languages: params.languages,
        enableAutoHideWebNotifications: params.enableAutoHideWebNotifications,
        enableSmsNotifications: params.enableSmsNotifications,
        enableEmailNotifications: params.enableEmailNotifications,
        name: params.name,
        notificationSoundName: params.notificationSoundName,
        profileUpdate: true,
        isCaregiver: params.removeIsCaregiverKey // isCaregiver key not needed while update user signature from the document signature
          ? undefined
          : this.sharedService.userData.roleName.toLowerCase() === Constants.userCaregiver ||
            this.sharedService.userData.roleName === Constants.alternateContact,
        sessionToken: this.sharedService.userData.authenticationToken,
        defaultSitesFilter: params.defaultSitesFilter,
        encodedUserSignature: params.encodedUserSignature,
        useSavedSign: params.useSavedSign,
        ...(isPatient
          ? {}
          : { displayName: params.displayName, firstName: params.firstName, lastName: params.lastName, outOfOfficeInfo: params.outOfOfficeInfo })
      }
    });
  }

  deleteVideoData(params: any): any {
    return this.apollo?.mutate<any>({
      mutation: Mutations.deleteVideoRoom,
      variables: {
        sessionToken: this.sharedService?.userData?.authenticationToken,
        chatroomId: params.chatroomId
      }
    });
  }
  createDocumentActivity(params: any): any {
    return this.apollo.use('middleware')?.mutate<any>({
      mutation: Mutations.createDocumentActivity,
      variables: {
        sessionId: this.sharedService.userData.authenticationToken,
        activityName: params.activityName,
        documentId: params.documentId,
        sender: params.sender,
        recipient: params.recipient,
        senderName: params.senderName,
        recipientName: params.recipientName,
        recipientEmail: params.recipientEmail,
        recipientPhoneNumber: params.recipientPhoneNumber,
        recipientCountryCode: params.recipientCountryCode,
        sentDocumentVia: 'APP_LESS',
        accessCodeSentMedium: params.accessCodeSentMedium
      }
    });
  }
  expireToken(): any {
    return this.apollo.use('middleware')?.query<any>({
      query: Queries.expireToken,
      variables: {
        token: this.sharedService.userData.authenticationToken
      }
    });
  }

  siteListByUserId(crossTenantId: number, userId: number): any {
    return this.apollo.query<any>({
      query: Queries.siteListByUserId,
      variables: { userId, crossTenantId },
      fetchPolicy: 'network-only'
    });
  }

  fetchSites(siteId: number, typeId, patientId = undefined, admissionId = ''): Promise<any> {
    return new Promise((resolve, reject) => {
      this.siteDetails(siteId, admissionId)?.subscribe(
        (response: SiteDetailsResponse) => {
          if (response && response.data.getSiteDetails) {
            resolve(this.fcmMapping(response.data.getSiteDetails, typeId, patientId));
          } else {
            resolve(false);
          }
        },
        (error) => {
          resolve(false);
        }
      );
    });
  }

  fcmMapping(response: any, typeId, patientId?): Promise<any> {
    let passParams: any = {
      docRefType: Constants.documentTagTypeValue,
      docRefTypeId: Number(typeId),
      siteRegistrationId: response.registrationId
    };
    if (isPresent(patientId) && typeof Number(patientId) == 'number') {
      passParams.patient = Number(patientId);
    }
    return new Promise((resolve, reject) => {
      this.siteFCMappings(passParams)?.subscribe(
        (result: FCMappingsResponse) => {
          const receivedData = result.data.siteFcMappings[0];
          resolve(
            receivedData?.filingCenterSiteConfig && receivedData?.filingCenterSiteConfig?.fromFilingCenter
              ? receivedData.filingCenterSiteConfig.fromFilingCenter
              : ''
          );
        },
        (error) => {
          resolve(false);
        }
      );
    });
  }

  getFileList(params) {
    return this.apollo.use('middleware')?.query({
      query: Queries.documents,
      variables: {
        limit: params.limit,
        page: params.page,
        searchText: params.searchText,
        sessionId: params.sessionId,
        siteIds: params.siteIds,
        tagId: params.tagId
      },
      fetchPolicy: 'network-only'
    });
  }
}
