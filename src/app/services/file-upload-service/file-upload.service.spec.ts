import { TestBed } from '@angular/core/testing';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { CommonService } from 'src/app/services/common-service/common.service';
import { HttpService } from 'src/app/services/http-service/http.service';
import { Constants } from 'src/app/constants/constants';
import { Activity } from 'src/app/constants/activity';
import { TestConstants } from 'src/app/constants/test-constants';
import { HttpClientModule } from '@angular/common/http';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { IonicModule } from '@ionic/angular';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { Keepalive } from '@ng-idle/keepalive';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { TranslateTestingModule } from '../translate-testing.module';
import { FileUploadService } from './file-upload.service';

describe('FileUploadService', () => {
  let service: FileUploadService;
  let sharedService: SharedService;
  let commonService: CommonService;
  let httpService: HttpService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        FileUploadService,
        SharedService,
        CommonService,
        HttpService,
        NgxPermissionsService,
        NgxPermissionsStore,
        SharedService,
        Idle,
        IdleExpiry,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      imports: [IonicModule.forRoot(), RouterModule.forRoot([]), HttpClientModule, HttpClientTestingModule, TranslateTestingModule]
    });

    service = TestBed.inject(FileUploadService);
    sharedService = TestBed.inject(SharedService);
    commonService = TestBed.inject(CommonService);
    httpService = TestBed.inject(HttpService);

    spyOn(sharedService, 'trackActivity');
    spyOn(commonService, 'getTranslateDataWithParam');
    spyOn(commonService, 'getTranslateData');
    spyOn(commonService, 'getFileType');
    spyOn(httpService, 'doPost');
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('validateFile', () => {
    it('should return valid for a valid file', () => {
      const file = new File(['test content'], 'test.jpg', { type: 'image/jpeg' });
      (commonService.getTranslateDataWithParam as jasmine.Spy).and.returnValue('File size error');
      (commonService.getTranslateData as jasmine.Spy).and.returnValue('File format error');

      const result = service.validateFile(file, ['jpg', 'jpeg', 'png']);

      expect(result.valid).toBeTrue();
      expect(result.error).toBeUndefined();
    });

    it('should return invalid for a file exceeding size limit', () => {
      const largeFile = new File([new ArrayBuffer(Constants.maxFileUploadSize + 1000)], 'large.jpg', { type: 'image/jpeg' });
      const errorMessage = 'File size error';
      (commonService.getTranslateDataWithParam as jasmine.Spy).and.returnValue(errorMessage);

      const result = service.validateFile(largeFile, ['jpg', 'jpeg', 'png']);

      expect(result.valid).toBeFalse();
      expect(result.error).toBe(errorMessage);
      expect(commonService.getTranslateDataWithParam).toHaveBeenCalledWith('ERROR_MESSAGES.FILE_UPLOADSIZE', {
        maxFileSize: Constants.maxFileSize
      });
    });

    it('should return invalid for a file with disallowed extension', () => {
      const file = new File(['test content'], 'test.exe', { type: 'application/x-msdownload' });
      const errorMessage = 'File format error';
      (commonService.getTranslateData as jasmine.Spy).and.returnValue(errorMessage);

      const result = service.validateFile(file, ['jpg', 'jpeg', 'png']);

      expect(result.valid).toBeFalse();
      expect(result.error).toBe(errorMessage);
      expect(commonService.getTranslateData).toHaveBeenCalledWith('ERROR_MESSAGES.FILE_FORMAT');
    });

    it('should return invalid for a file with HTML injection in name', () => {
      const file = new File(['test content'], 'test<script>alert(1)</script>.jpg', { type: 'image/jpeg' });
      const errorMessage = 'Invalid input error';
      (commonService.getTranslateData as jasmine.Spy).and.returnValue(errorMessage);

      const result = service.validateFile(file, ['jpg', 'jpeg', 'png']);

      expect(result.valid).toBeFalse();
      expect(result.error).toBe(errorMessage);
      expect(commonService.getTranslateData).toHaveBeenCalledWith('ERROR_MESSAGES.ERROR_MSG_INVALID_INPUT');
    });
  });

  describe('processSelectedFiles', () => {
    it('should process and filter valid files', () => {
      const validFile = new File(['content'], 'valid.jpg', { type: 'image/jpeg' });
      const invalidFile = new File(['content'], 'invalid.exe', { type: 'application/x-msdownload' });
      const files = [validFile, invalidFile];

      spyOn(service, 'validateFile').and.callFake((file) => {
        if (file.name === 'valid.jpg') {
          return { valid: true };
        }
        return { valid: false, error: 'Invalid file type' };
      });

      const result = service.processSelectedFiles(files, ['jpg', 'jpeg', 'png']);

      expect(result.validFiles.length).toBe(1);
      expect(result.invalidFiles.length).toBe(1);
      expect(result.errors.length).toBe(1);
      expect(result.validFiles[0].name).toBe('valid.jpg');
      expect(result.invalidFiles[0]).toBe('invalid.exe');
    });

    it('should return error when too many files are selected', () => {
      // Create an array of 11 files (exceeding limit of 10)
      const files: File[] = [];
      for (let i = 0; i < Constants.maxFileLength + 1; i += 1) {
        files.push(new File(['content'], `file${i}.jpg`, { type: 'image/jpeg' }));
      }

      const errorMessage = 'Too many files selected';
      (commonService.getTranslateDataWithParam as jasmine.Spy).and.returnValue(errorMessage);

      const result = service.processSelectedFiles(files, ['jpg', 'jpeg', 'png']);

      expect(result.validFiles.length).toBe(0);
      expect(result.invalidFiles.length).toBe(0);
      expect(result.errors.length).toBe(1);
      expect(result.errors[0]).toBe(errorMessage);
    });
  });

  describe('getFilePreview', () => {
    it('should resolve with data URL for valid file types', async () => {
      const file = new File(['content'], 'image.jpg', { type: 'image/jpeg' });
      (commonService.getFileType as jasmine.Spy).and.returnValue('jpgimage');
      spyOn(window, 'FileReader').and.returnValue({
        readAsDataURL: jasmine.createSpy('readAsDataURL'),
        onload: null,
        result: 'data:image/jpeg;base64,test'
      } as unknown as FileReader);

      // Simulate async FileReader behavior
      const promise = service.getFilePreview(file);
      const reader = new window.FileReader();
      const mockEvent = new ProgressEvent('load');
      Object.defineProperty(mockEvent, 'target', { value: reader });
      reader.onload(mockEvent as ProgressEvent<FileReader>);

      await expectAsync(promise).toBeResolvedTo('data:image/jpeg;base64,test');
      expect(commonService.getFileType).toHaveBeenCalledWith('image/jpeg');
    });

    it('should resolve with empty string for unsupported file types', async () => {
      const file = new File(['content'], 'doc.txt', { type: 'text/plain' });
      (commonService.getFileType as jasmine.Spy).and.returnValue('text');

      await expectAsync(service.getFilePreview(file)).toBeResolvedTo('');
    });
  });

  describe('trackFileUpload', () => {
    it('should call trackActivity with correct parameters', () => {
      Object.defineProperties(sharedService, TestConstants.sharedServiceTestProperties);

      service.trackFileUpload('test.jpg', 1024);

      expect(sharedService.trackActivity).toHaveBeenCalledWith({
        type: Activity.communication,
        name: Activity.fileUpload,
        des: {
          data: {
            displayName: 'name',
            fileName: 'test.jpg',
            fileSize: 1024
          },
          desConstant: Activity.fileUploadDes
        }
      });
    });
  });

  describe('getMimeTypeFromFileName', () => {
    it('should return correct MIME type default', () => {
      expect(service.getMimeTypeFromFileName('')).toBe('application/octet-stream');
    });
    it('should return correct MIME type for common image extensions', () => {
      expect(service.getMimeTypeFromFileName('image.jpg')).toBe('image/jpeg');
      expect(service.getMimeTypeFromFileName('image.jpeg')).toBe('image/jpeg');
      expect(service.getMimeTypeFromFileName('image.png')).toBe('image/png');
      expect(service.getMimeTypeFromFileName('image.gif')).toBe('image/gif');
      expect(service.getMimeTypeFromFileName('image.webp')).toBe('image/webp');
    });

    it('should return correct MIME type for document extensions', () => {
      expect(service.getMimeTypeFromFileName('document.doc')).toBe('application/msword');
      expect(service.getMimeTypeFromFileName('document.docx')).toBe('application/msword');
      expect(service.getMimeTypeFromFileName('spreadsheet.xls')).toBe('application/vnd.ms-excel');
      expect(service.getMimeTypeFromFileName('spreadsheet.xlsx')).toBe('application/vnd.ms-excel');
      expect(service.getMimeTypeFromFileName('presentation.ppt')).toBe('application/vnd.ms-powerpoint');
      expect(service.getMimeTypeFromFileName('presentation.pptx')).toBe('application/vnd.ms-powerpoint');
      expect(service.getMimeTypeFromFileName('document.pdf')).toBe('application/pdf');
    });

    it('should return correct MIME type for media extensions', () => {
      expect(service.getMimeTypeFromFileName('audio.mp3')).toBe('audio/mpeg');
      expect(service.getMimeTypeFromFileName('audio.wav')).toBe('audio/wav');
      expect(service.getMimeTypeFromFileName('video.mp4')).toBe('video/mp4');
      expect(service.getMimeTypeFromFileName('video.webm')).toBe('video/webm');
    });

    it('should return application/octet-stream for unknown extensions', () => {
      expect(service.getMimeTypeFromFileName('unknown.xyz')).toBe('application/octet-stream');
    });

    it('should handle filenames without extensions', () => {
      expect(service.getMimeTypeFromFileName('noextension')).toBe('application/octet-stream');
    });
  });

  describe('file type checking methods', () => {
    it('should identify image files correctly', () => {
      expect(service.isImageFile('image/jpeg')).toBeTrue();
      expect(service.isImageFile('image/png')).toBeTrue();
      expect(service.isImageFile('application/pdf')).toBeFalse();
    });

    it('should identify document files correctly', () => {
      // Test MIME types that are actually in Constants.mimeDoc
      expect(service.isDocumentFile('application/msword')).toBeTrue();
      expect(service.isDocumentFile('application/vnd.openxmlformats-officedocument.wordprocessingml.document')).toBeTrue();
      expect(service.isDocumentFile('application/vnd.ms-excel')).toBeTrue();
      expect(service.isDocumentFile('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')).toBeTrue();
      expect(service.isDocumentFile('application/excel')).toBeTrue();
      expect(service.isDocumentFile('application/msexcel')).toBeTrue();
      expect(service.isDocumentFile('application/zip')).toBeTrue();
      expect(service.isDocumentFile('application/octet-stream')).toBeTrue();
      expect(service.isDocumentFile('application/pdf')).toBeFalse();
      expect(service.isDocumentFile('application/vnd.ms-powerpoint')).toBeFalse();
      expect(service.isDocumentFile('image/jpeg')).toBeFalse();
    });

    it('should identify PDF files correctly', () => {
      expect(service.isPdfFile('application/pdf')).toBeTrue();
      expect(service.isPdfFile('application/x-download')).toBeTrue();
      expect(service.isPdfFile('image/jpeg')).toBeFalse();
      expect(service.isPdfFile('application/msword')).toBeFalse();
    });

    it('should identify audio files correctly', () => {
      expect(service.isAudioFile('audio/mpeg')).toBeTrue();
      expect(service.isAudioFile('audio/wav')).toBeTrue();
      expect(service.isAudioFile('image/jpeg')).toBeFalse();
    });

    it('should identify video files correctly', () => {
      expect(service.isVideoFile('video/mp4')).toBeTrue();
      expect(service.isVideoFile('video/webm')).toBeTrue();
      expect(service.isVideoFile('image/jpeg')).toBeFalse();
    });
  });
});
