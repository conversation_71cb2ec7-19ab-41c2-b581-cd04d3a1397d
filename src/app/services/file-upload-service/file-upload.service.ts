import { Injectable } from '@angular/core';
import { Constants } from 'src/app/constants/constants';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { CommonService } from 'src/app/services/common-service/common.service';
import { HttpService } from 'src/app/services/http-service/http.service';
import { Activity } from 'src/app/constants/activity';
import { getFileReader, isHTMLTagAvailable } from 'src/app/utils/utils';

@Injectable({
  providedIn: 'root'
})
export class FileUploadService {
  constructor(
    private readonly sharedService: SharedService,
    private readonly commonService: CommonService,
    private readonly httpService: HttpService
  ) {}

  /**
   * Validates a file for upload (checks size, type, and HTML injection)
   * @param file The file to validate
   * @param allowedFileTypes Array of allowed file extensions
   * @returns An object containing validation results and error messages
   */
  validateFile(file: File, allowedFileTypes: string[]): { valid: boolean; error?: string } {
    const fileName = file.name;
    const fileSize = file.size;
    const getFileExt = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();

    // Check file size
    if (fileSize > Constants.maxFileUploadSize) {
      const sizeErrorMessage = this.commonService.getTranslateDataWithParam('ERROR_MESSAGES.FILE_UPLOADSIZE', { maxFileSize: Constants.maxFileSize });
      return { valid: false, error: sizeErrorMessage };
    }

    // Check file type
    if (!allowedFileTypes.includes(getFileExt)) {
      return {
        valid: false,
        error: this.commonService.getTranslateData('ERROR_MESSAGES.FILE_FORMAT')
      };
    }

    // Check for HTML injection
    if (isHTMLTagAvailable(fileName)) {
      return {
        valid: false,
        error: this.commonService.getTranslateData('ERROR_MESSAGES.ERROR_MSG_INVALID_INPUT')
      };
    }

    return { valid: true };
  }

  /**
   * Process files after they've been selected by the user
   * @param files Array of files to process
   * @param allowedFileTypes Array of allowed file extensions
   * @returns An object containing valid files and any error messages
   */
  processSelectedFiles(
    files: File[],
    allowedFileTypes: string[]
  ): {
    validFiles: File[];
    invalidFiles: string[];
    errors: string[];
  } {
    const validFiles: File[] = [];
    const invalidFiles: string[] = [];
    const errors: string[] = [];

    // Check total number of files
    if (files.length > Constants.maxFileLength) {
      const fileLengthErrorMessage = this.commonService.getTranslateDataWithParam('ERROR_MESSAGES.MAX_FILE_LENGTH', {
        maxFileLength: Constants.maxFileLength
      });
      errors.push(fileLengthErrorMessage);
      return { validFiles, invalidFiles, errors };
    }

    // Validate each file
    files.forEach((file) => {
      const validation = this.validateFile(file, allowedFileTypes);
      if (validation.valid) {
        validFiles.push(file);
      } else {
        invalidFiles.push(file.name);
        if (validation.error && !errors.includes(validation.error)) {
          errors.push(validation.error);
        }
      }
    });

    return { validFiles, invalidFiles, errors };
  }

  /**
   * Get preview data for a file (e.g. for images)
   * @param file The file to preview
   * @returns Promise that resolves with the preview data
   */
  getFilePreview(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      if (Constants.checkFileExtensionType.includes(this.commonService.getFileType(file.type))) {
        const reader = getFileReader();
        reader.readAsDataURL(file);
        reader.onload = () => {
          resolve(reader.result as string);
        };
        reader.onerror = (error) => {
          reject(error);
        };
      } else {
        resolve('');
      }
    });
  }

  /**
   * Track file upload activity
   * @param fileName Name of the file being uploaded
   * @param fileSize Size of the file being uploaded
   * @param activityType Type of activity to track
   */
  trackFileUpload(fileName: string, fileSize: number, activityType: string = Activity.fileUpload): void {
    this.sharedService.trackActivity({
      type: Activity.communication,
      name: activityType,
      des: {
        data: {
          displayName: this.sharedService.userData?.displayName,
          fileName,
          fileSize
        },
        desConstant: Activity.fileUploadDes
      }
    });
  }

  /**
   * Infers the MIME type from the file extension
   * @param fileName The filename to extract extension from
   * @returns The inferred MIME type
   */
  getMimeTypeFromFileName(fileName: string): string {
    const extension = fileName.split('.').pop()?.toLowerCase();

    if (!extension) return 'application/octet-stream';

    // Image types
    if (['jpg', 'jpeg'].includes(extension)) return 'image/jpeg';
    if (extension === 'png') return 'image/png';
    if (extension === 'gif') return 'image/gif';
    if (extension === 'webp') return 'image/webp';

    // Document types
    if (['doc', 'docx'].includes(extension)) return 'application/msword';
    if (['xls', 'xlsx'].includes(extension)) return 'application/vnd.ms-excel';
    if (['ppt', 'pptx'].includes(extension)) return 'application/vnd.ms-powerpoint';
    if (extension === 'pdf') return 'application/pdf';

    // Media types
    if (extension === 'mp3') return 'audio/mpeg';
    if (extension === 'wav') return 'audio/wav';
    if (extension === 'mp4') return 'video/mp4';
    if (extension === 'webm') return 'video/webm';

    return 'application/octet-stream';
  }

  /**
   * Check if a file is an image based on its MIME type
   * @param mimeType The MIME type to check
   * @returns Boolean indicating if the file is an image
   */
  isImageFile(mimeType: string): boolean {
    return mimeType.startsWith('image/');
  }

  /**
   * Check if a file is a document based on its MIME type
   * @param mimeType The MIME type to check
   * @returns Boolean indicating if the file is a document
   */
  isDocumentFile(mimeType: string): boolean {
    return Constants.mimeDoc.includes(mimeType);
  }

  /**
   * Check if a file is a PDF based on its MIME type
   * @param mimeType The MIME type to check
   * @returns Boolean indicating if the file is a PDF
   */
  isPdfFile(mimeType: string): boolean {
    return Constants.mimePdf.includes(mimeType);
  }

  /**
   * Check if a file is an audio file based on its MIME type
   * @param mimeType The MIME type to check
   * @returns Boolean indicating if the file is an audio file
   */
  isAudioFile(mimeType: string): boolean {
    return mimeType.startsWith('audio/');
  }

  /**
   * Check if a file is a video file based on its MIME type
   * @param mimeType The MIME type to check
   * @returns Boolean indicating if the file is a video file
   */
  isVideoFile(mimeType: string): boolean {
    return mimeType.startsWith('video/');
  }
}
