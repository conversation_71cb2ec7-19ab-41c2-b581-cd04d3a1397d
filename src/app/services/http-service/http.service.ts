import { isJavaEndpoint } from 'src/app/utils/service-utils';
import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams, HttpResponse } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { map } from 'rxjs/operators';
import { Observable } from 'rxjs';
import { Constants, UseBaseAs, UseBaseTypes } from 'src/app/constants/constants';
import { isBlank } from 'src/app/utils/utils';
import { theme } from 'src/theme/theme';
import { ConfigValues } from 'src/assets/config/config';
import { getValueFromSession } from 'src/app/utils/storage-utils';
import { APIs } from 'src/app/constants/apis';

declare type ContentType = 'json' | 'form';
/**
 * This service is one of the low level services
 * Do not import any other services in to this file
 * This service used to define common http calls
 */
@Injectable({
  providedIn: 'root'
})
export class HttpService {
  private readonly apiRoot: string = environment.apiBase;
  paramsObj = {
    platform: '',
    cache: '1688714347102',
    version: ConfigValues.appVersion,
    bundleIdentifier: theme.bundleIdentifier
  };
  constructor(private readonly http: HttpClient) {}

  doGet(config: {
    endpoint: string;
    extraParams?: {};
    hasAuthToken?: boolean;
    loader?: boolean;
    version?: string;
    useBaseAs?: UseBaseAs;
  }): Observable<any> {
    const defaultConfig = {
      hasAuthToken: true,
      extraParams: {},
      loader: true,
      version: Constants.apiVersions.apiV4,
      useBaseAs: 'default'
    };
    const updatedConfig = Object.assign(defaultConfig, config);
    let headers = new HttpHeaders();
    let paramsMerged;
    if (updatedConfig.useBaseAs === 'default') {
      if (updatedConfig.hasAuthToken) {
        headers = headers.append('Authentication-Token', getValueFromSession(Constants.storageKeys.authToken));
        headers = headers.append('Content-Type', 'application/json');
        headers = headers.append('Token-type', 'Authentication-Token');
      }
      if (updatedConfig.loader) {
        headers = headers.append('loader', '1');
      }
      paramsMerged = {
        ...this.paramsObj,
        ...updatedConfig.extraParams
      };
    } else {
      if (updatedConfig.useBaseAs === UseBaseTypes.FORMS) {
        headers = headers.append('Authentication-Token', getValueFromSession(Constants.storageKeys.authToken));
        headers = headers.append('Content-Type', 'text/html');
        headers = headers.append('Token-type', 'Authentication-Token');
      } else {
        headers = headers.append('Authorization', getValueFromSession(Constants.storageKeys.authToken));
        headers = headers.append('Content-Type', 'application/json');
        headers = headers.append('Token-type', 'Authorization');
      }
      headers = headers.append('Accept', 'application/json');
      headers = headers.append('loader', '1');
      paramsMerged = {
        ...updatedConfig.extraParams
      };
    }
    const params = new HttpParams({ fromObject: paramsMerged });
    let url;
    if (updatedConfig.useBaseAs === UseBaseTypes.VISIT_SCHEDULE) {
      url = `${environment.visitScheduleApi}/${updatedConfig.endpoint}`;
    } else if (updatedConfig.useBaseAs === UseBaseTypes.FORMS) {
      url = `${environment.machFormUrl}${APIs.offlineForms.replace(/{endpoint}/g, updatedConfig.endpoint)}`;
      return this.http.get(url, { headers, params, withCredentials: false, responseType: 'text' });
    } else if (updatedConfig.useBaseAs === UseBaseTypes.VISIT_SCHEDULE_ROOT) {
      url = `${environment.visitScheduleRootApi}/${updatedConfig.endpoint}`;
    } else {
      url = isJavaEndpoint(updatedConfig.endpoint)
        ? `${environment.javaApiBase}/${updatedConfig.endpoint}`
        : `${environment.apiBasePath}/${updatedConfig.version}/${updatedConfig.endpoint}`;
    }
    return this.http.get(url, { headers, params, withCredentials: false }).pipe(map((response: any) => response));
  }
  /**
   * doFetchFile to get file as blob
   * @param url
   * @returns
   */
  doFetchFile(config: {
    endpoint: string;
    extraParams?: Record<string, string>;
    loader?: boolean;
    version?: string;
    isExternalLink?: boolean;
  }): Observable<HttpResponse<Blob>> {
    const defaultConfig = {
      extraParams: {},
      loader: true,
      version: Constants.apiVersions.apiV4,
      isExternalLink: false
    };
    const updatedConfig = Object.assign(defaultConfig, config);
    if (!updatedConfig.isExternalLink) {
      updatedConfig.endpoint = `${environment.apiBasePath}/${updatedConfig.version}/${updatedConfig.endpoint}`;
    }
    return this.http
      .get(updatedConfig.endpoint, { responseType: 'blob', observe: 'response', params: config.extraParams })
      .pipe(map((response: HttpResponse<Blob>) => response));
  }

  doFetchFileBuffer(config: {
    endpoint: string;
    extraParams?: Record<string, string>;
    loader?: boolean;
    version?: string;
    isExternalLink?: boolean;
    authToken?: string;
  }): Observable<HttpResponse<ArrayBuffer>> {
    const defaultConfig = {
      extraParams: {},
      loader: true,
      version: Constants.apiVersions.apiV4,
      isExternalLink: false
    };
    const headers = new HttpHeaders({ 'Content-Type': 'application/pdf', 'Authentication-Token': config.authToken });
    const updatedConfig = Object.assign(defaultConfig, config);
    if (!updatedConfig.isExternalLink) {
      updatedConfig.endpoint = `${environment.apiBasePath}/${updatedConfig.version}/${updatedConfig.endpoint}`;
    }
    return this.http
      .get(updatedConfig.endpoint, { params: config.extraParams, responseType: 'arraybuffer', headers, observe: 'response' })
      .pipe(map((response) => response));
  }
  doPost(config: {
    endpoint: string;
    payload: {};
    extraParams?: {};
    hasAuthToken?: boolean;
    hasOktaAuthToken?: boolean;
    parseToString?: boolean;
    responseType?: 'json' | 'text';
    contentType?: ContentType;
    loader?: boolean;
    version?: string;
    useBaseAs?: UseBaseAs;
    skipErrorHandling?: boolean;
    headers?: Record<string, string>;
  }): Observable<any> {
    const defaultConfig = {
      parseToString: false,
      hasAuthToken: true,
      hasOktaAuthToken: false,
      extraParams: {},
      contentType: 'json',
      loader: true,
      version: Constants.apiVersions.apiV4,
      useBaseAs: 'default',
      headers: {}
    };
    const updatedConfig = Object.assign(defaultConfig, config);
    let headers = new HttpHeaders(updatedConfig.headers ?? {});
    if (updatedConfig.useBaseAs === 'default') {
      headers = headers.append(
        'Content-Type',
        updatedConfig.contentType === 'json' ? 'application/json' : 'application/x-www-form-urlencoded; charset=UTF-8'
      );
      const oktaTokenStorage = JSON.parse(localStorage.getItem(Constants.storageKeys.oktaTokenStorage));
      if (oktaTokenStorage && updatedConfig.hasOktaAuthToken && isBlank(getValueFromSession(Constants.storageKeys.authToken))) {
        const { accessToken } = oktaTokenStorage.accessToken;
        headers = headers.append('Authentication-Token', accessToken);
      } else if (getValueFromSession(Constants.storageKeys.authToken) && updatedConfig.hasAuthToken) {
        headers = headers.append('Authentication-Token', getValueFromSession(Constants.storageKeys.authToken));
      }
      if (updatedConfig.loader) {
        headers = headers.append('loader', '1');
      }
    } else {
      headers = headers.append('Authorization', getValueFromSession(Constants.storageKeys.authToken));
      headers = headers.append('Content-type', 'application/json');
      headers = headers.append('Accept', 'application/json');
    }
    if (updatedConfig.skipErrorHandling) {
      headers = headers.append('skipErrorHandling', '1');
    }

    if (updatedConfig.useBaseAs !== 'default' || updatedConfig.hasAuthToken || updatedConfig.hasOktaAuthToken) {
      headers = headers.append('Token-type', updatedConfig.useBaseAs === 'default' ? 'Authentication-Token' : 'Authorization');
    }
    const paramsMerged = {
      ...updatedConfig.extraParams,
      ...this.paramsObj
    };
    const params = new HttpParams({ fromObject: paramsMerged });
    let url;
    if (updatedConfig.useBaseAs === 'visit-schedule') {
      url = `${environment.visitScheduleApi}/${updatedConfig.endpoint}`;
    } else if (updatedConfig.useBaseAs === UseBaseTypes.FORMS) {
      url = `${environment.machFormUrl}${updatedConfig.endpoint}`;
    } else {
      url = isJavaEndpoint(updatedConfig.endpoint)
        ? `${environment.javaApiBase}/${updatedConfig.endpoint}`
        : `${environment.apiBasePath}/${updatedConfig.version}/${updatedConfig.endpoint}`;
    }
    if (updatedConfig.parseToString) {
      updatedConfig.payload = new URLSearchParams(updatedConfig.payload).toString();
    }
    if (config.responseType === 'text') {
      return this.http
        .post(url, updatedConfig.payload, { headers, params, withCredentials: false, responseType: 'text' })
        .pipe(map((response: any) => response));
    }
    return this.http
      .post(url, updatedConfig.payload, {
        headers,
        params,
        withCredentials: false
      })
      .pipe(map((response: any) => response));
  }

  doDelete(config: {
    endpoint: string;
    payload: any;
    extraParams?: Record<string, any>;
    hasAuthToken?: boolean;
    contentType?: ContentType;
    loader?: boolean;
    version?: string;
  }): Observable<any> {
    const defaultConfig = {
      hasAuthToken: true,
      extraParams: {},
      contentType: 'json',
      loader: true,
      version: Constants.apiVersions.apiV4
    };
    const updatedConfig = Object.assign(defaultConfig, config);
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', updatedConfig.contentType === 'json' ? 'application/json' : 'application/x-www-form-urlencoded');
    if (updatedConfig.hasAuthToken) {
      if (getValueFromSession(Constants.storageKeys.authToken)) {
        headers = headers.append('Authentication-Token', getValueFromSession(Constants.storageKeys.authToken));
      }
      headers = headers.append('Token-type', 'Authentication-Token');
    }
    if (updatedConfig.loader) {
      headers = headers.append('loader', '1');
    }

    const paramsMerged = {
      ...updatedConfig.extraParams,
      ...this.paramsObj
    };
    const params = new HttpParams({ fromObject: paramsMerged });
    const url = `${environment.apiBasePath}/${updatedConfig.version}/${updatedConfig.endpoint}`;
    return this.http
      .delete(url, {
        headers,
        params,
        body: updatedConfig.payload,
        withCredentials: false
      })
      .pipe(map((response: any) => response));
  }

  doPostByUrl(
    config: { payload: Record<string, any>; apiUrl: string; type?: 'json' | 'string'; hasAuthToken?: boolean },
    useAuthTokenHeader = false
  ): Observable<any> {
    const defaultConfig = {
      type: 'json'
    };
    const updatedConfig = Object.assign(defaultConfig, config);
    let headers = new HttpHeaders();
    if(updatedConfig.hasAuthToken){
      headers = headers.append('Authorization', `Session ${getValueFromSession(Constants.storageKeys.authToken)}`);
      headers = headers.append('Token-type', 'Authorization');
    }
    if (useAuthTokenHeader) {
      headers = headers.append('Authentication-Token', getValueFromSession(Constants.storageKeys.authToken));
    }
    headers = headers.append('Content-Type', 'application/json');
    let payload: any = updatedConfig.payload;
    if (updatedConfig.type === 'string') {
      payload = new URLSearchParams(updatedConfig.payload).toString();
    }
    return this.http.post(updatedConfig.apiUrl, payload, { headers }).pipe(map((response: any) => response));
  }

  loadDataFromFile(filePath: string): Observable<any> {
    return this.http.get(filePath).pipe(map((response: any) => response));
  }

  formData = new FormData();
  fileUpload(config: {
    endpoint: string;
    payload: any;
    field: string;
    id: string;
    page: string;
    multiple?: {};
    socketId?: string;
    hasAuthToken?: boolean;
    parseToString?: boolean;
    contentType?: ContentType;
    loader?: boolean;
    responseType?: XMLHttpRequestResponseType;
  }): Observable<any> {
    const defaultConfig = {
      parseToString: false,
      hasAuthToken: true,
      multiple: false,
      contentType: 'json',
      loader: true
    };
    const updatedConfig = Object.assign(defaultConfig, config);
    let headers = new HttpHeaders();
    const fd = new FormData();
    if (
      config.page !== 'support' &&
      updatedConfig.hasAuthToken
    ) {
      if (getValueFromSession(Constants.storageKeys.authToken)) {
        headers = headers.append('Authentication-Token', getValueFromSession(Constants.storageKeys.authToken));
      }
      headers = headers.append('Token-type', 'Authentication-Token');
    }
    if (updatedConfig.loader) {
      headers = headers.append('loader', '1');
    }
    const url = `${this.apiRoot}/${updatedConfig.endpoint}`;
    if (config.multiple) {
      config.payload.forEach((file: any, i) => {
        if (file.e) {
          fd.append(config.field, file.e, file.e.name);
        } else {
          fd.append(config.field, file, file.name);
        }
        if (!isBlank(config.id)) {
          fd.append('user', config.id);
        }
        if (config.page === 'chat') {
          fd.append('uniqueIds[]', file.id);
        }
      });
    } else if (config.page === 'docUpload') {
      if (!isBlank(config.id)) {
        fd.append('user', config.id);
      }
      fd.append('client', config.socketId);
      if (config.field === 'file') {
        if (config.payload && !isBlank(config.payload.documentFile.name)) {
          if (config.payload.documentFile.e) {
            fd.append('file[]', config.payload.documentFile.e);
          } else {
            fd.append('file[]', this.formData.get('file'));
          }
        } else if (config.payload && config.payload.documentFile.length > 1) {
          config.payload.documentFile.forEach((element) => fd.append('file[]', element.e));
        }
      } else if (config.field === 'url') {
        fd.append('url', config.payload.documentFile);
      } else if (config.field === 'fileId') {
        fd.append('fileId', config.payload.fileId);
      }
      fd.append('sendDocumentStatus', config.payload.sendDocumentStatus);
      fd.append('argsForGql', config.payload.argsForGql);
    } else {
      fd.append(config.field, config.payload[0], config.payload[0].name);
    }
    if (config.responseType === 'text') {
      return this.http
        .post(url, fd, { headers, withCredentials: false, responseType: config.responseType })
        .pipe(map((response: any) => response));
    }
    return this.http.post(url, fd, { headers, withCredentials: false }).pipe(map((response: any) => response));
  }

  doPut(config: { endpoint: string; useBaseAs?: UseBaseAs; payload?: {}; loader?: boolean; version?: string }): Observable<any> {
    const defaultConfig = {
      useBaseAs: 'default',
      payload: {},
      loader: false,
      version: Constants.apiVersions.apiV4
    };
    const updatedConfig = Object.assign(defaultConfig, config);
    let headers = new HttpHeaders();
    headers = headers.append('Content-type', 'application/json');
    const isJava = isJavaEndpoint(updatedConfig.endpoint);
    let url = `${environment.apiBasePath}/${updatedConfig.version}/${updatedConfig.endpoint}`;
    if (defaultConfig.useBaseAs === 'visit-schedule') {
      headers = headers.append('Accept', 'application/json');
      headers = headers.append('Authorization', getValueFromSession(Constants.storageKeys.authToken));
      url = `${environment.visitScheduleApi}/${updatedConfig.endpoint}`;
    } else if (isJava) {
      headers = headers.append('Accept', 'application/json');
      headers = headers.append('Authentication-Token', getValueFromSession(Constants.storageKeys.authToken));
      url = `${environment.javaApiBase}/${updatedConfig.endpoint}`;
    } else {
      headers = headers.append('Authentication-Token', getValueFromSession(Constants.storageKeys.authToken));
    }
    headers = headers.append('Token-type', defaultConfig.useBaseAs === 'visit-schedule' ? 'Authorization' : 'Authentication-Token');
    if (updatedConfig.loader) {
      headers = headers.append('loader', '1');
    }

    return this.http.put(url, updatedConfig.payload, { headers }).pipe(map((response: any) => response));
  }

  visitSchedulefileUpload(payload: FormData, apiUrl: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', getValueFromSession(Constants.storageKeys.authToken));
    headers = headers.append('Token-type', 'Authorization');
    const url = `${environment.visitScheduleApi}/${apiUrl}`;
    return this.http.post(url, payload, { headers }).pipe(map((response: any) => response));
  }
}
