import { Constants } from 'src/app/constants/constants';
import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { TranslateTestingModule } from 'src/app/services/translate-testing.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { RouterModule } from '@angular/router';
import { IonicModule } from '@ionic/angular';
import { TestBed } from '@angular/core/testing';
import { environment } from 'src/environments/environment';
import { TestConstants } from 'src/app/constants/test-constants';
import { Urls } from 'src/app/constants/urls';
import { SendBulkMessageService } from './send-bulk-message.service';
import { FileUploadService } from '../file-upload-service/file-upload.service';
import { SharedService } from '../shared-service/shared.service';
import { CommonService } from '../common-service/common.service';

describe('SendBulkMessageService', () => {
  let service: SendBulkMessageService;
  let commonService: CommonService;
  let sharedService: SharedService;
  let fileUploadService: FileUploadService;
  const mockFileData: unknown = [
    {
      name: 'file1.jpg',
      lastModifiedDate: 'Wed Mar 24 2022 01:39:31 GMT+0530 (India Standard Time) {}',
      size: 1445,
      type: 'image/jpeg',
      uri: ''
    },
    {
      name: 'file-sample_1MB.doc',
      lastModifiedDate: 'Wed Mar 24 2022 01:39:31 GMT+0530 (India Standard Time) {}',
      size: 1445,
      type: 'application/msword',
      uri: ' https://file-examples.com/wp-content/uploads/2017/02/file-sample_1MB.doc'
    }
  ];
  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        IonicModule.forRoot(),
        RouterModule.forRoot([]),
        HttpClientModule,
        HttpClientTestingModule,
        TranslateTestingModule
      ],
      providers: [
        NgxPermissionsService,
        NgxPermissionsStore,
        FileUploadService,
        SharedService,
        CommonService,
        Idle,
        IdleExpiry,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ]
    });
    service = TestBed.inject(SendBulkMessageService);
    fileUploadService = TestBed.inject(FileUploadService);
    sharedService = TestBed.inject(SharedService);
    commonService = TestBed.inject(CommonService);
    Object.defineProperties(sharedService, TestConstants.sharedServiceTestProperties);
    // Setup common spies
    spyOn(fileUploadService, 'getMimeTypeFromFileName').and.callFake((filename) => {
      if (filename.endsWith('.jpg') || filename.endsWith('.jpeg')) {
        return 'image/jpeg';
      }
      if (filename.endsWith('.docx') || filename.endsWith('.doc')) {
        return 'application/msword';
      }
      if (filename.endsWith('.mp4')) {
        return 'video/mp4';
      }
      if (filename.endsWith('.mp3')) {
        return 'audio/mpeg';
      }
      return 'application/octet-stream';
    });
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should call fileFormatTypeTofileTag', () => {
    service.fileFormatTypeTofileTag(new Blob([mockFileData[0]]), '', mockFileData[0].name, '', () => null);
    expect(service.fileFormatTypeTofileTag).toBeDefined();
  });
  it('should call fileFormatTypeTofileTag: URL', () => {
    service.fileFormatTypeTofileTag('', 'https://picsum.photos/200/300', 'demo@call--1653721170', 'png', () => null);
    expect(service.fileFormatTypeTofileTag).toBeDefined();
  });

  it('should call createImageMessageWithTag with file', () => {
    service.createImageMessageWithTag(
      new Blob([mockFileData[0]]),
      '',
      mockFileData[0].name,
      mockFileData[0].type,
      () => {}
    );
    expect(service.createImageMessageWithTag).toBeDefined();
  });

  it('should call createImageMessageWithTag with URL', () => {
    service.createImageMessageWithTag(
      new Blob([mockFileData[0]]),
      'https://picsum.photos/200/300',
      mockFileData[0].name,
      mockFileData[0].type,
      () => {}
    );
    expect(service.createImageMessageWithTag).toBeDefined();
  });

  it('should call createVideoMessageWithTag with URL', () => {
    service.createVideoMessageWithTag(
      'https://file-examples.com/wp-content/uploads/2017/04/file_example_MP4_480_1_5MG.mp4',
      'file_example_MP4_480_1_5MG.mp4',
      'video/mp4',
      () => {}
    );
    expect(service.createVideoMessageWithTag).toBeDefined();
  });

  it('should call createAudioMessageWithTag with URL', () => {
    service.createAudioMessageWithTag(
      'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3',
      'SoundHelix-Song-1.mp3',
      'audio/mp3',
      () => {}
    );
    expect(service.createAudioMessageWithTag).toBeDefined();
  });

  it('should call createDocumentMessageWithTag with URL', () => {
    service.createDocumentMessageWithTag(
      new Blob([mockFileData[1]]),
      mockFileData[1].url,
      mockFileData[1].name,
      mockFileData[1].type,
      () => {}
    );
    expect(service.createDocumentMessageWithTag).toBeDefined();
  });

  it('should call createPdfMessageWithTag with URL', () => {
    let pdfFile: any = {
      name: 'dummy.pdf',
      lastModifiedDate: 'Wed Mar 24 2022 01:39:31 GMT+0530 (India Standard Time) {}',
      size: 1445,
      type: 'application/pdf',
      uri: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf'
    };
    service.createPdfMessageWithTag(new Blob([pdfFile]), pdfFile.url, pdfFile.name, pdfFile.type, () => {});
    expect(service.createPdfMessageWithTag).toBeDefined();
  });

  it('should call isImage', () => {
    service.isImage(mockFileData[0].type);
    expect(service.isImage).toBeTruthy();
  });

  it('should call isVideo', () => {
    service.isVideo('video/mp4');
    service.isVideo('audio/mp3');
    expect(service.isVideo).toBeTruthy();
  });

  it('should call isAudio', () => {
    service.isAudio('audio/mp3');
    service.isAudio('application/msword');
    expect(service.isAudio).toBeTruthy();
  });

  it('should call isDocument', () => {
    service.isDocument('application/msword');
    service.isDocument('application/pdf');
    expect(service.isDocument).toBeTruthy();
  });

  it('should call isPdf', () => {
    service.isPdf('application/pdf');
    service.isPdf('application/msword');
    expect(service.isPdf).toBeTruthy();
  });

  it('return image tag', () => {
    service.returnTag('https://picsum.photos/200/300', Constants.documentTypes.image);
    expect(service.returnTag).toBeDefined();
  });

  it('return other tag', () => {
    service.returnTag('https://file-examples.com/wp-content/uploads/2017/02/file-sample_1MB.doc', 'file-sample_1MB');
    expect(service.returnTag).toBeDefined();
  });

  it('return tag with url for image', () => {
    service.returnTagwithUrl(Constants.documentTypes.image, 'https://picsum.photos/200/300', 'demo');
    expect(service.returnTagwithUrl).toBeDefined();
  });

  it('return tag with url for excel', () => {
    service.returnTagwithUrl(
      Constants.documentTypes.excel,
      'https://file-examples.com/wp-content/uploads/2017/02/file_example_XLS_10.xls',
      'file_example_XLS_10'
    );
    expect(service.returnTagwithUrl).toBeDefined();
  });

  it('return tag with url for document', () => {
    service.returnTagwithUrl(
      Constants.documentTypes.document,
      'https://file-examples.com/wp-content/uploads/2017/02/file-sample_1MB.doc',
      'file-sample_1MB'
    );
    expect(service.returnTagwithUrl).toBeDefined();
  });

  it('return tag with url for pdf', () => {
    service.returnTagwithUrl(
      Constants.documentTypes.pdf,
      'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
      'dummy'
    );
    expect(service.returnTagwithUrl).toBeDefined();
  });

  it('return tag with url for video', () => {
    service.returnTagwithUrl(
      Constants.documentTypes.video,
      'https://file-examples.com/wp-content/uploads/2017/04/file_example_MP4_480_1_5MG.mp4',
      'file_example_MP4_480_1_5MG'
    );
    expect(service.returnTagwithUrl).toBeDefined();
  });

  it('return tag with url for audio', () => {
    service.returnTagwithUrl(
      Constants.documentTypes.audio,
      'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3',
      'SoundHelix-Song-1'
    );
    expect(service.returnTagwithUrl).toBeDefined();
  });

  describe('constructFileUrl', () => {
    it('should construct correct URL for image files', () => {
      const fileType = Constants.documentTypes.image;
      const fileName = 'test.jpg';
      const userId = '3';
      const expected = `${environment.apiServer}${Urls.writableFilePath}${userId}/image/thumb_180x116/${fileName}`;
      const result = service.constructFileUrl(fileType, fileName);

      expect(result).toBe(expected);
    });

    it('should construct correct URL for PDF files', () => {
      const fileType = Constants.documentTypes.pdf;
      const fileName = 'test.pdf';
      const userId = '3';
      const expected = `${environment.apiServer}${Urls.writableFilePath}${userId}/pdf/${fileName}`;
      const result = service.constructFileUrl(fileType, fileName);
      expect(result).toBe(expected);
    });

    it('should construct correct URL for document files', () => {
      const fileType = Constants.documentTypes.document;
      const fileName = 'test.docx';
      const userId = '3';
      const expected = `${environment.apiServer}${Urls.writableFilePath}${userId}/document/${fileName}`;
      const result = service.constructFileUrl(fileType, fileName);
      expect(result).toBe(expected);
    });

    it('should construct correct URL for video files', () => {
      const fileType = Constants.documentTypes.video;
      const fileName = 'test.mp4';
      const userId = '3';
      const expected = `${environment.apiServer}${Urls.writableFilePath}${userId}/video/${fileName}`;
      const result = service.constructFileUrl(fileType, fileName);
      expect(result).toBe(expected);
    });

    it('should construct correct URL for audio files', () => {
      const fileType = Constants.documentTypes.audio;
      const fileName = 'test.mp3';
      const userId = '3';
      const expected = `${environment.apiServer}${Urls.writableFilePath}${userId}/audio/${fileName}`;
      const result = service.constructFileUrl(fileType, fileName);

      expect(result).toBe(expected);
    });

    it('should construct fallback URL for unknown or document file types', () => {
      const fileType = 'unknown';
      const fileName = 'test.xyz';
      const userId = '3';
      const expected = `${environment.apiServer}${Urls.writableFilePath}${userId}/document/${fileName}`;
      const result = service.constructFileUrl(fileType, fileName);
      expect(result).toBe(expected);
    });
  });

  describe('getFullSizeImageUrl', () => {
    it('should convert thumbnail URL to full-size image URL', () => {
      const thumbnailUrl = 'https://example.com/path/thumb_180x116/image.jpg';
      const expected = 'https://example.com/path/image.jpg';
      const result = service.getFullSizeImageUrl(thumbnailUrl);
      expect(result).toBe(expected);
    });

    it('should return the original URL if not a thumbnail', () => {
      const nonThumbnailUrl = 'https://example.com/path/image.jpg';
      const result = service.getFullSizeImageUrl(nonThumbnailUrl);
      expect(result).toBe(nonThumbnailUrl);
    });

    it('should handle undefined or empty URLs', () => {
      expect(service.getFullSizeImageUrl(undefined)).toBeUndefined();
      expect(service.getFullSizeImageUrl('')).toBe('');
    });
  });

  describe('fileFormatTypeTofileTag', () => {
    it('should call createImageMessageWithTag for image files', () => {
      const file = { type: 'image/jpeg' };
      const url = 'https://example.com/image.jpg';
      const name = 'image.jpg';
      const type = 'image/jpeg';
      const callback = jasmine.createSpy('callback');
      spyOn(fileUploadService, 'isImageFile').and.returnValue(true);
      spyOn(service, 'createImageMessageWithTag').and.callFake((f, u, n, t, cb) => {
        cb({ message: '<img src="test"/>' }, { url: 'test-url' });
      });

      const result = service.fileFormatTypeTofileTag(file, url, name, type, callback);

      expect(result).toBeTrue();
      expect(fileUploadService.isImageFile).toHaveBeenCalledWith(type);
      expect(service.createImageMessageWithTag).toHaveBeenCalledWith(file, url, name, type, jasmine.any(Function));
      expect(callback).toHaveBeenCalledWith(
        jasmine.objectContaining({ message: '<img src="test"/>' }),
        jasmine.objectContaining({ url: 'test-url' }),
        Constants.documentTypes.image
      );
    });

    it('should call createVideoMessageWithTag for video files', () => {
      const file = { type: 'video/mp4', name: 'test.mp4' };
      const url = 'https://example.com/video.mp4';
      const name = 'video.mp4';
      const type = 'video/mp4';
      const callback = jasmine.createSpy('callback');
      spyOn(fileUploadService, 'isImageFile').and.returnValue(false);
      spyOn(fileUploadService, 'isVideoFile').and.returnValue(true);
      spyOn(service, 'createVideoMessageWithTag').and.callFake((u, n, t, cb) => {
        // Create a metadata object with type property that matches the expected test case
        const metaData = { type: 'video/mp4' };
        cb({ message: '<video src="test"/>' }, { url: 'test-url' }, Constants.documentTypes.video, metaData);
      });

      const result = service.fileFormatTypeTofileTag(file, url, name, type, callback);

      expect(result).toBeTrue();
      expect(fileUploadService.isVideoFile).toHaveBeenCalledWith(type);
      expect(service.createVideoMessageWithTag).toHaveBeenCalledWith(url, name, type, jasmine.any(Function));
      expect(callback).toHaveBeenCalledWith(
        jasmine.objectContaining({ message: '<video src="test"/>' }),
        jasmine.objectContaining({ url: 'test-url' }),
        Constants.documentTypes.video,
        jasmine.objectContaining({ type: 'video/mp4' })
      );
    });

    it('should return false for unknown file types', () => {
      const file = { type: 'application/unknown' };
      const url = 'https://example.com/unknown.xyz';
      const name = 'unknown.xyz';
      const type = 'application/unknown';
      const callback = jasmine.createSpy('callback');

      spyOn(fileUploadService, 'isImageFile').and.returnValue(false);
      spyOn(fileUploadService, 'isVideoFile').and.returnValue(false);
      spyOn(fileUploadService, 'isAudioFile').and.returnValue(false);
      spyOn(fileUploadService, 'isDocumentFile').and.returnValue(false);
      spyOn(fileUploadService, 'isPdfFile').and.returnValue(false);

      const result = service.fileFormatTypeTofileTag(file, url, name, type, callback);

      expect(result).toBeFalse();
      expect(callback).not.toHaveBeenCalled();
    });
  });

  describe('file type detection methods', () => {
    it('should delegate isImage to fileUploadService.isImageFile', () => {
      const fileType = 'image/jpeg';
      spyOn(fileUploadService, 'isImageFile').and.returnValue(true);

      const result = service.isImage(fileType);

      expect(result).toBeTrue();
      expect(fileUploadService.isImageFile).toHaveBeenCalledWith(fileType);
    });

    it('should delegate isVideo to fileUploadService.isVideoFile', () => {
      const fileType = 'video/mp4';
      spyOn(fileUploadService, 'isVideoFile').and.returnValue(true);

      const result = service.isVideo(fileType);

      expect(result).toBeTrue();
      expect(fileUploadService.isVideoFile).toHaveBeenCalledWith(fileType);
    });

    it('should delegate isAudio to fileUploadService.isAudioFile', () => {
      const fileType = 'audio/mpeg';
      spyOn(fileUploadService, 'isAudioFile').and.returnValue(true);

      const result = service.isAudio(fileType);

      expect(result).toBeTrue();
      expect(fileUploadService.isAudioFile).toHaveBeenCalledWith(fileType);
    });

    it('should delegate isDocument to fileUploadService.isDocumentFile', () => {
      const fileType = 'application/msword';
      spyOn(fileUploadService, 'isDocumentFile').and.returnValue(true);

      const result = service.isDocument(fileType);

      expect(result).toBeTrue();
      expect(fileUploadService.isDocumentFile).toHaveBeenCalledWith(fileType);
    });

    it('should delegate isPdf to fileUploadService.isPdfFile', () => {
      const fileType = 'application/pdf';
      spyOn(fileUploadService, 'isPdfFile').and.returnValue(true);

      const result = service.isPdf(fileType);

      expect(result).toBeTrue();
      expect(fileUploadService.isPdfFile).toHaveBeenCalledWith(fileType);
    });
  });

  describe('getMimeTypeFromView', () => {
    it('should get MIME type for image files from FileUploadService', () => {
      const view = 'image';
      const filename = 'test.jpg';
      // We're using the spy already defined in beforeEach

      const result = service.getMimeTypeFromView(view, filename);

      expect(result).toBe('image/jpeg');
      expect(fileUploadService.getMimeTypeFromFileName).toHaveBeenCalledWith(filename);
    });

    it('should return application/pdf for PDF files', () => {
      const view = 'pdf';
      const filename = 'test.pdf';

      const result = service.getMimeTypeFromView(view, filename);

      expect(result).toBe('application/pdf');
    });

    it('should get MIME type for document files from FileUploadService', () => {
      const view = 'document';
      const filename = 'test.docx';
      // We're using the spy already defined in beforeEach

      const result = service.getMimeTypeFromView(view, filename);

      expect(result).toBe('application/msword');
      expect(fileUploadService.getMimeTypeFromFileName).toHaveBeenCalledWith(filename);
    });

    it('should return application/octet-stream for unknown file types', () => {
      const view = 'unknown';
      const filename = 'test.xyz';

      const result = service.getMimeTypeFromView(view, filename);

      expect(result).toBe('application/octet-stream');
    });
  });

  describe('pushMessage', () => {
    beforeEach(() => {
      // Mock common.getTranslateData to return actual readable strings instead of MESSAGES constants
      spyOn(commonService, 'getTranslateData').and.callFake((key) => {
        if (key === 'MESSAGES.SHARED_PDF') return 'PDF';
        if (key === 'MESSAGES.SHARED_DOCUMENT') return 'Document';
        if (key === 'MESSAGES.SHARED_IMAGE') return 'Image';
        if (key === 'MESSAGES.SHARED_AUDIO') return 'Audio';
        if (key === 'MESSAGES.SHARED_VIDEO') return 'Video';
        return key;
      });
    });

    it('should generate correct push message based on attached files count', () => {
      service.filesAttached = {
        pdf: 1,
        document: 2,
        image: 3,
        audio: 0,
        video: 1
      };

      const result = service.pushMessage(service.filesAttached);

      // The message should contain PDF, Document, Image, and Video but not Audio
      expect(result).toContain('PDF');
      expect(result).toContain('Document');
      expect(result).toContain('Image');
      expect(result).toContain('Video');
      expect(result).not.toContain('Audio');
    });

    it('should return empty string for no attached files', () => {
      service.filesAttached = {
        pdf: 0,
        document: 0,
        image: 0,
        audio: 0,
        video: 0
      };

      const result = service.pushMessage(service.filesAttached);

      expect(result).toBe('');
    });
  });

  describe('sendMessageWithAttachment', () => {
    it('should update attachmentStr in payload for broadcast messages', () => {
      const attachmentStr = '<img src="test.jpg"/>';
      const payload = {
        message: 'Test message',
        language: 'en',
        selectedRoles: [],
        selectedTags: []
      };
      const messageType = 'broadcast';

      const result = service.sendMessageWithAttachment(attachmentStr, payload, messageType);

      expect(result.attachmentStr).toBe(attachmentStr);
      expect(result.message).toBe('Test message');
    });

    it('should update attachmentStr in maskedMessage for masked messages', () => {
      const attachmentStr = '<img src="test.jpg"/>';
      const payload = {
        maskedMessage: {
          messageSubject: 'Test subject',
          message: 'Test message'
        },
        language: 'en'
      };
      const messageType = 'masked';

      const result = service.sendMessageWithAttachment(attachmentStr, payload, messageType);

      expect(result.maskedMessage.attachmentStr).toBe(attachmentStr);
      expect(result.maskedMessage.messageSubject).toBe('Test subject');
    });

    it('should handle missing maskedMessage object for masked messages', () => {
      const attachmentStr = '<img src="test.jpg"/>';
      const payload = {
        language: 'en'
      };
      const messageType = 'masked';

      const result = service.sendMessageWithAttachment(attachmentStr, payload, messageType);

      expect(result.maskedMessage).toBeDefined();
      expect(result.maskedMessage.attachmentStr).toBe(attachmentStr);
    });
  });

  describe('returnTag and returnTagWithUrl', () => {
    it('should return correct image tag for image type', () => {
      const url = 'https://example.com/image.jpg';
      const type = 'image';

      const result = service.returnTag(url, type);

      expect(result).toBe(`<img title='Image' src='${url}'/>`);
    });

    it('should return correct file tag for non-image type', () => {
      const url = 'https://example.com';
      const type = 'document';
      const relativePath = 'doc';

      const result = service.returnTag(url, type, relativePath);

      expect(result).toBe(`<img title='Image' class='file-thumbnail' src='${url}${relativePath}.png' />`);
    });
  });
});
