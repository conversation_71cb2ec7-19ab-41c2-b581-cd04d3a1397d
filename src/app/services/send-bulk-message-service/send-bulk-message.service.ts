import { Injectable } from '@angular/core';
import { Constants } from 'src/app/constants/constants';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { CommonService } from 'src/app/services/common-service/common.service';
import { getFileReader, replaceSingleQuoteWithEmpty } from 'src/app/utils/utils';
import { environment } from 'src/environments/environment';
import { Urls } from 'src/app/constants/urls';
import { FileUploadService } from '../file-upload-service/file-upload.service';

@Injectable({
  providedIn: 'root'
})
export class SendBulkMessageService {
  iconPath = `${environment.apiServer}/webapp/www/img/`;
  filesAttached = {
    pdf: 0,
    document: 0,
    image: 0,
    audio: 0,
    video: 0
  };
  constructor(
    private readonly sharedService: SharedService,
    private readonly common: CommonService,
    private readonly fileUploadService: FileUploadService
  ) {}

  /**
   * Constructs file URL based on file type and filename
   * @param fileType The type of file (image, pdf, document, etc.)
   * @param fileName The name of the file
   * @returns The constructed file URL
   */
  constructFileUrl(fileType: string, fileName: string): string {
    const userId = this.sharedService?.userData.userId;
    const baseUrl = `${environment.apiServer}${Urls.writableFilePath}`;
    let url = '';

    switch (fileType) {
      case Constants.documentTypes.image:
        url = `${baseUrl}${userId}/image/thumb_180x116/${fileName}`;
        break;
      case Constants.documentTypes.pdf:
        url = `${baseUrl}${userId}/pdf/${fileName}`;
        break;
      case Constants.documentTypes.video:
        url = `${baseUrl}${userId}/video/${fileName}`;
        break;
      case Constants.documentTypes.audio:
        url = `${baseUrl}${userId}/audio/${fileName}`;
        break;
      default:
        url = `${baseUrl}${userId}/document/${fileName}`;
    }

    return url;
  }

  /**
   * Gets the full-size image URL by removing thumbnail path
   * @param url The thumbnail URL
   * @returns The full-size image URL
   */
  getFullSizeImageUrl(url: string): string {
    if (url && url.includes('/thumb_180x116/')) {
      return url.replace('/thumb_180x116/', '/');
    }
    return url;
  }

  fileFormatTypeTofileTag(file, url: string, fileName: string, type: string, callBack): boolean {
    let fileType;
    let name = fileName;
    if (file && !url) {
      fileType = file.type;
    } else {
      fileType = type?.toLowerCase();
      if (name?.includes('@call--')) {
        const baseName = name.split('@call--')[0];
        const extension = name.substring(name.lastIndexOf('.'));
        name = `${baseName}${extension}`;
      }
    }

    // Using the FileUploadService methods for type checking
    if (this.fileUploadService.isImageFile(fileType)) {
      this.createImageMessageWithTag(file, url, name, type, (result, fileData) => {
        if (file.name) {
          callBack(result, fileData, Constants.documentTypes.image, file);
        } else {
          callBack(result, fileData, Constants.documentTypes.image);
        }
      });
      return true;
    }
    if (this.fileUploadService.isVideoFile(fileType)) {
      this.createVideoMessageWithTag(url, name, type, (result, fileData) => {
        if (file.name) {
          callBack(result, fileData, Constants.documentTypes.video, file);
        } else {
          callBack(result, fileData, Constants.documentTypes.video);
        }
      });
      return true;
    }
    if (this.fileUploadService.isAudioFile(fileType)) {
      this.createAudioMessageWithTag(url, name, type, (result, fileData) => {
        if (file.name) {
          callBack(result, fileData, Constants.documentTypes.audio, file);
        } else {
          callBack(result, fileData, Constants.documentTypes.audio);
        }
      });
      return true;
    }
    if (this.fileUploadService.isDocumentFile(fileType)) {
      this.createDocumentMessageWithTag(file, url, name, type, (result, fileData) => {
        if (file.name) {
          callBack(result, fileData, Constants.documentTypes.document, file);
        } else {
          callBack(result, fileData, Constants.documentTypes.document);
        }
      });
      return true;
    }
    if (this.fileUploadService.isPdfFile(fileType)) {
      this.createPdfMessageWithTag(file, url, name, type, (result, fileData) => {
        if (file.name) {
          callBack(result, fileData, Constants.documentTypes.pdf, file);
        } else {
          callBack(result, fileData, Constants.documentTypes.pdf);
        }
      });
      return true;
    }
    return false;
  }

  isImage(fileType: string): boolean {
    return this.fileUploadService.isImageFile(fileType);
  }

  isVideo(fileType: string): boolean {
    return this.fileUploadService.isVideoFile(fileType);
  }

  isAudio(fileType: string): boolean {
    return this.fileUploadService.isAudioFile(fileType);
  }

  isDocument(fileType: string): boolean {
    return this.fileUploadService.isDocumentFile(fileType);
  }

  isPdf(fileType: string): boolean {
    return this.fileUploadService.isPdfFile(fileType);
  }

  createImageMessageWithTag(file: any, URL: string, name: string, type: string, callback: any): any {
    const url = '';
    let tag;
    if (file && !URL) {
      const reader = getFileReader();
      reader.readAsDataURL(file);
      reader.onload = (e) => {
        const newUrl = reader.result;
        const docType = 'image';
        tag = this.returnTag(newUrl, docType);
        callback(tag);
      };
      reader.onerror = (error) => {
        callback(false);
      };
    } else if (URL) {
      const fileDatas = {
        id: `'id'${new Date().getTime()}`,
        artist: name,
        title: name,
        duration: '',
        mime: type,
        url: URL
      };
      tag = this.returnTagwithUrl(Constants.documentTypes.image, URL, name);
      callback(tag, fileDatas);
    }
  }

  returnTagwithUrl(docType: string, URL: string, title: string): any {
    const name = replaceSingleQuoteWithEmpty(title);
    let data;
  const encodedURL = encodeURIComponent(URL);
    let downloadIcon = `<span class="show-download-options"><ion-icon name="cloud-download" ng-click="showDocumentOptionForMobile($event)" data-src='${encodedURL}' data-mediatype='${docType}'></ion-icon></span>`;
    if (docType === Constants.documentTypes.image) {
      // eslint-disable-next-line max-len
      downloadIcon = `<span class="show-download-options"><ion-icon name="cloud-download" ng-click="showDocumentOptionForMobile($event)" data-src='${encodedURL}' data-mediatype='${docType}'></ion-icon></span>`;
      data = {
        type: docType,
        message: `<img data-mediaType='${docType}' src='${URL}' ng-click='showCmisImage($event)' title= '${name}' class='image-download-max-width' ng-src='${URL}'/>`,
        downloadIcon
      };
    } else if (docType === Constants.documentTypes.audio) {
      data = {
        type: Constants.documentTypes.audio,
        message: `<audio controls> <source src='${URL}' type='${name}' > <i class= 'ion-headphone'> </i> Audio </audio>`,
        downloadIcon
      };
    } else if (docType === Constants.documentTypes.document) {
      data = {
        type: Constants.documentTypes.document,
        message: `<img data-mediaType='${docType}' data-src='${URL}' ng-click='showPdfOrDocs($event)'
    title='${name}' class='file-thumbnail' src='${this.iconPath}${Constants.assetRelativePath.doc}.png' />`,
        downloadIcon
      };
    } else if (docType === Constants.documentTypes.excel) {
      downloadIcon = `<span class="show-download-options"><ion-icon name="cloud-download" ng-click="showDocumentOptionForMobile($event)" data-src='${encodedURL}' data-mediatype='${Constants.documentTypes.document}'></ion-icon></span>`;
      data = {
        type: Constants.documentTypes.document,
        message: `<img data-mediaType='${Constants.documentTypes.document}' data-src='${URL}' ng-click='showPdfOrDocs($event)'
    title='${name}' class='file-thumbnail' src='${this.iconPath}${Constants.assetRelativePath.doc}.png' />`,
        downloadIcon
      };
    } else if (docType === Constants.documentTypes.pdf) {
      data = {
        type: Constants.documentTypes.pdf,
        message: `<img data-mediaType='${docType}' data-cmisFile='true' data-src='${URL}'
        ng-click='showPdfOrDocs($event)' title='${name}' class='file-thumbnail' src='${this.iconPath}${Constants.assetRelativePath.pdf}.png'/>
        `,
        downloadIcon
      };
    } else if (docType === Constants.documentTypes.video) {
      data = {
        type: Constants.documentTypes.video,
        fileName: `videothumb ${URL}.jpg`,
        message: name,
        downloadIcon
      };
    }

    return data;
  }

  createVideoMessageWithTag(url: string, name: string, type: string, callback: any): any {
    let fileURL;
    let mask = '';
    let fileDatas = {};
    fileURL = url;
    let tag;
    const duration = '';
    const filename = fileURL.split('/').pop();
    let filenameNotExt = filename.split('.');
    filenameNotExt = filenameNotExt.shift();
    const imageUrl = `${url} '?type=thumbnail'`;
    mask = `<img data-mediaType= ${Constants.documentTypes.video} data-src= '${fileURL}' src= '${imageUrl}' />`;
    fileDatas = {
      id: `'id' ${new Date().getTime()}`,
      artist: name,
      title: name,
      duration,
      mime: type,
      url,
      imgFile: imageUrl
    };
    tag = this.returnTagwithUrl(Constants.documentTypes.video, filenameNotExt, mask);
    callback(tag, fileDatas);
  }

  createAudioMessageWithTag(url: string, name: string, type: string, callback: any): any {
    let fileURL;
    let fileDatas = {};
    let tag;
    fileURL = url;
    const duration = '';
    fileDatas = {
      id: `'id' ${new Date().getTime()}`,
      artist: name,
      title: name,
      duration,
      mime: type,
      url: fileURL
    };
    tag = this.returnTagwithUrl(Constants.documentTypes.audio, fileURL, type);
    callback(tag, fileDatas);
  }
  createDocumentMessageWithTag(file: any, url: string, name: string, type: string, callback: any): any {
    let filename = url?.substring(url.lastIndexOf('/') + 1);
    let tag;
    filename = filename?.substring(filename.lastIndexOf('/') + 1);
    filename = filename?.substring(filename.lastIndexOf('.') + 1);
    if (file && !url) {
      const docType = this.common.getFileType(file.type);
      tag = this.returnTag(this.iconPath, docType, Constants.assetRelativePath.doc);
      callback(tag);
    } else if (url) {
      const docType = Constants.excelMimes.includes(file.type)
        ? Constants.documentTypes.excel
        : Constants.documentTypes.document;
      const fileDatas = {
        id: `'id' ${new Date().getTime()}`,
        artist: name,
        title: name,
        duration: '',
        mime: type,
        url
      };
      tag = this.returnTagwithUrl(docType, url, name);
      callback(tag, fileDatas);
    }
  }
  returnTag(url: any, type: string, relativePath?: string): string {
    if (type === 'image') {
      return `<img title='Image' src='${url}'/>`;
    } else {
      return `<img title='Image' class='file-thumbnail' src='${url}${relativePath}.png' />`;
    }
  }

  createPdfMessageWithTag(file: any, url: string, name: string, type: string, callback: any): any {
    let tag;
    if (file && !url) {
      const docType = Constants.documentTypes.pdf;
      tag = this.returnTag(this.iconPath, docType, Constants.assetRelativePath.pdf);
      callback(tag);
    } else if (url) {
      const fileDatas = {
        id: `'id' ${new Date().getTime()}`,
        artist: name,
        title: name,
        duration: '',
        mime: type,
        url
      };
      tag = this.returnTagwithUrl(Constants.documentTypes.pdf, url, name);
      callback(tag, fileDatas);
    }
  }
  pushMessage(attachedFiles: any): string {
    let pushMessage = '';
    Object.keys(attachedFiles).map((e, i) => {
      if (attachedFiles[e] > 0) {
        if (this.filesAttached[e]) {
          switch (e) {
            case 'pdf':
              pushMessage = `${pushMessage}, ${this.common.getTranslateData('MESSAGES.SHARED_PDF')} `;
              break;
            case 'document':
              pushMessage = `${pushMessage}, ${this.common.getTranslateData('MESSAGES.SHARED_DOCUMENT')} `;
              break;
            case 'image':
              pushMessage = `${pushMessage}, ${this.common.getTranslateData('MESSAGES.SHARED_IMAGE')} `;
              break;
            case 'audio':
              pushMessage = `${pushMessage}, ${this.common.getTranslateData('MESSAGES.SHARED_AUDIO')} `;
              break;
            case 'video':
              pushMessage = `${pushMessage}, ${this.common.getTranslateData('MESSAGES.SHARED_VIDEO')} `;
              break;
          }
        }
      }
    });
    return pushMessage;
  }

  /**
   * Infers the MIME type from the view property and filename
   * @param view The view property from file upload response (image, pdf, etc.)
   * @param filename The filename to extract extension if needed
   * @returns The inferred MIME type
   */
  getMimeTypeFromView(view: string, filename: string): string {
    // Use the FileUploadService to get the MIME type from the filename
    switch (view) {
      case 'image':
        // Try to get more specific image type from filename extension
        return this.fileUploadService.getMimeTypeFromFileName(filename);
      case 'pdf':
        return 'application/pdf';
      case 'document':
        return this.fileUploadService.getMimeTypeFromFileName(filename);
      case 'audio':
        return this.fileUploadService.getMimeTypeFromFileName(filename);
      case 'video':
        return this.fileUploadService.getMimeTypeFromFileName(filename);
      default:
        return 'application/octet-stream'; // Default fallback
    }
  }

  /**
   * Sends message with attachments
   * @param attachmentStr The attachment string to include in the message
   * @param payload The payload object to be sent
   * @param messageFormData The form data containing message content
   * @param messageType The type of message (broadcast or masked)
   * @returns Observable of the API response
   */
  sendMessageWithAttachment(attachmentStr: string, payload, messageType: string) {
    // Create a copy of the payload to avoid modifying the parameter directly
    const updatedPayload = { ...payload };
    if (messageType === 'broadcast') {
      updatedPayload.attachmentStr = attachmentStr;
    }
    if (messageType === 'masked') {
      // Ensure maskedMessage exists and is an object
      updatedPayload.maskedMessage = updatedPayload.maskedMessage || {};
      updatedPayload.maskedMessage.attachmentStr = attachmentStr;
      updatedPayload.createdBy = undefined;
    }
    return updatedPayload;
  }
}
