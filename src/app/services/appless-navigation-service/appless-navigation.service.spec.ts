/* eslint-disable @typescript-eslint/no-explicit-any */
import { TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { of, throwError } from 'rxjs';
import { Constants } from 'src/app/constants/constants';
import { APIs } from 'src/app/constants/apis';
import { Signature } from 'src/app/constants/signature';
import { ApplessNavigationService } from './appless-navigation.service';
import { SharedService } from '../shared-service/shared.service';
import { HttpService } from '../http-service/http.service';
import { FormService } from '../../pages/form-center/services/form.service';
import { CommonService } from '../common-service/common.service';
import { SessionService } from '../session-service/session.service';
import { GraphqlService } from '../graphql-service/graphql.service';

describe('ApplessNavigationService', () => {
  let service: ApplessNavigationService;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockSharedService: any;
  let mockHttpService: jasmine.SpyObj<HttpService>;
  let mockFormService: jasmine.SpyObj<FormService>;
  let mockCommonService: jasmine.SpyObj<CommonService>;
  let mockSessionService: any;
  let mockGraphqlService: jasmine.SpyObj<GraphqlService>;

  beforeEach(() => {
    const routerSpy = jasmine.createSpyObj('Router', ['navigate', 'navigateByUrl']);
    routerSpy.navigateByUrl.and.returnValue(Promise.resolve(true));

    // Create a proper mock object with mutable properties
    const sharedServiceSpy = {
      userData: { appLessSession: false, tenantId: 123, isVirtual: false },
      sessionService: { applessMessagingFlow: false },
      appLessHomeNext: { next: jasmine.createSpy('next') },
      errorHandler: jasmine.createSpy('errorHandler')
    };
    // Add consoloLoader as a mutable property
    Object.defineProperty(sharedServiceSpy, 'consoloLoader', {
      value: false,
      writable: true,
      configurable: true
    });

    const httpServiceSpy = jasmine.createSpyObj('HttpService', ['doGet', 'doPost']);
    const formServiceSpy = jasmine.createSpyObj('FormService', ['generalizeResponse']);
    const commonServiceSpy = jasmine.createSpyObj('CommonService', ['showMessage', 'getTranslateData']);
    const sessionServiceSpy = {
      applessMessagingFlow: false
    };
    const graphqlServiceSpy = jasmine.createSpyObj('GraphqlService', ['getDocumentDetails', 'createDocumentActivity']);

    TestBed.configureTestingModule({
      providers: [
        ApplessNavigationService,
        { provide: Router, useValue: routerSpy },
        { provide: SharedService, useValue: sharedServiceSpy },
        { provide: HttpService, useValue: httpServiceSpy },
        { provide: FormService, useValue: formServiceSpy },
        { provide: CommonService, useValue: commonServiceSpy },
        { provide: SessionService, useValue: sessionServiceSpy },
        { provide: GraphqlService, useValue: graphqlServiceSpy }
      ]
    });

    service = TestBed.inject(ApplessNavigationService);
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    mockSharedService = TestBed.inject(SharedService) as jasmine.SpyObj<SharedService>;
    mockHttpService = TestBed.inject(HttpService) as jasmine.SpyObj<HttpService>;
    mockFormService = TestBed.inject(FormService) as jasmine.SpyObj<FormService>;
    mockCommonService = TestBed.inject(CommonService) as jasmine.SpyObj<CommonService>;
    mockSessionService = TestBed.inject(SessionService) as jasmine.SpyObj<SessionService>;
    mockGraphqlService = TestBed.inject(GraphqlService) as jasmine.SpyObj<GraphqlService>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('handleDirectFormNavigation', () => {
    it('should navigate to form when found', (done) => {
      const mockResponse = {
        response: [
          {
            formStatus: Constants.formPendingStatus,
            form_id: 123,
            sent_id: '123',
            form_name: 'Test Form',
            admin_archived: 0
          }
        ]
      };

      mockHttpService.doGet.and.returnValue(of(mockResponse));

      // Mock the formService.generalizeResponse to return a form object
      const mockForm = {
        id: 123,
        formId: 123,
        form_id: 123,
        formName: 'Test Form',
        formStatus: Constants.formPendingStatus
      };
      mockFormService.generalizeResponse.and.returnValue([mockForm]);

      const formNavData = {
        sendId: '123',
        formId: 123
      };

      // Spy on the navigateToForm method
      spyOn(service, 'navigateToForm');

      service.handleDirectFormNavigation(formNavData, {});

      // Use setTimeout to allow the promise chain to complete
      setTimeout(() => {
        expect(service.navigateToForm).toHaveBeenCalledWith(mockForm, Constants.formPendingStatus, false);
        done();
      }, 10);
    });

    it('should navigate to appless home when form is not pending', (done) => {
      // Mock response with a completed form (not pending)
      const mockResponse = {
        response: [
          {
            sent_id: '123',
            form_name: 'Completed Form',
            formStatus: Constants.formCompletedStatus,
            form_id: 123,
            admin_archived: 0
          }
        ]
      };

      mockHttpService.doGet.and.returnValue(of(mockResponse));

      const formNavData = {
        sendId: '123', // This doesn't match the sent_id in the response
        formId: 123
      };

      service.handleDirectFormNavigation(formNavData, {});

      // Since the method now returns void, we need to test the side effects
      setTimeout(() => {
        // Test that navigation to appless home was called due to form not being pending
        expect(mockRouter.navigate).toHaveBeenCalledWith(
          ['./appless/home'],
          jasmine.objectContaining({
            skipLocationChange: false,
            state: jasmine.objectContaining({
              error: true,
              fromDirectNavigationFailure: true
            })
          })
        );
        done();
      }, 10);
    });
  });

  describe('navigateToDocument', () => {
    it('should navigate to document with correct parameters', () => {
      const mockDocNavData = {
        id: 456,
        senderTenant: 789
      };

      service.navigateToDocument(mockDocNavData);

      expect(mockSharedService.userData.appLessSession).toBe(true);
      expect(mockRouter.navigate).toHaveBeenCalledWith([`document-center/view-document/${mockDocNavData.id}/${mockDocNavData.senderTenant}`], {
        skipLocationChange: true,
        state: { docNavData: mockDocNavData }
      });
    });
  });

  describe('handleDirectDocumentNavigation', () => {
    it('should navigate to document when signature status is pending', (done) => {
      const mockDocNavData = {
        id: 456,
        senderTenant: 789
      };

      const mockDocument = {
        id: undefined,
        senderTenant: undefined,
        signatureStatus: Signature.signatureStatus.signaturePendingStatus
      };

      const mockResponse = {
        data: {
          signatureRequest: [mockDocument]
        },
        loading: false,
        networkStatus: 7
      };

      mockGraphqlService.getDocumentDetails.and.returnValue(of(mockResponse));

      // Spy on the navigateToDocument method to verify it's called with correct parameters
      spyOn(service, 'navigateToDocument').and.callThrough();

      service.handleDirectDocumentNavigation(mockDocNavData, {});

      // Since the method now returns void, we need to test the side effects
      setTimeout(() => {
        expect(service.navigateToDocument).toHaveBeenCalledWith(mockDocument);
        expect(mockRouter.navigate).toHaveBeenCalledWith(['document-center/view-document/undefined/undefined'], {
          skipLocationChange: true,
          state: { docNavData: mockDocument }
        });
        done();
      }, 10);
    });

    it('should not navigate when document is already signed', (done) => {
      const mockDocNavData = {
        id: 456,
        senderTenant: 789
      };

      const mockResponse = {
        data: {
          signatureRequest: [
            {
              signatureStatus: Signature.signatureStatus.signatureSignedStatus
            }
          ]
        },
        loading: false,
        networkStatus: 7
      };

      mockGraphqlService.getDocumentDetails.and.returnValue(of(mockResponse));

      service.handleDirectDocumentNavigation(mockDocNavData, {});

      // Since the method now returns void, we need to test the side effects
      setTimeout(() => {
        // Should navigate to appless home instead of document since it's already signed
        expect(mockRouter.navigate).toHaveBeenCalledWith(
          ['./appless/home'],
          jasmine.objectContaining({
            skipLocationChange: false,
            state: jasmine.objectContaining({
              fromDirectNavigationFailure: true
            })
          })
        );
        done();
      }, 10);
    });

    it('should handle API error gracefully', (done) => {
      const mockDocNavData = {
        id: 456,
        senderTenant: 789
      };

      mockGraphqlService.getDocumentDetails.and.returnValue(throwError(() => new Error('API Error')));

      service.handleDirectDocumentNavigation(mockDocNavData, {});

      // Since the method now returns void, we need to test the side effects
      setTimeout(() => {
        // Should navigate to appless home due to error
        expect(mockRouter.navigate).toHaveBeenCalledWith(
          ['./appless/home'],
          jasmine.objectContaining({
            skipLocationChange: false,
            state: jasmine.objectContaining({
              fromDirectNavigationFailure: true
            })
          })
        );
        done();
      }, 10);
    });
  });

  describe('navigateToMessageChat', () => {
    it('should navigate to message chat', () => {
      const chatRoomId = 'chat123';

      service.navigateToMessageChat(chatRoomId);

      expect(mockSessionService.applessMessagingFlow).toBe(true);
      expect(mockRouter.navigate).toHaveBeenCalledWith([`/message-center/messages/active/chat/${chatRoomId}`], {
        skipLocationChange: mockSharedService.userData?.isVirtual
      });
    });
  });

  describe('handleDirectNavigationFromAppless', () => {
    it('should navigate directly to form when frequency matches', () => {
      spyOn(service, 'handleDirectFormNavigation');

      const additionalData = {
        appLessWorkFlow: Constants.applessFlowMode.form,
        navigateToApplessHome: Constants.NAVIGATE_TO_APPLESS_HOME,
        sendId: '123',
        formId: undefined
      };

      service.handleDirectNavigationFromAppless(additionalData);

      expect(service.handleDirectFormNavigation).toHaveBeenCalledWith(
        {
          sendId: '123',
          formId: undefined
        },
        additionalData
      );
    });

    it('should navigate directly to document when frequency matches', () => {
      spyOn(service, 'handleDirectDocumentNavigation');

      const additionalData = {
        appLessWorkFlow: Constants.applessFlowMode.document,
        navigateToApplessHome: Constants.NAVIGATE_TO_APPLESS_HOME,
        documentId: '456',
        tenantId: '789'
      };

      service.handleDirectNavigationFromAppless(additionalData);

      expect(service.handleDirectDocumentNavigation).toHaveBeenCalledWith(
        {
          id: 456,
          senderTenant: 789
        },
        additionalData
      );
    });

    it('should navigate directly to message when frequency matches', () => {
      spyOn(service, 'navigateToMessageChat');

      const additionalData = {
        appLessWorkFlow: Constants.applessFlowMode.message,
        navigateToApplessHome: Constants.NAVIGATE_TO_APPLESS_HOME,
        chatRoomId: 'chat123'
      };

      service.handleDirectNavigationFromAppless(additionalData);

      expect(service.navigateToMessageChat).toHaveBeenCalledWith('chat123');
    });

    it('should navigate to appless home when frequency does not match', () => {
      const additionalData = {
        appLessWorkFlow: Constants.applessFlowMode.form,
        navigateToApplessHome: 'different-frequency',
        test: 'data'
      };

      service.handleDirectNavigationFromAppless(additionalData);

      expect(mockRouter.navigate).toHaveBeenCalledWith(
        ['./appless/home'],
        jasmine.objectContaining({
          skipLocationChange: false,
          state: jasmine.objectContaining({
            ...additionalData,
            fromDirectNavigationFailure: true
          })
        })
      );
    });
  });

  // Tests for saveApplessDocumentHistory method
  describe('saveApplessDocumentHistory', () => {
    beforeEach(() => {
      mockSharedService.userData = {
        userId: 123,
        displayName: 'Test User',
        userName: '<EMAIL>',
        mobile: '1234567890',
        countryCode: '+1'
      };
      mockSharedService.applessDocumentId = 456;
    });

    it('should not call any service when appLessWorkFlow is not document', () => {
      const params = {
        activityName: Constants.documentActivityParams.documentDownload,
        documentId: 789,
        sender: 'test-sender',
        appLessWorkFlow: Constants.applessFlowMode.form, // Not document
        accessCodeSentMedium: Constants.accessCodeSentMedium.email
      };

      service.saveApplessDocumentHistory(params);

      expect(mockHttpService.doPost).not.toHaveBeenCalled();
      expect(mockGraphqlService.createDocumentActivity).not.toHaveBeenCalled();
    });

    it('should call httpService.doPost for document download activity', () => {
      const params = {
        activityName: Constants.documentActivityParams.documentDownload,
        documentId: 789,
        sender: 'test-sender',
        appLessWorkFlow: Constants.applessFlowMode.document,
        accessCodeSentMedium: Constants.accessCodeSentMedium.email
      };

      mockHttpService.doPost.and.returnValue(of({ success: true }));

      service.saveApplessDocumentHistory(params);

      expect(mockHttpService.doPost).toHaveBeenCalledWith({
        endpoint: APIs.addDocumentHistory,
        payload: jasmine.objectContaining({
          activityName: Constants.documentActivityParams.documentDownload,
          documentId: 789,
          sender: 'test-sender',
          recipient: 123,
          senderName: '',
          recipientName: 'Test User',
          recipientEmail: '<EMAIL>',
          recipientPhoneNumber: '1234567890',
          recipientCountryCode: '+1',
          sentDocumentVia: Constants.documentSignedMedium,
          accessCodeSentMedium: Constants.accessCodeSentMedium.email
        }),
        loader: false
      });
    });

    it('should call graphqlService.createDocumentActivity for non-download activities', () => {
      const params = {
        activityName: 'DOCUMENT_VIEWED', // Not download
        documentId: 789,
        sender: 'test-sender',
        appLessWorkFlow: Constants.applessFlowMode.document,
        accessCodeSentMedium: Constants.accessCodeSentMedium.email
      };

      mockGraphqlService.createDocumentActivity.and.returnValue(of({ success: true }));

      service.saveApplessDocumentHistory(params);

      expect(mockGraphqlService.createDocumentActivity).toHaveBeenCalledWith(
        jasmine.objectContaining({
          activityName: 'DOCUMENT_VIEWED',
          documentId: 789,
          sender: 'test-sender',
          recipient: 123,
          senderName: '',
          recipientName: 'Test User',
          recipientEmail: '<EMAIL>',
          recipientPhoneNumber: '1234567890',
          recipientCountryCode: '+1',
          sentDocumentVia: Constants.documentSignedMedium,
          accessCodeSentMedium: Constants.accessCodeSentMedium.email
        })
      );
    });

    it('should use sharedService.applessDocumentId when documentId is not provided', () => {
      const params = {
        activityName: Constants.documentActivityParams.documentDownload,
        sender: 'test-sender',
        appLessWorkFlow: Constants.applessFlowMode.document,
        accessCodeSentMedium: Constants.accessCodeSentMedium.email
      };

      mockHttpService.doPost.and.returnValue(of({ success: true }));

      service.saveApplessDocumentHistory(params);

      expect(mockHttpService.doPost).toHaveBeenCalledWith(
        jasmine.objectContaining({
          payload: jasmine.objectContaining({
            documentId: 456 // From mockSharedService.applessDocumentId
          })
        })
      );
    });

    it('should handle errors gracefully', () => {
      const params = {
        activityName: Constants.documentActivityParams.documentDownload,
        documentId: 789,
        sender: 'test-sender',
        appLessWorkFlow: Constants.applessFlowMode.document,
        accessCodeSentMedium: Constants.accessCodeSentMedium.email
      };

      const error = new Error('Network error');
      mockHttpService.doPost.and.returnValue(throwError(error));

      service.saveApplessDocumentHistory(params);
      expect(mockSharedService.errorHandler).toHaveBeenCalledWith(error);
    });
  });
});
