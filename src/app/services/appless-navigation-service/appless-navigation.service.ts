/* eslint-disable no-param-reassign */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injectable } from '@angular/core';
import { Router, NavigationExtras } from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, catchError, finalize } from 'rxjs/operators';
import * as moment from 'moment';
import { Constants } from 'src/app/constants/constants';
import { PageRoutes } from 'src/app/constants/page-routes';
import { APIs } from 'src/app/constants/apis';
import { Config } from 'src/app/constants/config';
import { Permissions } from 'src/app/constants/permissions';
import { FormWorkListPayload } from 'src/app/interfaces/common-interface';
import { Signature } from 'src/app/constants/signature';
import { SharedService } from '../shared-service/shared.service';
import { HttpService } from '../http-service/http.service';
import { FormService } from '../../pages/form-center/services/form.service';
import { CommonService } from '../common-service/common.service';
import { SessionService } from '../session-service/session.service';
import { GraphqlService } from '../graphql-service/graphql.service';
import { PermissionService } from '../permission-service/permission.service';

export interface DirectFormNavigationData {
  sendId: string;
  formId: number;
}

export interface DirectDocumentNavigationData {
  id: number;
  senderTenant: number;
}

@Injectable({
  providedIn: 'root'
})
export class ApplessNavigationService {
  constructor(
    private readonly router: Router,
    private readonly sharedService: SharedService,
    private readonly httpService: HttpService,
    private readonly formService: FormService,
    private readonly common: CommonService,
    private readonly sessionService: SessionService,
    private readonly graphqlService: GraphqlService,
    private readonly permissionService: PermissionService
  ) {}

  handleDirectFormNavigation(formNavData: DirectFormNavigationData, additionalData: any) {
    this.sharedService.consoloLoader = true;

    const payload = {
      sendId: formNavData.sendId
    };

    this.httpService
      .doGet({
        endpoint: APIs.getMyFormWorklist,
        extraParams: payload,
        loader: false
      })
      .pipe(
        map((response) => {
          const formDetails = response?.response?.[0];
          // Pending and not archived and not cancelled
          const isPending =
            formDetails?.formStatus === Constants.formPendingStatus && formDetails?.admin_archived !== 1 && formDetails?.admin_archived !== 2;

          if (isPending) {
            const form = this.formService.generalizeResponse(response, Constants.formTypes.pending, Constants.formPendingStatus)[0];
            this.navigateToForm(form, Constants.formPendingStatus, false);
          } else {
            const formStatus = this.determineFormStatus(formDetails);
            this.sharedService.appLessHomeNext.next({
              type: 'FORM',
              id: formDetails?.form_id,
              message: '',
              status: formStatus
            });
            const errorData = {
              ...additionalData,
              error: true
            };
            this.navigateToApplessHome(errorData);
          }
        }),
        catchError(() => {
          const errorData = {
            ...additionalData,
            error: true
          };
          this.navigateToApplessHome(errorData);
          return of(null);
        }),
        finalize(() => {
          this.sharedService.consoloLoader = false;
        })
      )
      .subscribe();
  }

  private determineFormStatus(formDetails: any): string {
    if (formDetails?.formStatus === Constants.formCompletedStatus) {
      return Constants.formCompletedStatus;
    }
    if (formDetails?.admin_archived === 1) {
      return Constants.formArchivedStatus;
    }
    return Constants.formCancelledStatus;
  }

  handleDirectDocumentNavigation(docNavData: DirectDocumentNavigationData, additionalData: any): void {
    this.sharedService.consoloLoader = true;

    this.graphqlService
      .getDocumentDetails(docNavData.id, docNavData.senderTenant)
      .pipe(
        map((response) => {
          const document = response?.data?.signatureRequest[0];
          if (document.signatureStatus === Signature.signatureStatus.signaturePendingStatus) {
            this.navigateToDocument(document);
          } else {
            // Pass the document signature status to navigateToApplessHome
            const updatedData = {
              ...additionalData,
              signatureStatus: document.signatureStatus,
              documentId: docNavData.id,
              senderTenant: docNavData.senderTenant
            };
            this.sharedService.appLessHomeNext.next({
              type: 'DOCUMENT',
              id: docNavData.id,
              message: '',
              status: document.signatureStatus
            });
            this.navigateToApplessHome(updatedData);
          }
        }),
        catchError(() => {
          // On error, navigate to home without signature status
          const errorData = {
            ...additionalData,
            documentId: docNavData.id,
            senderTenant: docNavData.senderTenant,
            error: true
          };
          this.navigateToApplessHome(errorData);
          return of(null);
        }),
        finalize(() => {
          this.sharedService.consoloLoader = false;
        })
      )
      .subscribe();
  }

  navigateToDocument(docNavData: DirectDocumentNavigationData): void {
    this.sharedService.userData.appLessSession = true;
    const navigationExtras: NavigationExtras = {
      skipLocationChange: true,
      state: { docNavData }
    };
    this.router.navigate([`document-center/view-document/${docNavData.id}/${docNavData.senderTenant}`], navigationExtras);
  }

  navigateToForm(form: any, formStatus: string, isEditable: boolean = false, skipLocationChange: boolean = true): void {
    this.sharedService.userData.appLessSession = true;
    form.interactionChannel = Constants.appless;
    form.form_submission_id = form.formSubmissionId;

    const navigationExtras: NavigationExtras = {
      skipLocationChange,
      state: {
        viewData: { form },
        formStatus,
        isEditable
      }
    };

    const formId = form.id || form.formId || form.form_id;
    if (formId) {
      navigationExtras.queryParams = { id: formId };
    }
    this.router.navigateByUrl('/RefreshComponent', { skipLocationChange: true }).then(() => {
      this.router.navigate([`.${PageRoutes.viewForms}`], navigationExtras);
    });
  }

  navigateToMessageChat(chatRoomId: string): void {
    this.sessionService.applessMessagingFlow = true;
    this.router.navigate([`/message-center/messages/active/chat/${chatRoomId}`], {
      skipLocationChange: this.sharedService.userData?.isVirtual
    });
  }

  handleDirectNavigationFromAppless(additionalData: any): void {
    if (additionalData?.navigateToApplessHome !== Constants.NAVIGATE_TO_APPLESS_HOME) {
      this.navigateToApplessHome(additionalData);
      return;
    }

    switch (additionalData?.appLessWorkFlow) {
      case Constants.applessFlowMode.form:
        if (additionalData?.sendId) {
          const formNavData = { sendId: additionalData?.sendId, formId: additionalData?.formId };
          this.handleDirectFormNavigation(formNavData, additionalData);
        }
        break;
      case Constants.applessFlowMode.document:
        if (additionalData?.documentId && additionalData?.tenantId) {
          const docNavData = {
            id: parseInt(additionalData?.documentId, 10),
            senderTenant: parseInt(additionalData?.tenantId, 10)
          };
          this.handleDirectDocumentNavigation(docNavData, additionalData);
        }
        break;
      case Constants.applessFlowMode.message:
        if (additionalData?.chatRoomId) {
          this.navigateToMessageChat(additionalData?.chatRoomId);
        }
        break;
      default:
        this.navigateToApplessHome(additionalData);
    }
  }

  navigateToApplessHome(additionalData: any): void {
    const stateData = {
      ...additionalData,
      fromDirectNavigationFailure: true
    };

    this.router.navigate([`.${PageRoutes.appLess}/home`], {
      skipLocationChange: false,
      state: stateData
    });
  }

  getFormRequestPayload(dateFilterOptions?: { selectedDateOptions?: any; dateRange?: any }): FormWorkListPayload {
    const payload: FormWorkListPayload = {
      roleid: this.sharedService.userData.roleId,
      zone: moment.tz.guess(),
      isForms: true,
      isPrivilege:
        this.permissionService.userHasPermission(Permissions.viewFormEntries) || this.permissionService.userHasPermission(Permissions.manageTenants),
      limit: Constants.offset,
      offset: Constants.offset * Constants.defaultPageCount,
      searchText: '',
      orderData: Constants.orderData.sentOn,
      orderby: Constants.sortOrderDesc,
      isScheduled: Constants.noValue,
      archived: false,
      pending: true,
      completed: false,
      draft: false,
      accessSecurityEnabled: this.sharedService.userData.accessSecurityEnabled,
      citusRoleId: this.sharedService.userData.group || '',
      enableIntegrationStatus: false,
      enableSftpIntegration: false,
      startDate: '',
      endDate: ''
    };

    // Apply date filtering if provided
    if (dateFilterOptions) {
      const dateParams = this.sharedService.getFilterDateRange(dateFilterOptions.selectedDateOptions, dateFilterOptions.dateRange);
      payload.startDate = dateParams.startDate || '';
      payload.endDate = dateParams.endDate || '';
    }

    if (this.sharedService.isEnableConfig(Config.enableNursingAgencies)) {
      payload.nursingAgencies = this.sharedService.userData.nursing_agencies;
    }

    return payload;
  }

  saveApplessDocumentHistory(params: any): void {
    if (params.appLessWorkFlow !== Constants.applessFlowMode.document) {
      return;
    }

    const payload = {
      activityName: params.activityName,
      documentId: params?.documentId || this.sharedService.applessDocumentId,
      sender: params.sender,
      recipient: this.sharedService.userData?.userId,
      senderName: '',
      recipientName: this.sharedService.userData?.displayName,
      recipientEmail: this.sharedService.userData?.userName,
      recipientPhoneNumber: this.sharedService.userData?.mobile,
      recipientCountryCode: this.sharedService.userData?.countryCode,
      sentDocumentVia: Constants.documentSignedMedium,
      accessCodeSentMedium: params.accessCodeSentMedium
    };

    // Determine which service to use based on activity type
    const isDocumentDownload = payload.activityName === Constants.documentActivityParams.documentDownload;
    const serviceCall = isDocumentDownload
      ? this.httpService.doPost({
          endpoint: APIs.addDocumentHistory,
          payload,
          loader: false
        })
      : this.graphqlService.createDocumentActivity(payload);

    if (serviceCall) {
      serviceCall.subscribe({
        next: () => {
          // History saved successfully
        },
        error: (error) => {
          this.sharedService.errorHandler(error);
        }
      });
    }
  }

  saveApplessFormHistory(formId: number, sendId: number): void {
    const historyRequest = {
      sendId,
      formId
    };

    this.httpService
      .doPost({
        endpoint: APIs.addFormHistory,
        payload: historyRequest,
        loader: false
      })
      .subscribe({
        next: () => {
          // Form download history saved successfully
        },
        error: (error) => {
          this.sharedService.errorHandler(error);
        }
      });
  }
}
