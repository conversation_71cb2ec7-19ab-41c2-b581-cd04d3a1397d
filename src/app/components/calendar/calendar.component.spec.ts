import { HttpClientModule } from '@angular/common/http';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterModule } from '@angular/router';
import { AlertController, IonicModule } from '@ionic/angular';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { Keepalive } from '@ng-idle/keepalive';
import { TranslateModule } from '@ngx-translate/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { TestConstants } from 'src/app/constants/test-constants';
import { Constants } from 'src/app/constants/constants';
import { VisitScheduleConstants } from 'src/app/constants/visit-schedule-constants';
import { CalendarComponent } from './calendar.component';
import { CommonService } from 'src/app/services/common-service/common.service';
import { VisitScheduleService } from 'src/app/services/visit-schedule-service/visit-schedule.service';
import { of } from 'rxjs';

describe('CalendarComponent', () => {
  let component: CalendarComponent;
  let fixture: ComponentFixture<CalendarComponent>;
  let service: SharedService;
  let alertController: AlertController;
  const { alertSpy } = TestConstants;
  let common: CommonService;
  let visitScheduleService: VisitScheduleService;
  const mockItem = [
    {
      color: '#a84c4c',
      end: '2022-06-28 13:00:00',
      end_date: '2022-06-28 13:00:00',
      eventId: '3667',
      eventVisitTitle: 'Title',
      id: '3667',
      myVisitFor: 'visitScheduler',
      patientName: 'QA2 Patient',
      patientVisitStatus: 'Not Confirmed',
      staffVisitStatus: 'Pending Staff/Partner Assignment',
      start: '2022-06-28 12:00:00',
      start_date: '2022-06-28 12:00:00',
      title: 'Title',
      visitId: '1135',
      visitTitle: 'Title'
    }
  ];
  let mockEvent: any = {
    backgroundColor: '#a84c4c',
    el: {
      style: {
        backgroundColor: '',
        display: '',
        flexWrap: '',
        overflow: '',
        padding: '',
        color: '',
        zIndex: ''
      }
    },
    view: {
      type: '',
      currentStart: '2022-06-28 12:00:00',
      currentEnd: '2022-06-29 12:00:00'
    }
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [CalendarComponent],
      imports: [IonicModule.forRoot(), TranslateModule.forRoot(), HttpClientModule, HttpClientTestingModule, RouterModule.forRoot([])],
      providers: [
        SharedService,
        NgxPermissionsService,
        NgxPermissionsStore,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined },
        Idle,
        IdleExpiry,
        Keepalive
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    service = TestBed.inject(SharedService);
    common = TestBed.inject(CommonService);
    visitScheduleService = TestBed.inject(VisitScheduleService);
    service.userData = {
      ...service.userData,
      mySites: [{ id: 1, name: 'Atlanta' }]
    };
    service.userData.siteConfigs = {
      ...service.userData.siteConfigs,
      timezoneName: 'Eastern time'
    };
    alertController = TestBed.inject(AlertController);
    spyOn(alertController, 'create').and.callFake(() => {
      return Promise.resolve(alertSpy);
    });
    alertSpy.present.and.returnValue(Promise.resolve());
    alertSpy.onWillDismiss.and.returnValue(Promise.resolve());
    spyOn(alertController, 'dismiss').and.stub();
    fixture = TestBed.createComponent(CalendarComponent);
    component = fixture.componentInstance;
    spyOn(component, 'ngOnChanges').and.callThrough();
    fixture.detectChanges();
    component.item = mockItem;
    component.ngOnChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('eventDidMount should be defined', () => {
    component.eventDidMount(mockEvent);
    expect(component.eventDidMount).toBeDefined();
  });

  it('eventMouseHover should be defined', () => {
    component.eventMouseHover(mockEvent);
    expect(component.eventMouseHover).toBeDefined();
  });

  it('eventMouseHover should check inlineFlex case', () => {
    component.flex = '';
    component.eventMouseHover(mockEvent);
    expect(component.eventMouseHover).toBeDefined();
  });

  it('eventClickFunction should be defined', () => {
    const objParam = {
      id: '3742',
      start: '2022-06-30T19:30:00+05:30',
      title: 'Test Event',
      backgroundColor: '#a84c4c',
      borderColor: '#a84c4c',
      end: '2022-06-30T20:30:00+05:30',
      extendedProps: {
        end_date: '2022-06-30 20:30:00',
        eventId: '3742',
        eventVisitTitle: 'Test event',
        start_date: '2022-06-30 19:30:00',
        tasks: 'Visit Task',
        visitId: '1206',
        visitTitle: 'Test'
      },
      _instance: {
        view: {
          end: '2022-06-30 19:30:00',
          start: '2022-06-30 20:30:00'
        }
      }
    };
    const args: any = {
      event: {
        ...objParam,
        toJSON: () => {
          return objParam;
        }
      }
    };
    component.eventClickFunction(args);
    expect(component.eventClickFunction).toBeDefined();
  });

  it('datesSetup should be defined', () => {
    component.datesSetup(mockEvent);
    expect(component.datesSetup).toBeDefined();
  });

  it('datesSetup should be defined with view type', () => {
    mockEvent.view.type = 'timeGridWeek';
    component.datesSetup(mockEvent);
    expect(component.datesSetup).toBeDefined();
    expect(component.currentCalendarType).toBe('timeGridWeek');
  });
  describe('setDefaultView', () => {
    it('setDefaultView should be defined timeGridWeek', () => {
      component.currentCalendarType = 'timeGridWeek';
      spyOn(common, 'showAlert').and.resolveTo(false);
      component.setDefaultView();
      expect(component.setDefaultView).toBeDefined();
    });

    it('setDefaultView should be defined dayGridMonth', () => {
      component.currentCalendarType = 'dayGridMonth';
      spyOn(common, 'showAlert').and.resolveTo(true);
      spyOn(visitScheduleService, 'setDefaultView').and.throwError('');
      component.setDefaultView();
      expect(component.setDefaultView).toBeDefined();
    });

    it('setDefaultView should be defined timeGridDay', () => {
      component.currentCalendarType = 'timeGridDay';

      spyOn(common, 'showAlert').and.resolveTo(true);
      const response = {
        success: true
      };
      component.setDefaultView();
      spyOn(visitScheduleService, 'setDefaultView').and.returnValue(of(response));
      visitScheduleService.setDefaultView('timeGridDay', 'preference', 'timezone').subscribe();
      expect(component.setDefaultView).toBeDefined();
    });

    it('setDefaultView should be defined month', () => {
      const response = {
        success: false
      };
      spyOn(visitScheduleService, 'setDefaultView').and.returnValue(of(response));
      spyOn(common, 'showMessage').and.stub();
      component.setDefaultView();
      expect(component.setDefaultView).toBeDefined();
    });
  });
  it('loading should be defined', () => {
    component.loading('', '');
    expect(component.loading).toBeDefined();
  });
  it('eventDidMount should be defined', () => {
    mockEvent = {
      ...mockEvent,
      isEnd: true,
      isStart: true
    };
    component.eventDidMount(mockEvent);
    expect(component.eventDidMount).toBeDefined();
  });
  it('should call ngOnInit with view visit page', () => {
    component.visitPage = 'view_visit';
    component.ngOnInit();
    expect(component.ngOnInit).toBeDefined();
  });

  it('should call ngOnInit with tenant time zone', () => {
    component.tenantTimezone = 'Asia/Kolkata';
    component.ngOnInit();
    expect(component.ngOnInit).toBeDefined();
  });
  describe('ngOnInit', () => {
    it('should call ngOnInit with visit default week view', () => {
      component.visitPage = 'view_visit';
      service.userData.userVisitDefaultView = 'week';
      component.ngOnInit();
      expect(component.ngOnInit).toBeDefined();
    });
    it('should call ngOnInit with visit default month view', () => {
      component.visitPage = 'view_visit';
      service.userData.userVisitDefaultView = 'month';
      component.ngOnInit();
      expect(component.ngOnInit).toBeDefined();
    });
    it('should call ngOnInit with visit default timeGridDay view', () => {
      component.visitPage = 'view_visit';
      service.userData.userVisitDefaultView = 'timeGridDay';
      component.ngOnInit();
      expect(component.ngOnInit).toBeDefined();
    });
  });

  describe('eventContent function', () => {
    let mockEventArg: any;

    beforeEach(() => {
      mockEventArg = {
        timeText: '10:00 AM',
        event: {
          title: 'Default Title',
          extendedProps: {
            myVisitFor: 'visitScheduler',
            visitTitle: 'Physical Therapy',
            patientName: 'John Doe',
            visitType: 'Home Visit'
          }
        }
      };
    });

    describe('when visitPage is NOT viewVisit (uses <br> format)', () => {
      beforeEach(() => {
        component.visitPage = 'some_other_page'; // Not viewVisit
        component.ngOnInit(); // Initialize calendar options
      });

      it('should format event content with visitTitle present', () => {
        const eventContentFunction = component.calendarOptions.eventContent as Function;
        const result = eventContentFunction(mockEventArg);

        expect(result.html).toBe('Physical Therapy <br> 10:00 AM <br> Default Title');
      });

      it('should handle missing visitTitle and use event title', () => {
        mockEventArg.event.extendedProps.visitTitle = '';
        mockEventArg.event.title = 'Fallback Title';

        const eventContentFunction = component.calendarOptions.eventContent as Function;
        const result = eventContentFunction(mockEventArg);

        expect(result.html).toBe('Fallback Title <br> 10:00 AM');
      });

      it('should handle patient visit time correctly', () => {
        mockEventArg.event.extendedProps.myVisitFor = Constants.myVisitPatient;
        mockEventArg.timeText = '10:00 AM - 11:00 AM';

        const eventContentFunction = component.calendarOptions.eventContent as Function;
        const result = eventContentFunction(mockEventArg);

        expect(result.html).toBe('Physical Therapy <br> 10:00  ');
      });
    });

    describe('when visitPage is viewVisit (uses pipe format)', () => {
      beforeEach(() => {
        component.visitPage = VisitScheduleConstants.viewVisit;
        component.ngOnInit(); // Initialize calendar options
      });

      it('should format event content with all fields present', () => {
        const eventContentFunction = component.calendarOptions.eventContent as Function;
        const result = eventContentFunction(mockEventArg);

        expect(result.html).toBe('Physical Therapy | John Doe | 10:00 AM | Home Visit');
      });

      it('should handle missing visitTitle and use event title as fallback', () => {
        mockEventArg.event.extendedProps.visitTitle = '';
        mockEventArg.event.title = 'Fallback Title';

        const eventContentFunction = component.calendarOptions.eventContent as Function;
        const result = eventContentFunction(mockEventArg);

        expect(result.html).toBe('Fallback Title | John Doe | 10:00 AM | Home Visit');
      });

      it('should handle missing patientName gracefully', () => {
        mockEventArg.event.extendedProps.patientName = '';

        const eventContentFunction = component.calendarOptions.eventContent as Function;
        const result = eventContentFunction(mockEventArg);

        expect(result.html).toBe('Physical Therapy | 10:00 AM | Home Visit');
      });

      it('should handle missing visitType gracefully', () => {
        mockEventArg.event.extendedProps.visitType = '';

        const eventContentFunction = component.calendarOptions.eventContent as Function;
        const result = eventContentFunction(mockEventArg);

        expect(result.html).toBe('Physical Therapy | John Doe | 10:00 AM');
      });

      it('should handle missing time gracefully', () => {
        mockEventArg.timeText = '';

        const eventContentFunction = component.calendarOptions.eventContent as Function;
        const result = eventContentFunction(mockEventArg);

        expect(result.html).toBe('Physical Therapy | John Doe | Home Visit');
      });

      it('should process patient visit time correctly by taking first part', () => {
        mockEventArg.event.extendedProps.myVisitFor = Constants.myVisitPatient;
        mockEventArg.timeText = '10:00 AM - 11:00 AM';

        const eventContentFunction = component.calendarOptions.eventContent as Function;
        const result = eventContentFunction(mockEventArg);

        expect(result.html).toBe('Physical Therapy | John Doe | 10:00 | Home Visit');
      });

      it('should handle patient visit with single time format', () => {
        mockEventArg.event.extendedProps.myVisitFor = Constants.myVisitPatient;
        mockEventArg.timeText = '10:00 AM';

        const eventContentFunction = component.calendarOptions.eventContent as Function;
        const result = eventContentFunction(mockEventArg);

        expect(result.html).toBe('Physical Therapy | John Doe | 10:00 | Home Visit');
      });

      it('should handle all fields missing except title', () => {
        mockEventArg.event.extendedProps = {
          myVisitFor: 'visitScheduler',
          visitTitle: '',
          patientName: '',
          visitType: ''
        };
        mockEventArg.event.title = 'Only Title';
        mockEventArg.timeText = '';

        const eventContentFunction = component.calendarOptions.eventContent as Function;
        const result = eventContentFunction(mockEventArg);

        expect(result.html).toBe('Only Title');
      });

      it('should handle whitespace-only fields by trimming them', () => {
        mockEventArg.event.extendedProps.visitTitle = '  ';
        mockEventArg.event.extendedProps.patientName = '   ';
        mockEventArg.event.extendedProps.visitType = ' ';
        mockEventArg.event.title = 'Trimmed Title';

        const eventContentFunction = component.calendarOptions.eventContent as Function;
        const result = eventContentFunction(mockEventArg);

        // With simplified logic, whitespace visitTitle is truthy, so it won't fall back to event.title
        // Only time will be shown since other fields are whitespace and get trimmed out
        expect(result.html).toBe('10:00 AM');
      });

      it('should return proper HTML content structure', () => {
        const eventContentFunction = component.calendarOptions.eventContent as Function;
        const result = eventContentFunction(mockEventArg);

        expect(result.html).toBeDefined();
        expect(typeof result.html).toBe('string');
      });

      it('should handle undefined extendedProps by throwing error', () => {
        mockEventArg.event.extendedProps = undefined;
        mockEventArg.event.title = 'Safe Title';

        const eventContentFunction = component.calendarOptions.eventContent as Function;

        // With simplified destructuring, undefined extendedProps will throw an error
        expect(() => eventContentFunction(mockEventArg)).toThrow();
      });

      it('should handle null values in extendedProps', () => {
        mockEventArg.event.extendedProps = {
          myVisitFor: 'visitScheduler',
          visitTitle: null,
          patientName: null,
          visitType: null
        };
        mockEventArg.event.title = 'Null Safe Title';

        const eventContentFunction = component.calendarOptions.eventContent as Function;
        const result = eventContentFunction(mockEventArg);

        expect(result.html).toBe('Null Safe Title | 10:00 AM');
      });

      it('should verify myVisitPatient constant comparison', () => {
        // Test that the constant comparison works correctly
        mockEventArg.event.extendedProps.myVisitFor = Constants.myVisitPatient;
        mockEventArg.timeText = '2:30 PM - 3:30 PM';

        const eventContentFunction = component.calendarOptions.eventContent as Function;
        const result = eventContentFunction(mockEventArg);

        // Should split the time and take first part (note: split removes AM/PM)
        expect(result.html).toContain('2:30');
        expect(result.html).not.toContain('3:30');
      });

      it('should handle empty string values correctly', () => {
        mockEventArg.event.extendedProps = {
          myVisitFor: 'visitScheduler',
          visitTitle: '',
          patientName: '',
          visitType: ''
        };
        mockEventArg.event.title = '';
        mockEventArg.timeText = '';

        const eventContentFunction = component.calendarOptions.eventContent as Function;
        const result = eventContentFunction(mockEventArg);

        expect(result.html).toBe('');
      });

      it('should handle only visitType present', () => {
        mockEventArg.event.extendedProps = {
          myVisitFor: 'visitScheduler',
          visitTitle: '',
          patientName: '',
          visitType: 'Telehealth'
        };
        mockEventArg.event.title = '';
        mockEventArg.timeText = '';

        const eventContentFunction = component.calendarOptions.eventContent as Function;
        const result = eventContentFunction(mockEventArg);

        expect(result.html).toBe('Telehealth');
      });

      it('should handle mixed empty and filled fields', () => {
        mockEventArg.event.extendedProps = {
          myVisitFor: 'visitScheduler',
          visitTitle: 'Consultation',
          patientName: '',
          visitType: 'In-Person'
        };
        mockEventArg.timeText = '3:00 PM';

        const eventContentFunction = component.calendarOptions.eventContent as Function;
        const result = eventContentFunction(mockEventArg);

        expect(result.html).toBe('Consultation | 3:00 PM | In-Person');
      });
    });
  });
});
