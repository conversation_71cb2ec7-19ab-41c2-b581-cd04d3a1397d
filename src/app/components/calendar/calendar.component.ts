import { FullCalendarComponent } from '@fullcalendar/angular';
import { CalendarOptions, Calendar, DatesSetArg, EventMountArg, EventHoveringArg, EventClickArg } from '@fullcalendar/core';
import { Component, EventEmitter, Input, OnInit, Output, ViewChild, OnChanges } from '@angular/core';
import * as moment from 'moment';
import { Constants } from 'src/app/constants/constants';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { Config } from 'src/app/constants/config';
import { isPresent } from 'src/app/utils/utils';
import { VisitScheduleConstants } from 'src/app/constants/visit-schedule-constants';
import { CommonService } from 'src/app/services/common-service/common.service';
import { VisitScheduleService } from 'src/app/services/visit-schedule-service/visit-schedule.service';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import listPlugin from '@fullcalendar/list';
import interactionPlugin from '@fullcalendar/interaction';
import { PermissionService } from 'src/app/services/permission-service/permission.service';
import { Permissions } from 'src/app/constants/permissions';

@Component({
  selector: 'app-calendar',
  templateUrl: './calendar.component.html',
  styleUrls: ['./calendar.component.scss']
})
export class CalendarComponent implements OnInit, OnChanges {
  @Input() item: any;
  @ViewChild('calendar') calendarComponent: FullCalendarComponent;
  @Output() readonly parentFunction: EventEmitter<any> = new EventEmitter();
  @Output() readonly setCalendarView: EventEmitter<any> = new EventEmitter();
  @Input() startIntervalTime: any;
  @Input() endIntervalTime: any;
  @Input() tenantTimezone: string;
  @Input() visitsTimezone: string;
  @Input() visitsPreference: string;
  events = [];
  calendarOptions: CalendarOptions;
  calendar: Calendar;
  datesSet = Constants.datesSet;
  eventClick = Constants.eventClick;
  dateFormat = Constants.dateFormat;
  whiteColor = Constants.whiteColor;
  flex = Constants.flex;
  inlineFlex = Constants.inlineFlex;
  wrapReverse = Constants.wrapReverse;
  hidden = Constants.hidden;
  calendarEventPadding = Constants.calendarEventPadding;
  siteTimeZone: string;
  userData: any;
  @Input() visitPage: string;
  currentCalendarType: string;

  constructor(
    private sharedService: SharedService,
    private commonService: CommonService,
    private visitScheduleService: VisitScheduleService,
    private permissionService: PermissionService
  ) {
    this.siteTimeZone = this.sharedService.getSiteConfigValue(Config.timezoneName);
    this.userData = this.sharedService.userData;
  }
  ngOnChanges(): void {
    if (this.item) {
      this.events = this.item.filter((x) => x.id);
      this.calendarOptions.events = this.events;
      // Force the calendar to redraw if the calendar component is available
      this.forceCalendarRender();
    }
  }

  forceCalendarRender(): void {
    setTimeout(() => {
      if (this.calendarComponent && this.calendarComponent.getApi()) {
        const api = this.calendarComponent.getApi();
        api.removeAllEvents();
        api.addEventSource(this.events);
        // Update the calendar's current view
        api.updateSize();
        api.render();
      }
    }, 0);
  }

  ngOnInit(): void {
    this.calendarOptions = {
      plugins: [dayGridPlugin, interactionPlugin, timeGridPlugin, listPlugin],
      timeZone: isPresent(this.tenantTimezone) ? this.tenantTimezone : this.siteTimeZone,
      contentHeight: 'auto',
      initialView:
        this.visitPage === VisitScheduleConstants.viewVisit && this.userData.userVisitDefaultView
          ? this.userData.userVisitDefaultView === VisitScheduleConstants.week
            ? VisitScheduleConstants.timeGridWeek
            : this.userData.userVisitDefaultView === VisitScheduleConstants.month
            ? VisitScheduleConstants.dayGridMonth
            : VisitScheduleConstants.timeGridDay
          : VisitScheduleConstants.dayGridMonth,
      expandRows: true,
      selectable: true,
      nowIndicator: true,
      dayMaxEventRows: true,
      views: {
        dayGrid: {
          dayMaxEventRows: 2,
          titleFormat: { year: 'numeric', month: 'long' }
        },
        week: {
          eventMaxStack: 0,
          titleFormat: { month: 'short', day: 'numeric', year: 'numeric' },
          allDaySlot: false
        },
        day: {
          eventMaxStack: 1,
          titleFormat: { month: 'long', day: 'numeric', year: 'numeric' },
          allDaySlot: false
        }
      },
      headerToolbar: {
        start: 'prev,next',
        center: 'title',
        end:
          this.visitPage === VisitScheduleConstants.viewVisit
            ? 'dayGridMonth,timeGridWeek,timeGridDay,setDefaultViewButton'
            : 'dayGridMonth,timeGridWeek,timeGridDay'
      },
      showNonCurrentDates: false,
      eventClick: this.eventClickFunction.bind(this),
      events: this.events,
      eventDidMount: this.eventDidMount.bind(this),
      loading: this.loading.bind(this),
      datesSet: this.datesSetup.bind(this),
      displayEventEnd: true,
      progressiveEventRendering: true,
      eventMouseEnter: this.eventMouseHover.bind(this),
      eventMouseLeave: this.eventMouseHover.bind(this),
      eventTimeFormat: {
        hour12: true,
        hour: 'numeric',
        minute: 'numeric',
        omitZeroMinute: true,
        meridiem: 'narrow'
      },
      eventContent: (arg) => {
        const { extendedProps } = arg.event;
        const isPatient = extendedProps.myVisitFor === Constants.myVisitPatient;
        const timeText = isPatient ? arg.timeText.split(' ')[0] : arg.timeText;
        if (this.visitPage !== VisitScheduleConstants.viewVisit) {
          return {
            html: extendedProps.visitTitle
              ? `${extendedProps.visitTitle} ${Constants.break} ${timeText} ${isPatient ? '' : Constants.break} ${isPatient ? '' : arg.event.title}`
              : `${arg.event.title} ${Constants.break} ${timeText}`
          };
        }
        const visitTitle = extendedProps.visitTitle || arg.event.title || '';
        const patientName = extendedProps.patientName || '';
        const time = timeText || '';
        const visitType = extendedProps.visitType || '';

        const contentItems = [];
        if (visitTitle && visitTitle.trim()) contentItems.push(visitTitle);
        if (patientName && patientName.trim()) contentItems.push(patientName);
        if (time && time.trim()) contentItems.push(time);
        if (visitType && visitType.trim()) contentItems.push(visitType);
        const content = contentItems.join(' | ');

        return {
          html: content
        };
      }
    };
    if (this.visitPage === VisitScheduleConstants.viewVisit) {
      this.calendarOptions = {
        ...this.calendarOptions,
        customButtons: {
          setDefaultViewButton: {
            text: this.commonService.getTranslateData('BUTTONS.SET_AS_DEFAULT_VIEW'),
            click: () => this.setDefaultView()
          }
        },
        dateClick: (info) => {
          const hasPermission =
            this.permissionService.userHasPermission(Permissions.allowStaffToScheduleForThemselves) ||
            this.permissionService.userHasPermission(Permissions.manageVisitSchedule);
          if (!hasPermission) return;

          // Extract date and time components
          const { dateStr } = info;
          const hasTime = dateStr.includes('T');

          let selectedDate;
          let selectedTime;

          if (hasTime) {
            [selectedDate, selectedTime] = dateStr.split('T');
          } else {
            selectedDate = dateStr;
            selectedTime = '';
          }

          const dateTimeParam = hasTime ? { date: selectedDate, time: selectedTime } : { date: selectedDate };
          this.parentFunction.emit({
            operation: 'dateClick',
            dateTimeParam
          });
        }
      };
    }
  }
  eventDidMount(data: EventMountArg): void {
    if (data && 'backgroundColor' in data && data.backgroundColor) {
      data.el.style.backgroundColor = data.backgroundColor;
      if (this.visitPage === VisitScheduleConstants.viewVisit) {
        data.el.style.color = data.textColor;
        data.el.style.borderColor = data.textColor;
        data.el.style.boxShadow = `0 0px 2px ${data.textColor}`;
        data.el.style.flexWrap = Constants.wrap;
        data.el.style.overflow = 'visible';
        data.el.style.padding = '0 2px';
        data.el.style.overflowWrap = 'break-word';
        data.el.style.wordBreak = 'normal';
        data.el.style.whiteSpace = 'normal';
      } else {
        data.el.style.color = data.el.style.color ? data.el.style.color : this.whiteColor;
        data.el.style.border = data.isEnd && data.isStart ? '1px solid #3a87ad' : 'unset';
        data.el.style.flexWrap = this.wrapReverse;
        data.el.style.overflow = this.hidden;
        data.el.style.padding = this.calendarEventPadding;
      }
      data.el.style.display = this.flex;
      data.el.style.fontWeight = 'bold';
      data.el.style.fontSize = '10px';
    }
  }
  eventMouseHover(data: EventHoveringArg): void {
    switch (data.el.style.display) {
      case this.flex:
        data.el.style.display = this.inlineFlex;
        data.el.style.zIndex = '7';
        break;
      case this.inlineFlex:
        data.el.style.display = this.flex;
        data.el.style.zIndex = '6';
        break;
    }
  }
  eventClickFunction(arg: EventClickArg): void {
    const event = arg.event.toJSON();
    this.parentFunction.emit({
      event: arg.event.extendedProps,
      range: arg.event._instance.range,
      ...event.extendedProps,
      id: event.id,
      start: event.start,
      end: event.end,
      operation: this.eventClick
    });
  }
  loading(isLoading: any, view: any): void {
    // do nothing
  }
  datesSetup(view: DatesSetArg): void {
    if (this.visitPage === VisitScheduleConstants.viewVisit) {
      this.startIntervalTime = moment(view.startStr).format(this.dateFormat.ymd);
      this.endIntervalTime = moment(view.endStr).subtract(1, 'second').format(this.dateFormat.ymd);
    } else {
      this.startIntervalTime = moment(view.view.currentStart).add(-1, 'day').format(this.dateFormat.ymd0);
      this.endIntervalTime = moment(view.view.currentEnd).add(1, 'day').format(this.dateFormat.ymd0);
    }
    const param = {
      startIntervalTime: this.startIntervalTime,
      endIntervalTime: this.endIntervalTime,
      view: view.view.type
    };
    this.parentFunction.emit({
      operation: this.datesSet,
      param
    });
    if (view.view && view.view.type) {
      this.currentCalendarType = view.view.type;
    }
  }
  setDefaultView() {
    const buttons = [
      { text: 'BUTTONS.CANCEL', confirm: false, class: 'warn-btn alert-cancel' },
      { text: 'BUTTONS.OK', confirm: true, class: 'warn-btn alert-ok' }
    ];
    const setView =
      this.currentCalendarType === VisitScheduleConstants.timeGridWeek
        ? VisitScheduleConstants.week
        : this.currentCalendarType === VisitScheduleConstants.dayGridMonth
        ? VisitScheduleConstants.month
        : this.currentCalendarType === VisitScheduleConstants.timeGridDay
        ? VisitScheduleConstants.day
        : VisitScheduleConstants.month;
    this.commonService
      .showAlert({
        message: 'LABELS.DEFAULT_VISIT_VIEW',
        header: 'MESSAGES.ARE_YOU_SURE',
        buttons,
        mode: 'ios',
        cssClass: 'visit-alert'
      })
      .then((confirmation) => {
        if (confirmation) {
          this.visitScheduleService.setDefaultView(setView, this.visitsPreference, this.visitsTimezone).subscribe((resp) => {
            if (resp.success) {
              this.userData.userVisitDefaultView = setView;
              this.commonService.showMessage(
                this.commonService.getTranslateData('SUCCESS_MESSAGES.DEFAULT_VISIT_VIEW_SUCCESS')
              );
            } else {
              this.commonService.showMessage(
                this.commonService.getTranslateData('ERROR_MESSAGES.ERROR_MSG_EXCEPTION_CATCH')
              );
            }
          });
        } else {
          this.commonService.closeAllAlert();
        }
      });
  }
}
