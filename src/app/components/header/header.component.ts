import { Component, OnInit, OnDestroy, Input, NgZone, ViewChild, ElementRef, Optional } from '@angular/core';
import { getComputedStyleProperty, isBlank } from 'src/app/utils/utils';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { Activity } from 'src/app/constants/activity';
import Elevio from 'elevio/lib/client';
import { Config } from 'src/app/constants/config';
import { Constants, UserGroup } from 'src/app/constants/constants';
import { SocketPopoverComponent } from '../socket-popover/socket-popover.component';
import { PopoverController } from '@ionic/angular';
import { SocketService } from 'src/app/services/socket-service/socket.service';
import { isVirtual } from 'src/app/utils/storage-utils';
import { PageRoutes } from 'src/app/constants/page-routes';
import { InAppBrowser, InAppBrowserObject } from '@awesome-cordova-plugins/in-app-browser/ngx';


@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss']
})
export class HeaderComponent implements OnInit, OnDestroy {
  @Input() headerTitle: string;
  @Input() subHead: string;
  @Input() headerImage = false;
  @Input() hideSocketButton = false;
  @Input() hideHelpButton = false;
  showHelpButton = false;
  showSocketButton = false;
  scrollTitle = false;
  socketStatus = true;
  helpCenterStatus = false;
  currentPopover = null;
  isVirtual = false;
  showTutorialButton = false;
  tutorialLink = '';
  @Input() closeSocketPopover: boolean;
  @ViewChild('titleClick') titleInput: ElementRef;
  private tlmsBrowser: InAppBrowserObject | null = null;
  private tlmsPollingInterval: ReturnType<typeof setTimeout> = null;
  constructor(
    public sharedService: SharedService,
    private socketService: SocketService,
    private popoverController: PopoverController,
    private readonly ngZone: NgZone,
    @Optional() private inAppBrowser: InAppBrowser
  ) {
    this.sharedService.configValuesUpdated.subscribe(() => {
      this.showHelpButton = this.showElevio;
      this.showTutorialButton = this.showTutorial;
      this.tutorialLink = this.sharedService.getConfigValue(Config.talentlmsUrl);
    });
  }

  ngOnInit(): void {
    this.isVirtual = isVirtual();
    this.sharedService.pageAccess(this.subHead ? this.subHead : this.headerTitle);
    this.showHelpButton = this.showElevio;
    this.showTutorialButton = this.showTutorial;
    this.tutorialLink = this.sharedService.getConfigValue(Config.talentlmsUrl);
    this.showSocketButton =
      (!this.isVirtual || location.href.includes(PageRoutes.applessURLPathMessage)) && !this.hideSocketButton;
    this.socketStatus = this.socketService.status;
    this.socketService.socketStatusUpdated.subscribe(() => {
      this.socketStatus = this.socketService.status;
    });
    if (this.showElevio) {
      this.initializeElevio();
    }
  }
  get showTutorial(): boolean {
   const isOktaEnabled = localStorage.getItem(Constants.storageKeys.oktaLogin) && this.sharedService.isEnableConfig(Config.enableIDM);
    const isSSOEnabled = sessionStorage.getItem('isSSOLogin') === 'true';
    return this.sharedService.isEnableConfig(Config.showTalentlmsTutorial) &&
     +this.sharedService?.userData.group !== UserGroup.PATIENT &&
      (isOktaEnabled || isSSOEnabled);
  }
  openTLMS(): void {
    if (this.sharedService.platform.is('capacitor')) {
      if (this.sharedService.platform.is('ios')) {
        //console.log('ios8158');
        this.openTLMSInAppBrowser();
      } else {
        this.openTLMSInAppBrowser();
      }
    } else {
      window.open(this.tutorialLink, '_blank');
    }
    this.sharedService.trackActivity({
      type: Activity.openHelpCenter,
      name: Activity.helpCenter,
      des: {
        data: {
          firstName: this.sharedService.userData?.firstName,
          secondName: this.sharedService.userData?.secondName,
          userName: this.sharedService.userData?.userName,
          tutorialLink: this.tutorialLink
        },
        desConstant: Activity.helpCenterDes
      }
    });
  }
  openTLMSInAppBrowser(): void {
    try {
      this.tlmsBrowser = this.inAppBrowser.create(this.tutorialLink, '_blank', {
        location: 'no',
        toolbar: 'yes',
        clearcache: 'no',
        zoom: 'no',
        closebuttoncolor: Constants.whiteColor,
        toolbarcolor: getComputedStyleProperty('--ion-color-primary'),
        closebuttoncaption: 'Back to App',
        navigationbuttoncolor: Constants.whiteColor,
        enableViewportScale: 'yes',
        mediaPlaybackRequiresUserAction: 'no',
        allowInlineMediaPlaybook: 'yes',
        presentationstyle: 'pagesheet',
        transitionstyle: 'fliphorizontal'
      });

      const browser = this.tlmsBrowser;
      this.startTLMSPolling();

      browser.on('loadstop').subscribe(() => {
        this.injectTLMSEnhancements(browser);
      });

      browser.on('loaderror').subscribe(() => {
        browser.close();
      });

      browser.on('exit').subscribe(() => {
        this.tlmsBrowser = null;
        this.stopTLMSPolling();

        this.sharedService.trackActivity({
          type: Activity.openHelpCenter,
          name: Activity.helpCenter,
          des: {
            data: {
              action: 'TLMS_BROWSER_CLOSED',
              tutorialLink: this.tutorialLink
            },
            desConstant: 'User closed TLMS browser'
          }
        });
      });
    } catch {
     // alert('in app browser error');
      window.open(this.tutorialLink, '_blank');
    }
  }

  startTLMSPolling(): void {
    if (this.tlmsPollingInterval) {
      clearInterval(this.tlmsPollingInterval);
    }

    if (!this.tlmsBrowser) {
      return;
    }

    this.tlmsPollingInterval = setInterval(() => {
      if (this.tlmsBrowser) {
        this.tlmsBrowser
          .executeScript({ code: "localStorage.getItem('tlms_close_request');" })
          .then((values) => {
            if (values && values.indexOf('true') > -1) {
              this.tlmsBrowser?.executeScript({ code: "localStorage.setItem('tlms_close_request', '');" });
              this.closeTLMSBrowser();
            }
          })
          .catch(() => {});
      } else {
        this.stopTLMSPolling();
      }
    }, 1000);
  }

  stopTLMSPolling(): void {
    if (this.tlmsPollingInterval) {
      clearInterval(this.tlmsPollingInterval);
      this.tlmsPollingInterval = null;
    }
  }

  closeTLMSBrowser(): void {
    if (this.tlmsBrowser) {
      try {
        this.tlmsBrowser.close();
        this.tlmsBrowser = null;
      } catch {}
    }
    this.stopTLMSPolling();
  }

  ngOnDestroy(): void {
    this.stopTLMSPolling();
    if (this.tlmsBrowser) {
      try {
        this.tlmsBrowser.close();
      } catch {}
    }
  }

  private injectTLMSEnhancements(browser: InAppBrowserObject): void {
    browser.executeScript({
      code: `
        let mobileStyle = document.createElement('style');
        mobileStyle.innerHTML = \`
          body {
            font-size: 16px !important;
            -webkit-text-size-adjust: 100% !important;
          }
          input, textarea, select {
            font-size: 16px !important;
            -webkit-appearance: none !important;
          }
          #tlms-back-button {
            position: fixed !important;
            top: 15px !important;
            left: 15px !important;
            z-index: 99999 !important;
            background: ${getComputedStyleProperty('--ion-color-primary')} !important;
            color: white !important;
            border: 2px solid white !important;
            border-radius: 30px !important;
            padding: 14px 24px !important;
            font-size: 16px !important;
            font-weight: bold !important;
            box-shadow: 0 6px 20px rgba(0,0,0,0.4) !important;
            cursor: pointer !important;
            transition: all 0.2s ease !important;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
            display: flex !important;
            align-items: center !important;
            gap: 8px !important;
            min-height: 48px !important;
            min-width: 120px !important;
            justify-content: center !important;
            text-align: center !important;
            user-select: none !important;
            -webkit-user-select: none !important;
            -webkit-tap-highlight-color: transparent !important;
            transform: scale(1) !important;
          }
          #tlms-back-button:active {
            transform: scale(0.95) !important;
            opacity: 0.8 !important;
          }
          #tlms-back-button:hover {
            opacity: 0.9 !important;
          }
        \`;
        document.head.appendChild(mobileStyle);
      `
    });

    browser.executeScript({
      code: `
        let backButton = document.createElement('button');
        backButton.id = 'tlms-back-button';
        backButton.innerHTML = '← Back to App';
        backButton.title = 'Return to CitusHealth App';
        backButton.type = 'button';

        function closeTLMSBrowser() {
          try {
            localStorage.setItem('tlms_close_request', 'true');
            if (window.parent && window.parent !== window) {
              window.parent.postMessage({type: 'CLOSE_TLMS_BROWSER'}, '*');
            }
            if (typeof window.close === 'function') {
              setTimeout(() => window.close(), 100);
            }
            if (window.cordova && window.cordova.InAppBrowser && window.cordova.InAppBrowser.close) {
              window.cordova.InAppBrowser.close();
            }
            if (window.history.length > 1) {
              setTimeout(() => window.history.back(), 200);
            }
            return true;
          } catch (e) {
            try {
              localStorage.setItem('tlms_close_request', 'true');
            } catch (storageError) {}
            return false;
          }
        }

        backButton.addEventListener('click', function(e) {
          e.preventDefault();
          e.stopPropagation();
          backButton.innerHTML = '⏳ Closing...';
          backButton.style.opacity = '0.7';
          if (!closeTLMSBrowser()) {
            setTimeout(() => {
              backButton.innerHTML = '← Back to App';
              backButton.style.opacity = '1';
              alert('Please tap the X button in the top toolbar to return to the CitusHealth app.');
            }, 1000);
          }
        });

        document.body.appendChild(backButton);
      `
    });
  }
  get showElevio(): boolean {
    return this.sharedService.isEnableConfig(Config.showElevio) && !this.isVirtual && !this.hideHelpButton;
  }
  get checkPath(): boolean {
    const pathCheck = ['/home', '/message-center/messages/active'].includes(location.pathname);
    if (pathCheck) {
      this.sharedService.hideMessageCount = true;
    }
    return !pathCheck;
  }
  initializeElevio(): void {
    let configGroups = [];
    const appType = this.sharedService.platformValue === Constants.platform.web ? Constants.platform.desktop : Constants.platform.mobile;
    let groups = [Constants.platformUser, `${Constants.appType}:${appType}`];
    const isPatient = this.sharedService.userData?.group;
    if (this.sharedService.userData?.config && this.sharedService.isEnableConfig(Config.showElevio)) {
      if (Number(isPatient) === Constants.patientGroupId) {
        groups.push(`${Constants.userTypeKey}:${Constants.inviteTab.patient}`);
        if (!isBlank(this.sharedService.getConfigValue(Config.elevioPatient))) {
          configGroups = this.sharedService.getConfigValue(Config.elevioPatient).split(',');
        }
      }
      if (Number(isPatient) !== Constants.patientGroupId && Number(isPatient) !== Constants.partnerGroupId) {
        groups.push(`${Constants.userTypeKey}:${Constants.inviteTab.staff}`);
        if (!isBlank(this.sharedService.getConfigValue(Config.elevioStaff))) {
          configGroups = this.sharedService.getConfigValue(Config.elevioStaff).split(',');
        }
      }
      if (Number(isPatient) === Constants.partnerGroupId) {
        groups.push(`${Constants.userTypeKey}:${Constants.inviteTab.partner}`);
        if (!isBlank(this.sharedService.getConfigValue(Config.elevioPartner))) {
          configGroups = this.sharedService.getConfigValue(Config.elevioPartner).split(',');
        }
      }
      groups = groups.concat(configGroups);
      groups = groups.filter((item, pos) => groups.indexOf(item) === pos);
    }

    const mainColor = getComputedStyleProperty('--ion-color-elvio-header');
    const userIdE = `${this.sharedService.userData?.userId} - ${this.sharedService.userData?.tenantName}`;
    this.ngZone.runOutsideAngular(() => {
      this.ngZone.run(() => {
        Elevio.load(Constants.elevioKey).then(() => {
          Elevio.on('load', (elev) => {
            elev.setSettings({
              hideLauncher: true,
              disableDevelopmentWarnings: true,
              main_color: mainColor
            });
            elev.setUser({
              first_name: userIdE,
              groups
            });
          });
        });
      });
    });
  }
  enableHelpCenter(): void {
    this.helpCenterStatus = true;
    this.ngZone.runOutsideAngular(() => {
      this.ngZone.run(() => {
        Elevio.enable();
        (window as any)._elev?.open();
      });
    });
    this.sharedService.trackActivity({
      type: Activity.openHelpCenter,
      name: Activity.helpCenter,
      des: {
        data: {
          firstName: this.sharedService.userData.firstName,
          secondName: this.sharedService.userData.secondName,
          userName: this.sharedService.userData.userName
        },
        desConstant: Activity.helpCenterDes
      }
    });
  }
  checkTitleScroll(): void {
    this.scrollTitle = this.titleInput.nativeElement.offsetWidth < this.titleInput.nativeElement.scrollWidth;
  }
  async socketPopover(ev?: any): Promise<any> {
    if (this.helpCenterStatus) {
      (window as any)._elev.close();
      this.helpCenterStatus = false;
    }
    this.socketService.isConnected();
    this.socketStatus = this.socketService.status;
    const popover = await this.popoverController.create({
      id: 'socket-status',
      component: SocketPopoverComponent,
      event: ev,
      cssClass: 'event-popover',
      componentProps: {},
      showBackdrop: true,
      translucent: false,
      backdropDismiss: false,
      mode: 'ios'
    });
    this.currentPopover = popover;
    await popover.present();
  }
}
