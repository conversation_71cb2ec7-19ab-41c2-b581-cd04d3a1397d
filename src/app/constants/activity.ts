export class Activity {
  static start = 'Start';
  static pageNavigation = 'Page Navigation';
  static pageAccess = 'page access';
  static page = 'Previous Page - {previousPage}. currentPage - {currentPage}';
  static userAccess = 'user access';
  static userLogin = 'User Login';
  static ssoLogin = 'SSO Login';
  static userLogout = 'User Logout';
  static sessionTimeout = 'Session Timeout';
  static sessionRestored = 'Session Restored';
  static sessionRestoredDes = 'User {username} restored session by clicking continue session';
  static urlFirstHit = 'URL First Hit';
  static download = 'Download';
  static downloadPage = 'Download Page';
  static downloadFetch = 'Download url fetch';
  static downloadFetchDes = 'User attempt to fetch endpoint {endpoint} with params {params}';
  static downloadFetchFail = 'Download Page';
  static downloadFetchFailBeforeParseDes =
    'User attempt to fetch endpoint {endpoint} with params {params} failed before parsing blob';
  static downloadFetchFailDes =
    'User attempt to fetch endpoint {endpoint} with params {params} failed with error ({error})';
  static sessionTimeoutDes = 'User ({userEmail}) session has expired due to inactivity';
  static sessionTimeoutFormDraftSuccessDes =
    'User ({userEmail}) session has expired due to inactivity,form unsaved changes were saved to draft';
  static sessionTimeoutFormDraftFailedDes =
    'User ({userEmail}) session has expired due to inactivity, form unsaved changes were not saved.';
  static login = 'User logged in successfully ({userName}). User active in {activeLogins} device(s)';
  static ssologinDes =
    'SSO Config fetched for username {username} (Client id: {clientId}, Redirect URL: {redirectUri}, Base URL: {baseUrl})';
  static logout = 'User logged out from {currentPage} ({userName})';
  static communicationCreated = 'video communication Created';
  static communicationCreatedDes = '{displayName}({userId}) has created a vidyo connector object for video chat';
  static communicationError = 'video communication Error';
  static communicationErrorDes =
    '{displayName}({userId}) has failed to create a vidyo connector object for video chat. Reason -{error}';
  static videoConnectionSuccess = 'Vidyo Connection Success';
  static videoConnectionSuccessDes = '{displayName} ({userId}) successfully connected to the chatroom - {roomId}';
  static videoConnectionFail = 'Vidyo Connection Failed';
  static videoConnectionFailDes = '{displayName} ({userId}) failed to join in chatroom -{roomId} . Reason - {reason}';
  static remoteMemberJoin = 'Remote Member Join';
  static remoteMemberJoinDes = 'Remote member {participantName} joined in {displayName} ({userId} chatroom - {roomId}';
  static remoteMemberLeft = 'Remote Member Left';
  static remoteMemberLeftDes = 'Remote member {participantName} left from {displayName} ({userId}) chatroom - {roomId}';
  static remoteMemberListner = 'Remote Member Listener';
  static remoteMemberListnerDes = '{displayName}({userId}) created a member listener for video chat';
  static remoteMemberListnerFailed = 'Remote Member Listener Failed';
  static remoteMemberListnerFailedDes =
    '{displayName} ({userId}) failed to create a member listener for video chat. Reason - {error}';
  static remoteCameraListner = 'Remote Camera Listener';
  static remoteCameraListnerDes = '{displayName} ({userId}) created a remote camera listener for video chat';
  static remoteCameraListnerFailed = 'Remote Camera Listener Failed';
  static remoteCameraListnerFailedDes =
    '{displayName} ({userId}) failed to create a local camera listener for video chat. Reason - {error}';
  static remoteCameraRemoved = 'Remote Camera Removed';
  static remoteCameraRemovedDes =
    'Remote member {participantName} having participant id {participantId} removed the camera - {remoteCameraName} in {displayName}({userId}s chatroom - {roomId}';
  static remoteCameraRemovelFailed = 'Remote Camera Removal Failed';
  static remoteCamRemoveFailDes =
    'Remote member {participantName} having participant id {participantId} failed to remove the camera - {remoteCameraName} in {displayName} ({userId}s chatroom -{roomId}. Reason - {error}';
  static remoteMicroPhoneAdd = 'Remote Microphone Added';
  static remoteMicroPhoneAddDes =
    'Remote user {participantName} with participant id {participantId} added a microphone - {remoteMicrophone} in {displayName} ({userId}) s chatroom - {roomId}';
  static remoteMicroPhoneRemove = 'Remote Microphone Removed';
  static remoteMicroPhoneRemoveDes =
    'Remote user {participantName} with participant id {participantId} removed a microphone -{remoteMicrophone} in {displayName} ({userId}) s chatroom - {roomId}';
  static remoteMicroPhoneListner = 'Remote Microphone Listener';
  static remoteMicroPhoneListnerDes = '{displayName} ({userId}) created a remote microphone listener for video chat';
  static remoteMicroPhoneFailed = 'Remote Microphone Listener Failed';
  static remoteMicroPhoneFailedDes =
    '{displayName} ({userId}) failed to create a remote microphone listener for video chat. Reason - {reason}';
  static videoChatEnd = 'Ending Video Chat';
  static videoChatEndDes = '{displayName} ({userId}) successfully disconnected from the chatroom - {roomId}';
  static localCameraListner = 'Local Camera Listener';
  static localCameraRemoved = 'Local Camera Removed';
  static localCameraRemovedDes = '{displayName}({userId}) removed camera - {localCameraName} in chatroom - {roomId}';
  static localCameraRenderFailed = 'Local Camera Rendering Failed';
  static localRenderedDes =
    '{displayName}({userId}) s video stream rendering failed for video chat in chatroom - {roomId}. Reason - {e}';
  static localCameraListnerFail = 'Local Camera Listener Failed';
  static localMicroPhoneRemoved = 'Local Microphone Removed';
  static localMicroPhoneSelect = 'Local Microphone Selected';
  static localMicroPhoneListner = 'Local Microphone Listener';
  static toggleMute = 'Toggle Microphone Mute';
  static toggleMuteDes = '{displayName} ({userId}) {comment} the microphone in chatroom - {roomId}';
  static toggleVideoMute = 'Toggle Video Mute';
  static toggleVideoMuteDes = '{displayName} ({userId}) {comment} the video stream in chatroom - {roomId}';
  static videoCommunication = 'video communication';
  static videoAccept = 'Video Call Accept';
  static videoAcceptDes = '{userName}({userId}) accepted the video call from chatroom- {roomId}';
  static videoReject = 'Video Call Reject';
  static videoRejectDes = '{displayName}({userId}) rejected the video call from chatroom- {roomId}';
  static joiningVideoChat = 'Joining Video Chat';
  static joiningVideoChatDes = '{displayName} ({userId}) {type} video chat in chatroom - {roomId}';
  static initiateVideoChat = 'Initiating Video Chat';
  static endvideoChat = 'Ending Video Chat';
  static endVideoChatDes = '{initiatorName} ({initiator}) (initiator) disconnected video call from chat room {roomId}';
  static disconnectDes = '{displayName}({userId}) clicked on disconnect button in chatroom -{roomId}';
  static initiatorDisconnectDes = 'Initiator ended the video call';
  static swapTiles = 'Tile Swapping';
  static swaptileDes =
    '{displayName} ({userId}) clicked on remote video tile for switching to fullscreen in chatroom - {roomId}';
  static assignToFullscreen = 'Assigning Video stream of {participantName} to fullscreen success';
  static assignToTile = 'Assigning Video stream of {participantName} to tile of id ({rendererId}) success';
  static swapTileFailed = 'Assigning Video stream of {participantName} to {valueIndex} failed. Reason - {err}';
  static exceptionHandler = 'exception handler';
  static apiException = 'API Exception';
  static wuiException = 'WUI Exception';
  static gqlException = 'GQL Exception';
  static gql = 'graphql';
  static exceptionDescription =
    '[CitusHealth Error] - Request URL - {url}. Data - {data}. Error Message - {message}. Status - {status}';
  static authException = 'Failure User Login';
  static authExceptionDescription = 'User authentication failed ({userName}). Message - {message}';
  static forgotPassword = 'Forgot Password';
  static forgotPasswordDes = '{userEmail} requested forgot password';
  static forgotPasswordException = 'Failure Forgot Password';
  static forgotPasswordExceptionDes = 'Forgot password request failed for {userEmail}. The email does not exists';
  static feedback = 'support and feedback';
  static feedbackDescription =
    '{displayName} submitted feedback support {subject} Preferred Contact Mode: {contactMethod} {contactMobile} Description : {feedbackDesc} {attachmentTxt}';
  static manageUser = 'manage user';
  static updateUserProfile = 'Update User Profile';
  static updateUserProfileFailed = 'Failure Update User Profile';
  static updateUserProfileDescription = '{displayName} updated profile details';
  static updateUserProfileFailedDes = '{displayName} update profile details failed';
  static deleteProfilePhoto = 'Delete User Profile Photo';
  static deleteProfilePhotoDescription = '{displayName} deleted profile photo';
  static updateProfilePhoto = 'Update User Profile Photo';
  static updateProfilePhotoDes = '{userName} updated profile photo';
  static updateProfilePhotoFailed = 'Failure Update User Profile Photo';
  static updateProfilePhotoFailedDes = `{userName} couldn't update profile image due to invalid file extension {fileExtension}`;
  static messageBroadcast = 'Message Broadcast';
  static communication = 'communication';
  static maskedMessage = 'Masked Message';
  static messageBroadcastDes = 'Broadcast message sent by- {displayName} to {recipients}';
  static maskedMessageDes = 'Masked message with chatroom id {chatroomId} sent by - {displayName} to {recipientList}';
  static doubleVerification = 'Double Verification';
  static doubleVerificationDes =
    '{displayName} ({userId}) has choosed {doubleVerificationStatusChoosed} in chatroom - {roomId}';
  static viewDocument = 'Document View';
  static signatureRequest = 'Signature Request';
  static readonly signatureCancel = 'Signature - CANCEL';
  static readonly signatureCancelDes = '{displayName} (User-{userId}) CANCEL {document} Document ( Status : {status})';
  static signatureSave = 'Signature save';
  static signatureSaveDes = '{displayName} signed palette of  {documentName} Document in page {pageNo}.';
  static finishSigning = 'Finish signing';
  static finishSigningDes = 'Patient click Finish for {documentName}';
  static viewDocumentDes =
    '{displayName} open {documentName} Document ( Status : {signatureStatus} Document type : {type})';
  static openDocument = 'Document open';
  static openDocumentDes = '{displayName} viewed {displayText} (Document status : {signatureStatus})';
  static tagSelect = 'Tag Select';
  static tagSelectDescription = '{displayName} select {docName}';
  static fileCooser = 'File Chooser';
  static fileChooserDes = '{displayName} select Browse option for file upload';
  static forms = 'forms';
  static notifications = 'Notifications';
  static notificationsGranted = 'PushNotifications Permission Granted';
  static pushNotificationEvents = 'PushNotifications events registered successfully';
  static pushNotificationEventsDes = '{appName} PushNotifications app events registered successfully from {device} device and Token : {token} .uniqueDeviceId: {uniqueDeviceId}';
  static userPushDeviceRegistration = 'User Push device registration completed successfully';
  static userPushDeviceRegistrationDes = '{appName} app userPushDeviceRegistration completed successfully, registrationData : {userPushRegistrationData}';
  static pushNotificationPermission = 'PushNotifications Permission Granted';
  static pushNotificationPermissionDes = '{appName} app PushNotifications Permission Granted successfully.';
  static searchForms = 'Search Forms';
  static searchFormDes = `{displayName} has searched {formType} forms with keyword '{keyWord}'`;
  static selectForm = 'Select Form';
  static selectFormDes = '{displayName} has selected {formName} ({formId})';
  static readonly selectDocument = 'Select Document';
  static readonly selectDocumentDes = '{displayName} has selected {documentName} ({documentId}) from {fromName}';
  static patientSearch = 'Patient Search';
  static patientSearchDes = `{displayName} has searched for patient with keyword '{searchKey}' for the form '{formName}' ({id})`;
  static selectAssociatedPatient = 'Select Associated Patient';
  static selectAssociatedPatientDes =
    '{displayName} has selected Associated Patient {recipientName} ({recipientId}) : Form Name - {formName}';
  static formAlreadyPending = 'Selected form already exists in pending and navigated to pending';
  static formAlreadyPendingDes =
    '{displayName} - Form {formName} ({formId}) already exists in pending and navigated to pending.';
  static patientFormAlreadyPending = 'Patient already associated with the selected form and navigated to pending';
  static patientFormAlreadyPendingDes =
    '{displayName} - {recipientName} - this patient already has a form in pending - {formName} (formId) and navigated to pending.';
  static formAlreadyPendingContinue = 'Selected form already exists in pending and continue with the form';
  static formAlreadyPendingContinueDes =
    '{displayName} - Form {formName} ({formId}) already exists in pending and continue with the form.';
  static patientFormAlreadyPendingContinue =
    'Patient already associated with the selected form, exists in pending and continue with the new form';
  static patientFormAlreadyPendingContinueDes =
    '{displayName} - {recipientName} - this patient already has a form in pending {formName} ({formId}) and continue with the new form.';
  static patientFormAlreadyDraft = 'Patient already has drafts for the selected form and navigated to draft bucket';
  static patientFormAlreadyDraftDes =
    '{displayName} - {recipientName} - this patient already has drafts for the form {formName} ({formId}) and navigated to draft bucket';
  static patientFormAlreadyDraftContinue =
    'Patient already has drafts for the selected form and continued with the form';
  static patientFormAlreadyDraftContinueDes =
    '{displayName} - {recipientName} - this patient already has drafts for the form and continued with the form {formName} ({formId})';
  static sendApplessForm = 'Send Appless Form';
  static sendApplessFormDes = '{displayName} has sent {formName} ({formId}) to users {recipientId} (appless)';
  static virtualPatientRegistration = 'On the Fly Patient Registration';
  static userCreation = 'user creation';
  static virtualPatientRegDes =
    'New on the fly user registered ({firstName} {lastName} {dob}) by {displayName} ({patientId})';
  static userEnrollment = 'user enrollment';
  static partialSubmission = 'Partial Submission';
  static fullSubmission = 'Full Submission';
  static userRegistrationByInvite = 'User Registration By Invite';
  static readonly structuredForms = 'structured forms';
  static readonly cancelPendingForm = 'Cancel Pending Form my form work list';
  static readonly cancelPendingFormDes =
    '{displayName} canceled pending form {formName} (formId {formId},sentId {sentId},recipientId {recipientId}) successfully';
  static readonly cancelPendingFormFailDes =
    '{displayName} canceled pending form {formName} (formId {formId},sentId {sentId},recipientId {recipientId}) failed and the api response is {message}';
  static userRegistrationByInviteDes =
    '{displayName} has referred {email} ({enrollUserId}) using referral token - {token}, User type: {userType}';
  static referUser = 'Refer {user}';
  static referUserDes = '{displayName} has referred {email} using  referral token - {token}';
  static failureReferUser = 'Failure Refer {user}';
  static failureReferDes = `{displayName} couldn't refer {email} using referral token - {token} due to {statusMessage}`;
  static manageUserAccess = 'manage user access';
  static deleteUser = 'Delete User';
  static deleteUserDes = 'Deleted user ({displayName})';
  static deleteUserFailure = 'Failure Delete User';
  static deleteUserFailureDes = 'User deletion failed ({displayName})';
  static updatePatient = 'Update Patient via User Center';
  static updatePatientDes = 'Updated patient {email} via User Center';
  static failureUpdatePatient = 'Failure Update Patient via User Center';
  static failureUpdatePatientDes = 'Update patient {email} via User Center failed';
  static addUserSuccess = 'Add User via User Center';
  static addUserSuccessDes = 'Successfully added {user} {email} via User Center';
  static addExistingUser = 'Add Existing User via User Center';
  static addExistingUserDes = 'Added User via User Center already in the CitusHealth platform';
  static addUserFailure = 'Failure Add User via User Center';
  static addUserFailureDes = 'Add {user} {email} via User Center failed';
  static userRegistrationFailure = 'Failure User Registration via User Center';
  static userRegistrationFailureDes = 'User registration failed via User Center. {errorMessage}. Details: {details}';
  static userRegistration = 'User Registration via User Center';
  static userRegistrationDes = 'New user registered {userEmail} via User Center';
  static userReactivation = 'User Reactivation via User Center';
  static userReactivationDes = 'Reactivated the existing user {userEmail} via User Center';
  static createReferralTokenFailure = 'Failure Referral Token Create - Signup via User Center';
  static manageUserEnrollment = 'manage user enrollment';
  static failureFirstLoginUpdateReferral = 'Failure First Login Update - Referral';
  static failureFirstLoginUpdateReferralDes =
    'Failed to update first login details of user {username}. Full response is : {fullResponse}';
  static failureFirstLoginBeforeWPCallDes =
    'Failed to call first login because of user {username} having status:{status} and registration type: {registrationType}';
  static createReferralTokenFailedDes =
    'Referral token creation for user {displayName} ({userId}) via User Center. Failed due to {message}';
  static createReferralToken = 'Referral Token Create - Signup via User Center';
  static createReferralTokenDes = 'Referral token created for user {displayName} ({userId}) vis User Center';
  static patientSearchDesUserCenter = `{displayName} has searched for patient with keyword '{keyWord}'`;
  static selectPatient = 'Select Patient';
  static selectPatientDes = '{displayName} has selected patient {patientName} ({id})';
  static userCenter = 'user center';
  static searchTag = 'Search Tag';
  static searchTagDes = `{displayName} has searched for user tags with keyword '{keyWord}'`;
  static downloadDoc = 'Download Document';
  static downloadDocDes = '{displayName} has downloaded document {documentName} ({id})';
  static removeTag = 'Remove Tag';
  static removeTagDes = '{displayName} removed tag {tagName} ({id})';
  static userTagSelect = 'User Tags';
  static userTagSelectDes = '{displayName} selected tags {tags}';
  static feedbackAttachmentDes = '{displayName} selected an attachment';
  static removeAttachment = 'Remove Attachment';
  static removeAttachmentDes = '{displayName} has removed the attachment';
  static searchDocuments = 'Search Documents';
  static searchDocumentDes = `{displayName} has searched {documentType}  with keyword '{keyWord}' and '{selectedDate}' as selected date`;
  static searchPhysicianOrder = 'Search Physician Orders';
  static searchPhysicianOrderDes = `{displayName} has searched Physician Order with keyword '{keyWord}'`;
  static searchRecipients = 'Search Recipients';
  static searchRecipientsDes = `{displayName} has searched for recipients with keyword '{keyWord}'`;
  static educationMaterial = 'education material';
  static searchEducationMaterial = 'Search Education Material';
  static searchEducationMaterialDes = `{displayName} searched for education materials  with keyword '{keyWord}'`;
  static viewEducationMaterial = 'View Education Material';
  static viewEducationMaterialDes = `{displayName} viewed the education material - '{materialName}' shared by {fromName}`;
  static downloadEduMaterial = 'Download Education Material';
  static downloadEduMaterialDes = '{displayName} has downloaded education material {materialName} ({materialId})';
  static fileUpload = 'File Upload';
  static fileUploadDes = 'Successfully uploaded file';
  static messaging = 'messaging';
  static searchMessage = 'Search Message';
  static searchMessageDes = `{displayName} searched for message with keyword '{keyword}'`;
  static startChatSession = 'Start Chat Session';
  static startChatSessionDes = 'Chat With - {activityContent} in Chatroom {chatroomId} ( chat thread details: {details} )';
  static startChatSessionFailDes = 'Chat With - {activityContent} failed (Details: {details})';
  static endChatSession = 'End Chat Session';
  static endChatSessionDes = 'Exited from Chatroom ID - {chatroomId}';
  static reRouteChatSession = 'Reroute Chat Session';
  static reRouteChatSessionDes =
    '{fromUser}({fromUserId}) has forwarded this chat session to {forwardUser}({forwardUserId}) in chatroom ({chatroomId})';
  static reRouteChatSessionFailedDes =
    'Forwarding this chat session to {forwardUser} in chatroom {chatroomId} failed - Member already exists in the chatroom';
  static searchUser = 'Search User';
  static searchUserDes = `{displayName} has searched for user with keyword '{keyword}' to forward the chat session`;
  static removeFromChatSession = 'Remove from Chat Session';
  static removeFromChatSessionDes = '{removedBy} removed {removedUser}({id}) from  chatroom {chatroomId}';
  static inviteToChatSession = 'Invite to Chat Session';
  static inviteUsersToChatSessionDes =
    '{invitedBy} successfully invited user(s): {invited} to Chatroom ID - {chatroomId}. Details - {message}';
  static inviteRolesToChatSessionDes =
    '{invitedBy} successfully invited role(s): {invited} to Chatroom ID - {chatroomId}. Details - {message}';
  static inviteToChatSessionFailedDes = 'Invite users to Chatroom ID - {chatroomId} failed. Details - {message}';
  static inviteSearchUserDes = `{displayName} has searched for user with keyword '{keyword}' to invite to the chat session`;
  static signMessage = 'Sign Message';
  static signMessageDes = '{displayName} signed the message ({messageId}) in chat session {chatroomId}';
  static removeSignMessage = 'Remove Sign Message';
  static removeSignMessageDes = '{displayName} removed sign from message ({messageId}) in chat session {chatroomId}';
  static removeTagMessage = 'Remove Tag Message';
  static removeTagMessageFromMessageDes = '{displayName} removed tag from message ({messageId}) in chat session {chatroomId}';
  static removeTagMessageFromThreadDes = '{displayName} removed tag from chat session {chatroomId}';
  static searchUserChatDes = `{displayName} has searched for user with keyword '{keyword}' to chat`;
  static viewFormReport = 'View Form Report';
  static viewFormReportDes = '{viewedUser} has viewed {formName} ({formId}) from inbox filled by {filledBy} ({id})';
  static setEnrollPassword = 'Set Enroll Password';
  static setEnrollPasswordDes = '{userName}({userId}) successfully set the password';
  static resetPassword = 'Reset Password';
  static resetPasswordDes = '{userName}({userId}) successfully updated the password';
  static documentUpload = 'Document Uploaded';
  static documentUploadDes = `{displayName} uploaded a document with name {actualFileName}( Status :{status},
                                sendDocumentWithoutSignature : {sendDocumentWithoutSignature},
                                ObtainSignature : {ObtainSignature}, Personal Signatuire : {personalSignature})`;
  static sendDocument = 'Sent Document';
  static sendDocumentDes = `{displayName} Sent Document ({actualFileName}) to recepient ({recipient} ({recipientId}))
                              (Status: PENDING,Obtainsignature:{obtainSignature})`;
  static documentSendError = 'Document Send Error';
  static documentSendErrorDes = `{displayName} Sent Document ({actualFileName}) to recepient ({recipient} ({recipientId}))
                              (Error : graphql,Status: PENDING,Obtainsignature:{obtainSignature})`;
  static removePalette = 'Remove Field';
  static removePaletteDes = `{displayName} {activityDescriptionfields}  From Document ( {actualFileName} - {documentUniqueName})`;
  static cancelSignatureProcess = 'Cancelled Process';
  static cancelSignatureProcessDes = `{displayName} cancelled signature request Process ({actualFileName} - {documentUniqueName})`;
  static formSavedAsDraft = 'Form Saved As Draft';
  static formSaveAsDraftOnSessionExpiry = 'Form Save As Draft on Session Expiry';
  static formSaveAsDraftOnSessionExpiryDes =
    '{displayName} attempting to save form progress as draft on session expiry {retry}';
  static formSavedAsDraftDes = `{displayName} saved form - {formName} as draft. Associated Patient - {recipientName} ({recipientId}). Form id - {formId}`;
  static submitForm = 'Submit Form';
  static submitFormDes =
    '{displayName} has completed staff facing form - {formName} ({formId}). Associated Patient - {recipientName} ({recipientId}). submissionId - {submissionId}';
  static viewForm = 'View Form';
  static viewDraftForm = 'Form view from Draft';
  static viewDraftFormDes = `{displayName} viewed form {formName}({formId}). Associated Patient - {recipientName} ({recipientId}).`;
  static viewFormDes = `{displayName} has viewed {formName}({formId}) sent by {recipientName} ({recipientId}) from inbox to fill the fields.`;
  static viewFormDesAppless = `{displayName} has viewed {formName}({formId}) sent by {recipientName} ({recipientId}) using appless link to fill the fields.`;
  static sendForm = 'Send Form';
  static sendFormDes = `{displayName} has sent {formName} ({formId}) to user ({recipientId})`;
  static sendFormMultipleUsersDes = `{displayName} has sent {formName} ({formId}) to users {recipientId}`;
  static helpCenterDes = `{firstName} {secondName} with username {userName} opened Help center from desktop`;
  static openHelpCenter = 'Open Help Center';
  static helpCenter = 'Help Center';
  static addField = 'Add Field';
  static addFieldDes = `{displayName} {activityDescriptionfields} ( {actualFileName} - {documentUniqueName})`;
  static setField = 'Field set';
  static setFieldDes = `{displayName} clicked {selectedUserName} tab for set fields for document {actualFileName}`;
  static selectRecipient = 'Select Recipient';
  static selectRecipientDes = `{displayName} has selected recipient ({recipientId}) ({recipientName}): Form Name - {formName} ({formId})`;
  static copiedtoFilingCenter = 'Copied to Filing Center After the Document upload';
  static submitDocumentDes = `{displayName} uploaded a document with name {documentName} ( Status :{status},sendDocumentWithoutSignature : {sendDocumentWithoutSignature}, Obtain Signature : {obtainSignature}, Personal Signature : {personalSignature})`;
  static signedSignatureRequest = 'Signed Signature Request';
  static signedSignatureReqDes = `{displayName} sent a document with direct submit option and document name is {documentName} ( Status :Direct)`;
  static addUser = 'add user';
  static addUserFromInviteSuccess = 'Added User via invite user';
  static addUserFromInviteSuccessDes = 'Added User ({userEmail}) via invite user';
  static addUserFromInviteFailed = 'Added User failed via invite user';
  static addUserFromInviteFailedDes = 'Added User failed ({userEmail}) via invite user';
  static copiedSignedSignatureToFilingCenter = 'Copied Signed Signature Request To Filing Center';
  static copiedSignedSignatureToFilingCenterError = 'Blocked Copy to Filing Center After the Document upload';
  static copiedSignedSignatureToFilingCenterDes = '{displayName} sent signed document. Moved to filing center.';
  static copiedSignedSignatureToFilingCenterErrorDes = 'Blocker copyToFc after complete because of {reason} and user is {displayName} and document_id is {documentId}.';
  static sentSignedDocDes = '{displayName} sent signed document';
  static signatureRequestFailure = 'Failure Signature Request';
  static signatureRequestFailureDes = 'Document signing failed when {displayName} tried to sign the document.';
  static draftCreated = 'Visit Schedule Draft Created';
  static visitScheduleActivity = 'Visit Schedule Activity';
  static createdDraftVisit = 'created draft visit';
  static forThePatient = 'for the patient';
  static metaData = 'metadata';
  static scheduledTheVisit = 'scheduled the visit';
  static visitScheduleCreated = 'Visit Schedule Created';
  static visitScheduled = 'Visit Scheduled';
  static visitSchedule = 'Visit Schedule';
  static visitScheduleDeleted = 'Visit Schedule Deleted';
  static visitDetails = 'visit details';
  static schedulerDeletedVisit = '(scheduler) deleted the visit';
  static addAlternateContactSuccess = 'Added alternate contact via User Center';
  static viewDeliveryDetailsDes = 'Delivery Details for Ticket number {ticketNumber} and unique id is {uId}';
  static viewDeliveryDetails = 'view Delivery Details';
  static deliveryDetailsPageView = 'Delivery Details Page View';
  static delivery = 'search Keyword';
  static searchDelivery = 'Search Keyword value';
  static searchDeliveryDes = '{searchText} Searched Keyword';
  static courierTrackingUrl = 'Courier Tracking Url';
  static courierUrlForTicket = 'courier url for Ticket';
  static courierTrackingDes = 'courier tracking Url courier tracking  Url {courierTrackingURL}';
  static signDelivery = 'Sign the Delivery';
  static deliverySign = 'Delivery Sign';
  static signDeliveryDes = 'Redirect to sign the delivery url {signatureURL}';
  static viewPendingDelivery = 'View Pending Deliveries';
  static pendingDelivery = 'Pending Deliveries';
  static viewPendingDeliveryDes = 'Viewed Pending deliveries for the delivery center';
  static viewCompletedDelivery = 'View Completed Deliveries';
  static completedgDelivery = 'Completed Deliveries';
  static viewCompletedDeliveryDes = 'Viewed Completed deliveries for the delivery center';
  static changeStatus = 'Change status';
  static alternateContactStatusChange = 'Alternate Contact {action} via User Center';
  static alternateContactstatusChangeDes = 'Alternate Contact {action} ({email}) via User Center';
  static deleteAlternateContact = 'Deleted Alternate Contact  via User Center';
  static alternateContactsDeleteDes = '{action} Alternate Contact ({email}) via User Center';
  static deleteAlternateContactType = 'Delete user';
  static applessFailedSubmit = '(Appless) Document signing failed when {displayName} tried to sign the document ';
  static applessCanceldDocumentSubmit =
    '(Appless) Document signing failed due to sign canceled Document when {displayName} tried to sign ';
  static applessFlow = ' (Appless)';
  static appless = 'Appless';
  static applessPage = 'Appless Page';
  static applessPageDesc = 'User accessed appless page for {flow} with token {token}';
  static applessPageDescId = 'User accessed appless page for {flow} with guid {notificationId}';
  static applessTokenVerified = 'Appless token verify';
  static applessTokenVerifiedDes = 'Appless token ({token}) verified for user ({username}) for flow: {flow}';
  static applessTokenAutoVerifiedDes = 'Appless token ({token}) auto verified for user ({username}) for flow: {flow}';
  static applesstokenSendCode = 'Send code for verification';
  static applessInvalidCode = 'Invalid Details in Appless';
  static applessInvalidCodeDes =
    'User entered incorrect Details ({request}) for appless flow :{flow} with token :{token}';
  static applessInvalidSourceId = 'Invalid source Id';
  static applessInvalidSourceIdDes = 'source Id ({sourceId}) is not valid for appless download with token {token}';
  static applessValidCodeDes =
    'User entered correct Details ({request}) and session validated for appless flow :{flow} with token :{token}';
  static mobile = 'mobile';
  static email = 'email';
  static applesstokenSendCodeDes =
  'Appless token ({token}) send code by {sendThrough} for verification for user ({username}) for flow: {flow} ({sendMedium})';
  static applesstokenSendCodeFail = 'Sending code for verification failed';
  static applesstokenSendCodeFailDes =
    'Appless token ({token}) failed to send code by {sendThrough} for verification for user ({username}) for flow: {flow} ({sendMedium})';
  static reSign = 'Re-Sign';
  static reSignDes = '{displayName} (representative ) click resign button for {actualFileName} Document ';
  static confirmSubmit = 'Confirm Submit';
  static representiveConfirm = '{displayName} (representative ) click confirm submit for {actualFileName} Document ';
  static consolo = 'consolo window';
  static consoloType = 'WindowClose';
  static consoloDesc = 'window automatically close function failed';
  static formSubmissionDes =
    '{displayName} has {action} staff facing form {formName} ({formId}) Associated Patient- submissionId {submissionId}';
  static updateApp = 'App Update Button Clicked';
  static appUpdate = 'app update';
  static appUpdateDes = '{displayName} has clicked app Update Now button ({url})';
  static resendFailedMessages = 'Resend Failed';
  static resendFailedMessagesDes = 'Failed messages resending process started';
  static mainActivityDescription =
    '{description} . Client Time: {date} . bundleIdentifier: {bundle} . Remote notification is {status} . Location is {location}.';
  static fetchSiteRegistrationId = 'Fetch site registration id using site id';
  static siteIdNotFound = 'SiteId not found';
  static updateMessageGroupSubject = 'Update Message Group Subject';
  static updateMessageGroupSubjectType = 'manage message group';
  static updateMessageGroupSubjectDesc = '{displayName} has updated the message group subject to {subject}';
  static failedUpdateMessageGroupSubjectDesc = '{displayName} has has failed to update the message group subject to {subject}';
  static patientAddress = 'Patient address';
  static patientAddressUpdateDesc = "{patientName}'s Patient address {action} by {userName}";
  static patientAddressDeleteDesc = "{patientName}'s {category} Patient address {action} by {userName}";
  static linkBrokenDes = 'The user is not able to proceed due to the absence of token in the link. The app-less flow is: {applessFlow}';
  static authGuardSessionErrorDes = 'The user is not able to proceed due to an invalid or expired session, restricted by AuthGuard';
  static authGuardSessionApplessDes = 'Page redirection was restricted by AuthGuard, displaying the app-less error info page';
  static applessRedirectPathDes = 'User redirected to {applessPath} from appless link : {applessLink}';
  static getSessionDataErrorRedirectDes = 'The user was redirected to the login page due to a failure in fetching user session data from the token';
  static redirectToLoginNoSessionDateDes = 'The user was redirected to the login page due to due to no session data';
  static redirectToLoginDes = 'User redirected to the login page due to the absence of an appLessPath and token';
  static redirectToLoginNoSessionOrOfflineDes =
    'User redirected to the login page due to missing user data or being offline with offline forms enabled.';
  static redirectToLoginOfflineModeDes = 'User redirected to the login page due to seems the device is in offline mode';
  static offlineModeRedirectDes = 'User reloaded the page after coming back online';
  static readonly applessNavigationInitiated =
    'User is about to navigate to {appLessWorkFlow} page with sessionAuthToken: {sessionAuthToken}, userDataAuthToken: {userDataAuthToken} and token: {token}.';
  static applessLinkAccess = 'AppLess Link Access';
  static applessLinkAccessDes = 'AppLess Link accessed by {patientName} by clicking link sent to {userEmail} at {accessTime}';
}
