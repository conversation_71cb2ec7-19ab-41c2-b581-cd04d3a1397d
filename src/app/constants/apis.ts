export class APIs {
  static loginApi = 'chat-window-authenticate-forgerock.php';
  static messageFormCount = 'get-user-inbox-counts.php';
  static fetchMessages = 'message-inbox-list.php';
  static getMaskedReplyMessages = 'get-masked-reply-messages.php';
  static updatePinStatus = 'handle_message_pin.php';
  static forgotPasswordApi = 'forgot-password-icampp.php';
  static getUsersByTenantRole = 'get-tenant-users-by-roleid.php';
  static getFormsListing = 'get-sent-structured-forms-paginated.php';
  static getMyFormWorklist = 'get-my-form-worklist.php';
  static getAllTaggedForms = 'get-all-tagged-forms-limit-lazy.php';
  static getChatMessages = 'socket-message.php';
  static getNewChatMessagePolling = 'get-new-chat-message-thread-on-polling.php';
  static getRoomUsers = 'get-room-users.php';
  static getChatRoomStaffCount = 'get-accessible-users-count-in-chatroom.php';
  static createChatRoom = 'create-chatroom.php';
  static getEduMaterials = 'get-user-education-materials-paginated.php';
  static getForms = 'get-all-survey-names.php';
  static getActiveTags = 'get-active-user-tags.php';
  static getActiveUserRoles = 'get-tenant-roles-for-active-users.php';
  static getAlltags = 'get-all-tags.php';
  static getMaskedRecipients = 'get-masked-message-recipients.php';
  static userProfileUpdateApi = 'update-userdetails-icampp.php';
  static broadcastMessage = 'send-broadcast-message.php';
  static maskedMessage = 'create-chatroom-masked-message.php';
  static getMessageGroups = 'getMessageGroups.php';
  static getPatientGroups = 'getPatientGroups.php';
  static getUserRolesWithTenants = 'get-roles-of-tenants-with-active-users.php';
  static getTenantRolesByPrivilege = 'get-tenant-roles-by-privilege.php';
  static inviteUserToChatRoom = 'invite-user-to-chatroom.php';
  static rerouteMessage = 'reroute-message.php';
  static removeRoleParticipantFromChatRoom = 'remove-chatroom-role-participant.php';
  static manageChatParticipants = 'manageChatParticipants.php';
  static chatSignatureAction = 'chat-signature-upload.php';
  static getTagDetails = 'get-form-tag-details.php';
  static getAssociatedPatients = 'get-tenant-associated-patient.php';
  static getPatientAssocForms = 'get-patient-associated-forms.php';
  static getIntegrationStatus = 'check-integration-status.php';
  static sendForm = 'send-form.php';
  static cancelForm = 'cancel-form.php';
  static archiveInboxItems = 'archive-inbox-items.php';
  static deleteSentForm = 'delete-sent-form.php';
  static restoreSentForm = 'restore-sent-form.php';
  static deleteDraftForm = 'delete-draft-form.php';
  static archiveMessages = 'archive-messages.php';
  static getMessagGroupTopics = 'getGroupTopics.php';
  static updateChatSubject = 'update-chat-subject.php';
  static manageIdmToken = 'manage-idm-token.php';
  static flagMessage = 'update-chat-thread-or-msg-flag.php';
  static restoreMessages = 'restore-messages.php';
  static removeChatRoomParticipants = 'remove-chatroom-participants.php';
  static getAlertMessage = 'get-alert-message-data.php';
  static checkAllowEditForm = 'check-allow-edit-enabled-form.php';
  static checkDraftForm = 'check-draft-form.php';
  static deleteUser = 'delete-user.php';
  static saveVirtualPatient = 'save-virtual-patients.php';
  static trackActivity = 'track-activity.php';
  static configuration = 'citus-health-messages.php';
  static fileUpload = 'UploadHandler.php';
  static messageFileUpload = 'chatfile-upload.php';
  static checkStaffExist = 'check-staff-exists.php';
  static profileImageUpload = 'profile-upload.php';
  static deleteAvatar = 'delete-avatar.php';
  static getUserTags = 'get-tag-details.php';
  static checkUserExist = 'check-user-exists.php';
  // static saveVirtualUser = 'save-virtual-user.php';
  static saveVirtualUser = 'create-user.php';
  static readonly sendNotificationEmail = 'send-notification-email.php';
  static signupApi = 'chat-window-signup-forgerock.php';
  static embed = 'embed.php';
  static checkDocIntegrationStatus = 'check-doc-type-integration-status.php';
  static proofOfDeliveryMultipleUpload = 'proof-of-delivery-multiple_upload.php';
  static logout = 'chat-window-logout.php';
  static pushNotification = 'push-notification.php';
  static checkFormSubmited = 'check-structured-form-submitted.php';
  static structuredFormData = 'get-strucured-form-data.php';
  static resetPassword = 'set-rx-password.php';
  static maskedMessageReply = 'masked-message-reply-from-patient.php';
  static updateReadStatusMessage = 'update-marked-message-status.php';
  static referralCodesWrapper = 'referral-codes-wrapper.php';
  static inviteExistsWrapper = 'invite-exists-wrapper.php';
  static enrollWrapper = 'enroll-wrapper.php';
  static formsWrapper = 'forms-wrapper.php';
  static messageTranslate = 'messages-translate.php';
  static translationLogs = 'save-chatroom-translation-logs.php';
  static updateDeliveryMessage = 'get-undelivered-msg-and-update.php';
  static machformAuthentication = '/machform/Upload/machform_authentication.php';
  static resendFormToRecipients = 'resend-form-to-recipient.php';
  static startNewChat = 'start-new-chat.php';
  static chatWindowWrapperAuthenticateForgerok = 'chat-window-wrapper-authenticate-forgerok.php';
  static visitUserResourceData = 'get-visit-user-resource-data.php';
  static getAvailability = 'get-availability.php';
  static getVisit = 'get-visits.php';
  static getVisitById = 'get-visits-by-id.php';
  static updateVisitSchedule = 'update-visit-schedule.php';
  static getAppAuthURL = 'get-app-auth-url.php'; // New Added
  static deleteUpdatePatientAddress = 'manage-patient-address.php'; // New Added
  static getUserSessionDataByToken = 'get-user-session-data-by-token.php';
  static getVisitLocationType = 'get-visit-location-type-details.php';
  static deleteChatroomMessage = 'delete-chatroom-messages.php'; // New Added
  static archiveMaskedMessage = 'archive-masked-message.php'; // New Added
  static deleteSignatureDoc = 'delete-signature-doc.php'; // New Added
  static deleteInventory = 'delete-inventory-count.php'; // New Added
  static restoreChatroomMessage = 'restore-chatroom-messages.php'; // New Added
  static restoreMaskedMessage = 'restore-masked-message.php'; // New Added
  static getLocationTypes = 'get-visit-type-details.php';
  static locationNames = 'get-visit-location-details.php';
  static getPayorList = 'get-payor-details.php';
  static getAvailableUserResource = 'get-visit-available-user-resource-data.php';
  static checkPatientForCaregiverDocument = 'check-patient-for-caregiver.php';
  static getAdministrationDetails = 'get-route-administration-details.php';
  static getVisitTask = 'get-visit-tasks-details.php';
  static getTIimezones = 'get-timezones-with-dst.php';
  static getResource = 'get-visit-available-resource.php';
  static viewAvailability = 'view-availability-staff.php';
  static visitSchedule = 'visit-schedule.php';
  static fileAttachment = 'upload-visit-attachment.php';
  static visitScheduleActivity = 'visit-schedule-activity.php';
  static getVisitDetails = 'get-visit-details.php';
  static updateAppLessVideoStatus = 'update-appless-video-status.php';
  static readonly associationRequestValidation = 'association-request-validation.php';
  static getVisitTrackHistory = 'get-visit-track-history.php';
  static createAlternateContact = 'alternate-contact-save.php';
  static getDeliveryListing = 'get-delivery-datas.php';
  static getDeliveryDetails = 'get-delivery-details.php';
  static getAlternateContacts = 'alternate-contacts.php';
  static checkMessageTagIntegration = 'check-message-tag-integration-status.php';
  static verifyApplessDocumentToken = 'verify-appless-document-token.php';
  static verifyApplessFormToken = 'verify-appless-form-token.php';
  static verifyVideoToken = 'verify-appless-video-token.php';
  static verifyApplessVisitToken = 'verifyApplessVisitToken';
  static manageVisit = 'visits';
  static getFormTypeById = 'get-form-type-by-id.php';
  static applessChatMode = 'appless-chat-mode.php';
  static verifyApplessMessageToken = 'verify-appless-message-token.php';
  static availability = 'availability';
  static initiateFormReminder = 'initiateFormReminder.php';
  static initiateDocumentReminder = 'initiateDocumentReminder.php';
  static visitTypes = 'visitTypes';
  static userList = 'userList';
  static visits = 'visits';
  static attachment = 'attachment';
  static visitChat = 'visits/{{visitKey}}/chat';
  static checkAvailability = 'check-availability';
  static userSettings = 'userSettings';
  static addUserToMessageGroup = 'add-user-to-message-group.php';
  static appUsageWrapper = '/app-usage-wrapper.php';
  static getAlternateContactsCaregiverForPatient = 'get-alternate-contacts-caregiver-for-patient.php';
  static formDownload = 'form-download.php';
  static documentDownload = 'document-download.php';
  static tokenValidate = 'TokenValidate.php';
  static deleteMessageTag = 'delete-message-tag-from-chat-log.php';
  static generateStructuredFormDataPdf = 'generate-structured-form-data-pdf.php';
  static getDocumentDownloadUrl = 'get-document-download-url.php';

  static getHtmlForm = 'get-form-html.php';
  static readonly offlineForms = 'citus/includes/offline_form/{endpoint}';
  static syncOfflineForm = 'sync-offline-draft-form.php';
  static getOfflineDraftStatus = 'get-sync-offline-draft-status.php';

  static visitUpdateActualTime = 'visitUpdateActualTime';
  //* Java Endpoints Start
  static readonly userLookupEndpoint = 'coreservice/api/unauthorized/users/lookup';
  static readonly sendUsernameEndpoint = 'coreservice/api/unauthorized/users/notify';
  static readonly messageStatusEndpoint = 'coreservice/api/chatroom/message-status';
  static readonly sendOtpEndpoint = 'coreservice/api/unauthorized/users/send-otp';
  static readonly reSendOtpEndpoint = 'coreservice/api/unauthorized/users/resend-otp';
  static readonly otpEnabledEndpoint = 'coreservice/api/unauthorized/users/{username}/otp-2fa-enabled';
  static readonly oldToNewSeriesEndpoint = 'coreservice/api/visits/mapping/old-to-new-series';
  static readonly visitLocationLookup = 'coreservice/api/visits/visit-locations/search';
  static readonly visitChairLookup = 'coreservice/api/visits/visit-locations/{visitLocationId}/visit-chairs/search';
  static readonly getVisitLocation = 'coreservice/api/visits/visit-locations/{id}';
  static readonly getVisitChair = 'coreservice/api/visits/visit-chairs/{id}';
  static readonly therapyTypesEndpoint = 'coreservice/api/visits/therapy-types/search';
  static readonly therapiesEndpoint = 'coreservice/api/visits/therapies/search?therapyTypeId={therapyTypeId}';
  static readonly visitTherapyTypeByIdEndpoint = 'coreservice/api/visits/therapy-types/{id}';
  static readonly visitTherapyByIdEndpoint = 'coreservice/api/visits/therapies/{id}';
  static readonly deliveryStatusFetch = 'coreservice/api/chatroom-message-delivery/{messageId}/{deliveryStatus}';
  static readonly otpSendUrl = 'coreservice/api/unauthorized/patient/visit/create-otp';
  static readonly verifyOTPUrl = 'coreservice/api/unauthorized/patient/visit/verification-otp';
  static readonly resendOTPUrl = 'coreservice/api/unauthorized/patient/visit/resend-otp';
  static readonly createVisitUrl = 'coreservice/api/unauthorized/patient/visit/create-visit';
  static readonly getSlotsUrl = 'coreservice/api/unauthorized/patient/visit/get-slots';
  static readonly getAdmissionNamesList = 'coreservice/api/admissions/search/custom-fields';
  static readonly getAdmissionSitesEndPoint = 'coreservice/api/admissions/admission-sites';
  static readonly getAdmissionSearchList = 'coreservice/api/admissions/search';
  static readonly getAdmissionDetails = 'coreservice/api/admissions/';
  static readonly slotAvailabilityUrl = 'coreservice/api/unauthorized/patient/visit/slot-availability';
  static readonly unsubscribeNotifications = 'coreservice/api/unauthorized/users/unsubscribe-notifications';
  static readonly identityValidation = 'coreservice/api/unauthorized/users/identity-validation';
  static readonly identityValidationSendOtp = 'coreservice/api/unauthorized/users/identity-validation/send-otp';
  static readonly identityValidationReSendOtp = 'coreservice/api/unauthorized/users/identity-validation/resend-otp';
  static readonly configurations = 'coreservice/api/unauthorized/data/appLess-configurations/{notificationId}';
  static readonly getDownloadForms = 'coreservice/api/forms/downloadForms';
  static readonly getDownloadDocuments = 'coreservice/api/documents/downloadDocuments';
  static readonly addDocumentHistory = 'coreservice/api/documents/addHistory';
  static readonly addFormHistory = 'coreservice/api/forms/addHistory';

  //* Java Endpoints End

  // Webhook Middleware Endpoints Start
  static readonly notify = 'generic/v2/notify';
  static readonly prescribe = 'prescribe';
  //
  // Okta Endpoints start
  static redirectURLAndroid = 'http://localhost';
  static redirectURLIos = 'capacitor://localhost';
  // Okta Endpoints End
}
