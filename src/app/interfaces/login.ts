import { GeneralResponse } from 'src/app/interfaces/common-interface';

interface UserContactVerification {
  mobileVerified: number;
  emailVerified: number;
}
export interface LoginResponse {
  outOfOfficeInfo: any;
  enabledCrosssites?: any[];
  ssoId: any;
  isAlternateContact?: boolean;
  countryIsoCode?: string;
  siteid?: string;
  appLessSession?: boolean;
  isVirtual: any;
  accessSecurityType?: any;
  accessSecurityIdentifierType?: any;
  accessSecurityEsiValue?: any;
  accessSecurityEnabled: boolean;
  userName: string;
  first_login: boolean;
  idmMigrated?: boolean;
  code: number;
  date: string;
  dayNumber: string;
  userContactVerification: UserContactVerification;
  authenticationToken: string;
  userId: string;
  userUuid: string;
  userCmisId: string;
  tenantCmisId: string;
  displayName: string;
  firstName: string;
  secondName: string;
  avatar: string;
  profileImageUrl: string;
  profileImageThumbUrl: string;
  roleName: string;
  roleId: string;
  assignedRoles: string;
  tenantId: string;
  tenantType: string;
  organizationMasterId: string;
  crossTenantId: string;
  isMaster: string;
  group: string;
  userStatus: string;
  languages: string;
  supply_menu: string[];
  crossTenantsDetails: any[];
  privileges: string;
  privileges_replica: string;
  enable_auto_hide_web_notifications: string;
  enable_sms_notifications: string;
  enable_email_notifications: string;
  alternate_username: string;
  username: string;
  dob: string;
  mobile: string;
  address: string;
  city: string;
  state: string;
  country: string;
  zip: string;
  useSavedSign: boolean;
  savedUserSignature: string;
  caregivername: string;
  hasfamilycare: string;
  avatarUploadSize: number;
  primaryCare: string;
  cameraPictureLimit: number;
  allowedFileFormat?: any;
  caregiver_userid?: any;
  caregiver_username?: any;
  caregiver_displayname?: any;
  caregiver_firstname?: any;
  caregiver_lastname?: any;
  caregiver_zip?: any;
  caregiver_dob?: any;
  caregiver_mobile?: any;
  associated_user: any[];
  alternate_contact_patient: any[];
  gender?: any;
  countryCode: string;
  referral_code: string;
  patientInventory: boolean;
  staffInventory: boolean;
  tenantKey: string;
  tenantName: string;
  firstInfusionSupportNursesRoleName?: any;
  firstDefaultNursesRoleName: string;
  firstDefaultNursesRoleNameDuringWorkingHour?: any;
  firstBeyondWorkingNursesRoleName: string;
  firstInfusionSupportNursesRoleDuringWorkingHour?: any;
  nursing_agencies: string;
  config: Config;
  config_replica: Configreplica;
  defaultPage: string;
  defaultPageMobile: string;
  schedulerData: SchedulerData;
  escalatedSchedulerData: any[];
  avoidScheduleConversion: boolean;
  masterSchedulerData: any[];
  masterEscalatedSchedulerData: any[];
  avoidMasterScheduleConversion: boolean;
  updateAvailable: boolean;
  pushVarient: PushVarient;
  status: number;
  message: string;
  registration_type: string;
  masterEnabled: string;
  version: string;
  disabledPrivilegesIdsPatient: string[];
  cmisServiceId: number;
  cmisOperatedBy: string;
  cmisApiBaseUrl: string;
  cmisFileBaseUrl: string;
  cmisDefaultFolderList: CmisDefaultFolderList[];
  authToken: string;
  master_details: any;
  master_config: any;
  mySites: any;
  siteConfigs: SiteConfigResponse;
  mySiteinfo: string;
  notificationSoundName: string;
  alternate_contacts: any;
  userVisitDefaultView: any;
  userVisitDefaultTimezone: any;
  oooInfo: any;
}
export interface ApplessTokenResponse {
  activityLog: number;
  both: number;
  device: string;
  documentId: number;
  exp: number;
  iat: number;
  senderTenant: string;
  status: number | { code: number; message: string };
  tenantId: string;
  tokenExpire: boolean;
  userId: number;
  userData: LoginResponse;
  message: string;
  formDetails: any;
  sendId: number;
  chatRoomId: number;
}
interface ApplessErrorResponseData {
  code: string;
  message: string;
  param: string;
  tokenExpire: boolean;
}

export type ApplessErrorResponse = GeneralResponse<{ errors: ApplessErrorResponseData[] }>;
export interface ApplessTokenData {
  Email?: string;
  chatRoomId?: string;
  exp?: number;
  iat?: number;
  message?: string;
  notificationMode?: number;
  status?: number;
  tenantId?: number;
  tokenExpire?: boolean;
  userId?: number;
  visitKey?: string;
  start?: string;
  startTime?: string;
  end?: string;
  endTime?: string;
}
interface ApplessUserData extends LoginResponse {
  messages?: any;
  response?: LoginResponse;
}
interface ApplessMessageData {
  chatRoomId: string;
  status: number;
  tokenData: ApplessTokenData;
  tokenExpire: boolean;
  userSessionData?: { messages: any; response: LoginResponse };
  userData?: ApplessUserData;
}
export interface UserConsentPatientData {
  patientId: number;
  firstName: string;
  lastName: string;
  username: string;
}
export interface ApplessResponse extends ApplessTokenResponse {
  patientData?: UserConsentPatientData;
  sourceId?: string;
  entity?: string;
  isReminder?: number;
  data?: ApplessMessageData;
  completedFileName?: string;
  fileToken?: string;
  success?: boolean;
  responseStatus?: { code: number; message: string };
  errors?: ApplessErrorResponseData[];
}
interface CmisDefaultFolderList {
  folderName: string;
  defaultName: string;
  default: string;
  folderId?: any;
  update: boolean;
}

interface PushVarient {
  dev: Dev;
  prod: Dev;
}

interface Ios {
  variantID: string;
  variantSecret: string;
}

interface Android {
  senderID: string;
  variantID: string;
  variantSecret: string;
}
interface Dev {
  android: Android;
  ios: Ios;
}

interface SchedulerData {
  '2335': string;
  '2337': string;
  '11143': string;
}

interface Configreplica {
  token: string;
  escalation_time: string;
  message_refresh_interval: string;
  session_timeout: string;
  session_timeout_warning: string;
  primary_nurse_id: string;
  primary_pharmacist_id: string;
  patient_approver_role_id: string;
  clinician_approver_role_id: string;
  chat_auto_translate: string;
  ga_tracking: string;
  message_forwarding_behaviour: string;
  watchtower_tracking: string;
  esign_api_baseurl: string;
  esign_client_id: string;
  esign_api_key: string;
  clinician_roles_beyond_working_hours: string;
  clinician_roles_on_working_hours: string;
  show_infusion_support: string;
  show_prototypes: string;
  home_infusion_start_time: string;
  home_infusion_end_time: string;
  show_support_and_feedback_form: string;
  infusion_support_clinician_roles: string;
  default_clinician_roles_available: string;
  message_escalation_behavior: string;
  no_clinician_message: string;
  infusion_support_where_is_my_delivery: string;
  infusion_support_when_is_my_nurse_coming: string;
  working_hour: string;
  default_escalation_users: string;
  default_clinician_roles_available_on_working_hour: string;
  no_clinician_message_on_working_hours: string;
  infusion_support_my_nurse_coming_on_working_hours: string;
  infusion_support_my_delivery_on_working_hours: string;
  infusion_support_clinician_roles_on_working_hours: string;
  toggle_chat_translation: string;
  show_form_features: string;
  allow_virtual_patient: string;
  form_approver_clinician: string;
  show_patient_supply_inventory: string;
  clinical_liaison: string;
  show_user_tagging: string;
  enable_configurable_faq: string;
  show_document_tagging: string;
  show_chat_history_to_new_participant: string;
  patient_signup_with_admin_approval: string;
  clinician_signup_with_admin_approval: string;
  supply_request_recipient_type: string;
  immediate_assistance_message: string;
  enable_filing_center: string;
  conversation_type: string;
  chat_role1: string;
  chat_role2: string;
  chat_role1_displayname: string;
  chat_role2_displayname: string;
  chat_role1_message_template: string;
  chat_role2_message_template: string;
  initiation_message: string;
  show_patient_status_dashboard: string;
  message_reply_timeout: string;
  patient_name_display: string;
  enable_external_integration: string;
  enable_where_is_my_delivery: string;
  enable_when_is_my_nurse_coming: string;
  enable_masked_discussion_group: string;
  masked_discussion_recipient_roles: string;
  wp_enroll_url: string;
  branch_start_time: string;
  branch_end_time: string;
  tenant_timezone: string;
  patient_reminder_time: string;
  patient_reminder_types: string;
  allow_prefilling_during_partial_enrollment: string;
  caregiver_sees_enrollment_invitation_data: string;
  show_chatwith_modal: string;
  staffs_aware_enroll_by_sms: string;
  staffs_aware_enroll_by_email: string;
  partner_patient_referral_initiation_introduction: string;
  partner_referral_form_tags: string;
  web_notification_hide_behaviour: string;
  enrollment_reminder_types: string;
  show_staff_supply_inventory: string;
  enable_forms: string;
  patient_referrals: string;
  url_expiration_days_in_autoenrollment: string;
  enable_double_verification: string;
  enable_user_enrollment: string;
  branch_working_days: string;
  enable_download_citus_health_connect: string;
  menu_display_behaviour_of_disabled_feature: string;
  default_category_of_chat_window_popup: string;
  archive_signature_files_to_fc_after_attachment: string;
  defaulth_fc_for_chatlogs: string;
  defaulth_filenameformat_for_chatlogs_fc: string;
  title_incoming_filing_center: string;
  title_outgoing_filing_center: string;
  enable_video_chat: string;
  external_system_identifier: string;
  enable_patient_intake: string;
  enable_activity_hub: string;
  filename_character_limit_of_fc: string;
  allow_multiple_organization: string;
  new_patient_chat_welcome_message: string;
  weekend_days: string;
  associate_pdg_with_patient_id: string;
  field_label_of_patient_association_in_pdg: string;
  automatically_create_pdg_on_patient_enroll: string;
  member_roles_for_pdg_which_create_automatically: string;
  enable_form_auto_save: string;
  enable_form_save_draft_patient: string;
  enable_form_save_draft_staff: string;
  patient_message_sms_notifcation_beyond_branch_24hr: string;
  progress_note_integration_data_format: string;
  staff_message_sms_notifcation: string;
  enable_progress_note_integration: string;
  esi_code_for_patient_identity: string;
  esi_code_for_staff_identity: string;
  esi_code_for_staff_name: string;
  add_user_to_group_on_invite_to_chat_session: string;
  message_for_clinical_services_in_feedback_form: string;
  chie_admin_user: string;
  recipient_email_for_support_widget: string;
  view_other_branch_dashboard: string;
  enable_worklist_center: string;
  enable_chat_window_sidebar: string;
  progress_note_integration_mode: string;
  pn_partner_token: string;
  pn_webhook_endpoint: string;
  pn_callback_url: string;
  pn_base_url: string;
  pn_resource_url: string;
  pn_webhook_endpoint_authorization: string;
  esi_code_for_pn_clientid: string;
  show_education_training: string;
  show_schedule_center: string;
  enable_move_form: string;
  generated_progress_note_content_format: string;
  label_for_file_generation: string;
  cross_tenant_roles_for_pdg_create_automatically: string;
  enable_nursing_agencies_visibility_restrictions: number;
  na_manage_education_material_roles: string;
  na_manage_group_roles: string;
  default_staff_role: string;
  documet_exchange_mode: string;
  wh_endpoint: string;
  wh_autharization_key: string;
  wh_resourse_url: string;
  wh_callback_url: string;
  wh_endpoint_form: string;
  wh_resourse_url_form: string;
  wh_document_reference_integration_base_url: string;
  enable_integration_status_worklist: string;
  show_push_notification_for_chat_room_invite: string;
  na_patients_chat_with_pharmacy: string;
  na_staffs_chat_with_pharmacy: string;
  enable_collaborate_edit: string;
  save_as_draft_message_interval: string;
  organization_model: string;
  esi_code_for_clientid: string;
  esi_code_for_customerid: string;
  enable_delivery_center: string;
  esi_code_for_branchid: string;
  enable_multipart_mrn: string;
  show_staffid_in_user_invite: string;
  make_mrn_field_mandatory_in_patient_invite: string;
  pn_authorization_type: string;
  wh_integration_url: string;
  message_for_clinical_services_in_patient_feedback: string;
  signature_serverside: string;
  alternative_background_color_pdf_generated: string;
  enable_partial_invite_in_auto_enrollment: string;
  make_staffid_field_mandatory_in_staff_invite_page: string;
  show_manage_alerts: string;
  enable_esi_value_in_document_category: string;
  default_inbound_fc: string;
  access_token_for_reading_inbound_document: string;
  default_staff_user_tag: string;
  default_partner_role: string;
  default_partner_user_tag: string;
  enable_auto_enrollment_integration_for_virtual_sta: string;
  enable_auto_enrollment_integration_for_virtual_staff: string;
  enable_message_center: string;
  enable_message_flagging: string;
  patient_direct_link_callback_url: string;
  restrict_to_branch_hour: string;
  enable_sftp_integration: string;
  enable_disclose_PHI: string;
  consolo_baseurl: string;
  consolo_authorize: string;
  consolo_access_token: string;
  consolo_userInfo: string;
  consolo_clientId: string;
  consolo_client_secret: string;
  consolo_scope: string;
  fax_queue_show_warning: string;
  flex_site_patients_can_chat_with_internal_staffs: string;
  pdg_member_add_behavior: string;
  enable_locking_of_progress_notes_in_third_party_application: string;
  enable_patient_info_from_third_party_app: string;
  enroll_message: string;
  enrollMessage: string;
  default_category_of_message_display: string;
  ignore_duplicate_checking_in_virtual_patient_onboarding: string;
  enable_direct_link: string;
  form_mobile_orientaion: string;
  enable_mobile_web_app: string;
  enable_email_for_staff: string;
  enable_email_for_patient: string;
  enable_appless_model: string;
  enable_user_center: string;
  enable_verification_of_cell_and_mobile: string;
  app_short_link: string;
  enable_patient_drieven_flow_forms: string;
  form_assignment_management_source: string;
  patient_reminder_checking_type: string;
  enable_support_widget_branding: number;
  support_widget_from_email: string;
  support_widget_email_color_code: string;
  inbox_form_archive_remove_self: number;
  tenant_timezoneName: string;
  enable_multi_admissions?: string;
  enable_multisite: string;
}

interface Config {
  app_name?: string;
  token: string;
  escalation_time: string;
  message_refresh_interval: string;
  session_timeout: string;
  session_timeout_warning: string;
  primary_nurse_id: string;
  primary_pharmacist_id: string;
  patient_approver_role_id: string;
  clinician_approver_role_id: string;
  chat_auto_translate: string;
  ga_tracking: string;
  message_forwarding_behaviour: string;
  watchtower_tracking: string;
  esign_api_baseurl: string;
  esign_client_id: string;
  esign_api_key: string;
  clinician_roles_beyond_working_hours: string;
  clinician_roles_on_working_hours: string;
  show_infusion_support: string;
  show_prototypes: string;
  home_infusion_start_time: string;
  home_infusion_end_time: string;
  show_support_and_feedback_form: string;
  infusion_support_clinician_roles: string;
  default_clinician_roles_available: string;
  message_escalation_behavior: string;
  no_clinician_message: string;
  infusion_support_where_is_my_delivery: string;
  infusion_support_when_is_my_nurse_coming: string;
  working_hour: string;
  default_escalation_users: string;
  default_clinician_roles_available_on_working_hour: string;
  no_clinician_message_on_working_hours: string;
  infusion_support_my_nurse_coming_on_working_hours: string;
  infusion_support_my_delivery_on_working_hours: string;
  infusion_support_clinician_roles_on_working_hours: string;
  toggle_chat_translation: string;
  show_form_features: string;
  allow_virtual_patient: string;
  form_approver_clinician: string;
  show_patient_supply_inventory: string;
  clinical_liaison: string;
  show_user_tagging: string;
  enable_configurable_faq: string;
  show_document_tagging: string;
  show_chat_history_to_new_participant: string;
  patient_signup_with_admin_approval: string;
  clinician_signup_with_admin_approval: string;
  supply_request_recipient_type: string;
  immediate_assistance_message: string;
  enable_filing_center: string;
  conversation_type: string;
  chat_role1: string;
  chat_role2: string;
  chat_role1_displayname: string;
  chat_role2_displayname: string;
  chat_role1_message_template: string;
  chat_role2_message_template: string;
  initiation_message: string;
  show_patient_status_dashboard: string;
  message_reply_timeout: string;
  patient_name_display: string;
  enable_external_integration: string;
  enable_where_is_my_delivery: string;
  enable_when_is_my_nurse_coming: string;
  enable_masked_discussion_group: string;
  masked_discussion_recipient_roles: string;
  wp_enroll_url: string;
  branch_start_time: string;
  branch_end_time: string;
  tenant_timezone: string;
  patient_reminder_time: string;
  patient_reminder_types: string;
  allow_prefilling_during_partial_enrollment: string;
  caregiver_sees_enrollment_invitation_data: string;
  show_chatwith_modal: string;
  staffs_aware_enroll_by_sms: string;
  staffs_aware_enroll_by_email: string;
  partner_patient_referral_initiation_introduction: string;
  partner_referral_form_tags: string;
  web_notification_hide_behaviour: string;
  enrollment_reminder_types: string;
  show_staff_supply_inventory: string;
  enable_forms: string;
  patient_referrals: string;
  url_expiration_days_in_autoenrollment: string;
  enable_double_verification: string;
  enable_user_enrollment: string;
  branch_working_days: string;
  enable_download_citus_health_connect: string;
  menu_display_behaviour_of_disabled_feature: string;
  default_category_of_chat_window_popup: string;
  archive_signature_files_to_fc_after_attachment: string;
  defaulth_fc_for_chatlogs: string;
  defaulth_filenameformat_for_chatlogs_fc: string;
  title_incoming_filing_center: string;
  title_outgoing_filing_center: string;
  enable_video_chat: string;
  external_system_identifier: string;
  enable_patient_intake: string;
  enable_activity_hub: string;
  enable_save_signature_to_profile: boolean;
  filename_character_limit_of_fc: string;
  allow_multiple_organization: string;
  new_patient_chat_welcome_message: string;
  weekend_days: string[];
  associate_pdg_with_patient_id: string;
  field_label_of_patient_association_in_pdg: string;
  automatically_create_pdg_on_patient_enroll: string;
  member_roles_for_pdg_which_create_automatically: string;
  enable_form_auto_save: string;
  enable_form_save_draft_patient: string;
  enable_form_save_draft_staff: string;
  patient_message_sms_notifcation_beyond_branch_24hr: string;
  progress_note_integration_data_format: string;
  staff_message_sms_notifcation: string;
  enable_progress_note_integration: string;
  esi_code_for_patient_identity: string;
  esi_code_for_staff_identity: string;
  esi_code_for_staff_name: string;
  add_user_to_group_on_invite_to_chat_session: string;
  message_for_clinical_services_in_feedback_form: string;
  chie_admin_user: string;
  recipient_email_for_support_widget: string;
  view_other_branch_dashboard: string;
  enable_worklist_center: string;
  enable_chat_window_sidebar: string;
  progress_note_integration_mode: string;
  pn_partner_token: string;
  pn_webhook_endpoint: string;
  pn_callback_url: string;
  pn_base_url: string;
  pn_resource_url: string;
  pn_webhook_endpoint_authorization: string;
  esi_code_for_pn_clientid: string;
  show_education_training: string;
  enable_visit_schedule: string;
  enable_move_form: string;
  generated_progress_note_content_format: string;
  label_for_file_generation: string;
  cross_tenant_roles_for_pdg_create_automatically: string;
  enable_nursing_agencies_visibility_restrictions: number;
  na_manage_education_material_roles: string;
  na_manage_group_roles: string;
  default_staff_role: string;
  documet_exchange_mode: string;
  wh_endpoint: string;
  wh_autharization_key: string;
  wh_resourse_url: string;
  wh_callback_url: string;
  wh_endpoint_form: string;
  wh_resourse_url_form: string;
  wh_document_reference_integration_base_url: string;
  enable_integration_status_worklist: string;
  show_push_notification_for_chat_room_invite: string;
  na_patients_chat_with_pharmacy: string;
  na_staffs_chat_with_pharmacy: string;
  enable_collaborate_edit: string;
  save_as_draft_message_interval: string;
  organization_model: string;
  esi_code_for_clientid: string;
  esi_code_for_customerid: string;
  enable_delivery_center: string;
  esi_code_for_branchid: string;
  enable_multipart_mrn: string;
  show_staffid_in_user_invite: string;
  make_mrn_field_mandatory_in_patient_invite: string;
  pn_authorization_type: string;
  wh_integration_url: string;
  message_for_clinical_services_in_patient_feedback: string;
  signature_serverside: string;
  alternative_background_color_pdf_generated: string;
  enable_partial_invite_in_auto_enrollment: string;
  make_staffid_field_mandatory_in_staff_invite_page: string;
  show_manage_alerts: string;
  enable_esi_value_in_document_category: string;
  default_inbound_fc: string;
  access_token_for_reading_inbound_document: string;
  default_staff_user_tag: string;
  default_partner_role: string;
  default_partner_user_tag: string;
  enable_auto_enrollment_integration_for_virtual_sta: string;
  enable_auto_enrollment_integration_for_virtual_staff: string;
  enable_message_center: string;
  enable_message_flagging: string;
  patient_direct_link_callback_url: string;
  restrict_to_branch_hour: string;
  enable_sftp_integration: string;
  enable_disclose_PHI: string;
  consolo_baseurl: string;
  consolo_authorize: string;
  consolo_access_token: string;
  consolo_userInfo: string;
  consolo_clientId: string;
  consolo_client_secret: string;
  consolo_scope: string;
  fax_queue_show_warning: string;
  flex_site_patients_can_chat_with_internal_staffs: string;
  pdg_member_add_behavior: string;
  enable_locking_of_progress_notes_in_third_party_application: string;
  enroll_message: string;
  enrollMessage: string;
  default_category_of_message_display: string;
  ignore_duplicate_checking_in_virtual_patient_onboarding: string;
  enable_direct_link: string;
  form_mobile_orientaion: string;
  enable_mobile_web_app: string;
  enable_email_for_staff: string;
  enable_email_for_patient: string;
  enable_appless_model: string;
  enable_user_center: string;
  enable_verification_of_cell_and_mobile: string;
  app_short_link: string;
  enable_patient_drieven_flow_forms: string;
  form_assignment_management_source: string;
  patient_reminder_checking_type: string;
  enable_support_widget_branding: number;
  support_widget_from_email: string;
  support_widget_email_color_code: string;
  inbox_form_archive_remove_self: number;
  tenant_timezoneName: string;
  enable_sftp_integration_machform_value: string;
  enable_appless_video_chat: string;
  enable_multi_admissions?: string;
  enable_multisite: string;
  showElevio?: any;
  default_patients_workflow: string;
  magiclink_verification_token_expiration_time: number;
  magiclink_verification_expiry_time: string;
  enable_talentlms: string;
  talentlms_url: string;
}

export interface IdpConfigFetchResponse {
  status: number;
  data: IdpConfigData;
  message: string;
}

export interface IdpConfigData {
  consolo_baseurl: string;
  consolo_authorize: string;
  consolo_access_token: string;
  consolo_userInfo: string;
  consolo_clientId: string;
  consolo_client_secret: string;
  consolo_scope: string;
  response_type: string;
  sso_service_type: string;
  enable_alternate_username_login: string;
}

export interface AppAuthURLResponse {
  app_auth_link: string;
  tenant_id: number;
  oktaEnabled: boolean;
  ssoId:string;
  talentlmsEnabled: boolean;
  talentlmsIdpId: string;
}
export interface SessionExtraParam {
  date: string;
  dayNumber: string;
  currentTime: string;
  clientLoginedTimeZone: number;
}
export interface Banner {
  assignedSites: string;
  alert_type: number;
  bannerid: number;
  branchesToShow: string;
  created_branch: number;
  description: string;
  end_date: string;
  id: string;
  is_published: number;
  start_date: string;
  start_date_formated: number;
  subject: string;
  message?: string;
  showMore?: boolean;
}
export interface BannerPollingData {
  bannerId: string;
  data: Banner[];
  message: string;
  status: string;
}
export interface SiteConfigResponse {
  branch_end_time: string;
  branch_start_time: string;
  working_hour: string;
  branch_working_days: string;
  timezoneName: string;
  magiclink_verification_token_expiration_time: string;
  magiclink_verification_expiry_time: string;
  magiclink_token_expiration_time: string;
}
export interface UserPushRegistrationSocketData {
  uniqueDeviceId: string;
  registrationId: string;
  userId: string;
  platform: string;
}
export interface FormIndividualCounts {
  pending: number;
  completed: number;
  archived: number;
  draft: number;
}
export interface OutOfOfficeInfo {
  isOutOfOffice: boolean
  message: string
  startDateTime: string
  endDateTime: string
  endDatePassed: boolean
}
