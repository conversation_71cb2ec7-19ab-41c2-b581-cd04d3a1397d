@import "variables.scss";
@import "theme.scss";
@import url("../src/theme/vitalcare/variables.scss");
@import url("../src/theme/matrixcare/variables.scss");
@import url("../src/theme/soleoconnect/variables.scss");
@import url("../src/theme/virtisconnect/variables.scss");
@import url("../src/theme/trustport/variables.scss");
@import url("../src/theme/myardon/variables.scss");
@import url("../src/theme/myInhouseconnect/variables.scss");
@import url("../src/theme/kabafusion/variables.scss");
@import url("../src/theme/intermountain/variables.scss");
@import url("../src/theme/rxconnect/variables.scss");
@import url("../src/theme/ameripharma/variables.scss");
@import url("../src/theme/kennebecpharmacy/variables.scss");
@import url("../src/theme/realocares/variables.scss");
@import url("../src/theme/orsinipharmacy/variables.scss");
@import url("../src/theme/premierinfusion/variables.scss");
@import url("../src/theme/myanovo/variables.scss");
@import url("../src/theme/nutrishare/variables.scss");

*:focus {
    outline: none;
}

html {
    background: #fff;
    --background: none;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    -webkit-animation: autofill 0s forwards;
    animation: autofill 0s forwards;
}

@keyframes autofill {
    100% {
        background: transparent;
        color: inherit;
    }
}

@-webkit-keyframes autofill {
    100% {
        background: transparent;
        color: inherit;
    }
}

// Added this now; need to set the fonts.
body {
    font-family: "Open Sans", sans-serif, Helvetica, Arial !important;
}

// Add ionic elements style here.
ion-content {
    --ion-background-color: #fafafa;
}

// Added Placeholder styles.
input::placeholder,
textarea::placeholder {
    color: #adadad;
}

input::-moz-placeholder,
textarea::-moz-placeholder {
    color: #adadad;
}

input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
    color: #adadad;
}

input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
    text-indent: 0;
    color: #adadad;
}

// Add other common styles here.
.common {
    &-checkbox {
        --checkbox-background-checked: var(--ion-color-checked);
        --border-color-checked: var(--ion-color-checked);
    }

    &-radio-button {
        --color-checked: var(--ion-color-checked);
    }

    &-header {
        border: none !important;
        position: relative;
        height: calc(50px + constant(safe-area-inset-top));
        height: calc(50px + env(safe-area-inset-top));
        z-index: 9999;

        ion-toolbar {
            height: auto;
            --ion-background-color: transparent;
        }

        .header-title {
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 17px;
            font-weight: 500;
            margin-top: 18px;
        }

        .header-menu {
            color: var(--ion-color-skin-secondary-bright);
            position: absolute;
            --ion-toolbar-color: var(--ion-color-skin-secondary-bright);
            font-size: 17px;
            z-index: 1;
            min-width: initial;
            min-height: 31px;
            font-weight: 400;
            line-height: 32px;
        }

        .header-ion-title {
            display: block;
            position: absolute;
            top: 0;
            right: 0;
            left: 0;
            z-index: 0;
            overflow: hidden;
            margin: 0 10px;
            min-width: 30px;
            height: 43px;
            line-height: 44px;
            text-align: center;
            text-overflow: ellipsis;
            white-space: nowrap;
            color: #fff;
            margin-left: 30px;
            margin-right: 20px;
            width: calc(100% - 90px);
        }

        .pull {
            float: right;
        }
    }

    &-document-head-title {
        margin-top: 30px;
    }

    &-header:after {
        position: absolute;
        width: 100%;
        height: 30px;
        left: 0px;
        bottom: -20px;
        content: "";
        background-size: 100% 30px !important;
    }

    &-footer {
        --ion-toolbar-background: var(--ion-color-skin-main);
        bottom: 0;
        border-bottom-width: 0;
        background-position: top;
        height: calc(44px + constant(safe-area-inset-bottom));
        height: calc(44px + env(safe-area-inset-bottom));
    }

    &-back-btn-icon {
        color: var(--ion-color-skin-secondary-bright);
        font-size: 35px;
        margin-bottom: 8px;
    }

    &-full-width {
        width: 100% !important;
    }

    &-page-layout {
        top: 20px;
        padding: 0 15px;
    }

    &-page-sections {
        padding: 11px;
        padding-bottom: 15px !important;
    }

    &-page-btns {
        text-align: center;
        width: 100%;
        border-radius: 0;
        margin-bottom: 3px;
        padding-bottom: 5px;

        span {
            display: block;
            color: #fff;
            font-size: 20px;
            text-align: center;
            padding-top: 5px;
        }

        img {
            display: inline-block;
            margin: auto;
            width: 80px;
        }

        i {
            position: relative;
        }
    }

    &-btn1 {
        background: #4bacc6;
    }

    &-btn2 {
        background: #71c4db;
    }

    &-btn3 {
        background: #8ecfe0;
    }

    &-btn4 {
        background: #9ecfe0;
    }

    &-btn5 {
        background: #bde6f4;
    }

    &-count {
        background: var(--ion-color-count-bright-bg);
        min-width: 33px;
        height: 33px;
        border-radius: 50%;
        color: var(--ion-color-count-bright-color);
        position: absolute;
        right: 0;
        top: -16px;
        padding-top: 8px;
        font-weight: bold;
        text-align: center;
        font-size: 14px;
        z-index: 9999;
    }

    &-input-row {
        color: #555555;
        margin: 6px 0px;
        padding-top: 5px;
    }

    &-input {
        margin-top: 3px;
        margin-right: 0px;
        width: 100%;
        border: 1px solid #e2e2e2;
        min-height: 38px;
        color: #555555;
        -webkit-appearance: none;
        -moz-appearance: none;
        text-indent: 0.01px;
        text-overflow: "";
        background-size: 30px auto;
        background-color: #ffffff;
        padding: 5px 32px 5px 10px;
        --padding-start: 8px;
    }

    &-textarea-input {
        flex: 1;
        display: block;
        padding: 5px;
        width: 100%;
        height: 100px;
        background: #ffffff;
        color: #555555;
        resize: none;
        border: 1px solid #e2e2e2;
    }

    &-auth-input {
        background: none;
        color: #555555;
        font-size: 22px;
        line-height: 18px;
        min-height: 48px;
        padding: 0 0 0 50px;
        width: 100%;
        border: 0;
    }

    &-auth-button {
        background: var(--ion-color-skin-secondary-bright);
        color: #ffffff;
        font-size: 23px;
        border-radius: 3px;
        line-height: 38px;
        min-height: 50px;
        -webkit-box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.25);
        -moz-box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.25);
        box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.25);
        margin-top: 10px;
        margin-bottom: 10px;
        display: block;
        width: 100%;
        text-transform: uppercase;
    }

    &-auth-icon-input {
        font-size: 25px;
        position: absolute;
        top: 8px;
        left: 0px;
        color: #359cb2;
        border: 1px solid #359cb2;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        text-align: center;
        line-height: 36px;

        ion-icon {
            height: 100%;
        }
    }

    &-full-button {
        background: var(--ion-color-skin-secondary-bright) none repeat scroll 0 0 !important;
        border-radius: 3px;
        color: #ffffff;
        font-size: 15px !important;
        line-height: 18px !important;
        min-height: 35px !important;
        width: 100%;
        -webkit-box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.25);
        -moz-box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.25);
        box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.25);
    }

    &-button {
        --background: var(--ion-color-skin-secondary-bright);
        --color: #ffffff;
        text-transform: none;
    }

    &-box-container {
        width: 100%;
        padding: 35px 25px 5px 25px;
    }

    &-auth-input-row {
        border-bottom: 1px solid rgba(53, 156, 178, 0.3);
        color: #555555;
        padding: 4px 0;
        margin: 6px 0px;
        position: relative;
    }

    &-document-header {
        position: relative;
        padding: 15px;
        background: #fff;
    }

    &-document-head-title {
        color: var(--ion-color-head-title);
        font-size: 18px;
    }

    &-document-head-menu {
        position: absolute;
        right: 10px;
        margin-top: -40px;

        ul {
            float: right;

            li {
                float: left;
                color: #7c7c7c;
                list-style: none;

                .active {
                    color: var(--ion-color-active) !important;
                }

                .view-list {
                    font-size: 30px;
                    margin-right: 15px;
                }

                .view-grid {
                    font-size: 28px;
                    margin-right: 15px;
                }
            }
        }
    }

    &-plain-header {
        --ion-toolbar-background: var(--ion-color-skin-main);
        color: var(--ion-toolbar-color);

        .header-ion-title {
            padding: 0px 40px;
        }

        .header-title {
            color: #fff;
            font-size: 17px;
            border: medium none;
            text-align: center;
            margin-top: 0px;
            margin-bottom: 0px;
            overflow: scroll hidden;
            text-overflow: ellipsis;
        }

        /* Hide scrollbar for Chrome, Safari and Opera */
        .header-title::-webkit-scrollbar {
            display: none;
        }

        /* Hide scrollbar for IE, Edge and Firefox */
        .header-title {
            -ms-overflow-style: none;
            /* IE and Edge */
            scrollbar-width: none;
            /* Firefox */
        }

        .header-menu ion-button {
            --color: var(--ion-color-skin-secondary-bright);
            text-transform: capitalize;
        }

        .header-menu {
            position: absolute;

            &.right {
                right: 0;
            }
        }
    }

    &-tab-segment {
        ion-segment {
            --ion-color-primary: var(--ion-color-skin-secondary-bright);
            --ion-color-primary-shade: var(--ion-color-skin-secondary-bright);
        }
    }

    &-list {
        ion-item {
            border-bottom: solid 1px #e2e2e2;
            color: #555555;
        }
    }

    &-bottom-action-buttons {
        display: flex;

        .button-col {
            flex: 1;
            padding: 8px;

            .action-button {
                height: 40px;
                color: #fff;
                width: 100%;
                background: var(--ion-color-skin-secondary-bright);
                font-size: 23px;
                border-radius: 3px;
                -webkit-box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.25);
                -moz-box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.25);
                box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.25);
            }

            button.disabled,
            button[disabled] {
                opacity: 0.4;
                cursor: default !important;
                pointer-events: none;
            }
        }
    }

    &-sweet .alert-wrapper {
        border-radius: 5px;

        .alert-title {
            font-size: 18px;
        }

        .cancel-btn {
            background-color: #f39834;
            color: #fff;
        }

        .wait-btn {
            background-color: #7c7c7c;
            color: #fff;
        }
    }

    &-video .alert-wrapper {
        background: none;
        background-color: rgba(0, 0, 0, 0.6);
        border-radius: 10px;
        font-weight: 500;

        .alert-title {
            color: #fff;
            text-align: center;
        }

        .alert-button-group {
            margin-left: -36px;
        }

        .vid-btn {
            padding: 6px 12px;
            background-image: none;
            border: 1px solid transparent;
            border-radius: 4px;
            margin-bottom: 0;
            font-size: 14px;
            font-weight: normal;
            line-height: 1.42857143;
            text-align: center;
            white-space: nowrap;
        }

        .vid-btn-success {
            color: #fff;
            background-color: #5cb85c;
            border-color: #4cae4c;
        }

        .vid-btn-danger {
            color: #fff;
            background-color: #d9534f;
            border-color: #d43f3a;
        }
    }

    &-alert .alert-wrapper {
        min-width: 93% !important;

        .alert-message {
            max-height: 300px;
            color: #000 !important;
        }

        .warn-img {
            text-align: center;

            img {
                width: 50px;
            }
        }

        .warn-text {
            color: #555555 !important;
            font-size: 19px;
            line-height: 23px;
        }

        .warn-btn {
            width: 50%;
            border: none !important;
            margin: 0px !important;
            background: none !important;
            border-radius: 0px;
            color: #999999;
            font-size: 18px;
            min-height: 45px !important;
            line-height: 18px !important;
            padding: 5px 5px;
            border-top: 1px solid #e2e2e2 !important;
            text-transform: capitalize;

            .alert-button-inner.sc-ion-alert-md {
                justify-content: center;
            }
        }

        .alert-cancel {
            color: #999999;
        }

        .alert-ok {
            color: var(--ion-color-form-submit);
        }

        .alert-update {
            color: #6cc188;
        }

        .full-width {
            width: 100%;
        }
    }

    &-message {
        --backdrop-opacity: 0.5;
        --width: 70%;
    }

    &-na-space {
        display: contents;
    }

    &-flag-icon {
        padding: 4px;
        float: left;
        margin-right: 5px;
        font-size: 12px;
        text-align: center;
        width: 20px;
        height: 20px;
        color: #fff;
        font-weight: 700;

        &.high {
            background: red;
        }

        &.medium {
            background: orange;
        }

        &.low {
            background: blue;
        }
    }

    &-emoji-popover::part(content) {
        width: 300px;
    }

    &-message-parse {
        white-space: pre-wrap;

        audio {
            width: -webkit-fill-available;
        }

        a {
            color: inherit;
            text-decoration: inherit;
        }
    }

    &-blue-popover::part(content) {
        --background: var(--ion-color-skin-main);
        width: fit-content;
        border: 0;
    }

    &-blue-popover::part(arrow) {
        --background: var(--ion-color-skin-main);
    }

    &-wide-popover::part(content) {
        width: fit-content;
    }

    &-show-message {
        color: #58595b !important;
        padding: 10px 5px !important;
        line-height: 22px;
        background: #fafafa !important;
        background-size: 15px auto !important;
        text-align: center;
    }

    &-load-more-btn {
        position: relative;
        display: block;
        margin: 0 auto;
        cursor: pointer;
        --background: var(--ion-color-skin-secondary-bright);
        text-transform: capitalize;
        border: none;
        color: #ffffff;
        border-radius: 12px;
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 10px;
        margin-top: 10px;
        width: 160px;
    }

    &-load-more-small {
        button {
            display: block;
            background: var(--ion-color-skin-secondary-bright);
            text-transform: capitalize;
            color: #ffffff;
            border-radius: 12px;
            padding: 4px 12px;
            width: 200px;
            margin: auto;
            font-size: 14px;
            font-weight: bold;
        }

        .modal-button {
            margin-top: 10px;
            margin-bottom: 11px;
        }
    }

    &-badge {
        background-color: transparent;
        color: white;
        z-index: 1;
        display: inline-block;
        padding: 3px 8px;
        min-width: 10px;
        border-radius: 10px;
        vertical-align: baseline;
        text-align: center;
        white-space: nowrap;
        font-weight: bold;
        font-size: 10px;
        line-height: 16px;
    }

    &-active {
        color: var(--ion-color-active);
    }

    &-swipe-buttons {
        ion-item-option {
            text-transform: capitalize;
        }

        ion-icon {
            font-size: 24px;
        }

        .archive {
            --background: #15b0e6;
        }

        .forward {
            --background: #7ed459;
        }

        .flag {
            --background: #336897;
        }

        .restore {
            --background: #6cc187;
        }

        .resend {
            --background: #6cc187;
        }

        .cancel {
            --background: #15b0e6;
        }
    }

    &-validation-error {
        padding-top: 14px;
        color: red;
        font-family: "Open Sans", sans-serif, Helvetica, Arial;
        font-size: 15px;
    }

    &-toggle-container {
        margin: 0px;
        font-size: 14px;
        float: left;
        line-height: 18px;
        padding-top: 5px;
        flex: 1;
        display: block;
        padding: 5px;
        padding-top: 0px;
        width: 50%;
    }

    &-content-span {
        font-size: 14px;
        margin: 0;
        padding: 0;
        border: 0;
        vertical-align: baseline;
    }

    &-mobile-input {
        position: relative;
        display: inline-block;
        position: absolute;
    }

    &-input-text-indent {
        padding: 4px 62px;
    }

    &-flag-input {
        width: 0;
    }

    &-file-browse {
        background-size: cover;
        display: block;
        position: relative;
        width: 130px;
        height: 42px;
        float: left;
        bottom: 8px;
        border-radius: 30px;
        cursor: pointer;
        border: 1px solid #ffffff;
        background: #4fbed7;
        color: #ffffff;
        padding: 1px;
        top: 10px;
        margin-right: 10px;
        margin-bottom: 10px;

        .browse-span {
            float: left;
            padding-top: 12px;
            margin-left: 37px;
            color: #ffffff;
            font-size: 14px;
        }

        img {
            cursor: pointer;
            position: absolute;
            left: 6px;
            top: 4px;
            width: 30px;
        }

        .selectfile {
            background: none;
            color: #555555;
            font-size: 22px;
            line-height: 18px;
            min-height: 48px;
            padding: 0 0 0 50px;
            background: #ffffff;
            border: 1px solid #e6e6e6;
        }
    }

    &-flag-sec {
        border: 1px solid #e2e2e2;
        height: 46px;
        margin-top: 7px;
        width: 54px;
        text-align: center;
        margin-top: 3px;
        padding-top: 5px;
        padding-left: 3px;
        .arrow-img {
            padding-bottom: 16px;
            max-width: 25%;
            border: 0;
            margin-top: -17px;
            margin-bottom: 5px;
            margin-left: 43px;
        }

        .arrow-img-space {
            margin-top: -19px;
        }

        .country-popover {
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;
            justify-content: center;
            align-items: center;
        }
    }

    &-file-browse input {
        background: rgba(0, 0, 0, 0) none repeat scroll 0 0;
        border: medium none;
        height: 35px;
        opacity: 0;
        padding: 0;
        position: absolute;
        top: -6px;
        width: 130px;
        right: 0px;
    }

    &-sign-pad {
        width: 100%;
        display: block;

        canvas {
            border: 2px dashed #228072;
        }
    }

    &-uploads {
        margin-left: 8px;
        display: inline-block;
        color: #000;
        margin-top: 15px;

        .file-close {
            color: var(--ion-color-form-submit);
            font-size: 22px;
            margin-left: 5px;
            cursor: pointer;
        }

        .filename {
            background: #fff none repeat scroll 0 0;
            display: inline-block;
            font-size: 14px;
            line-height: 30px;
            min-height: 30px;
            padding: 4px 25px 4px 10px;
            position: relative;
            top: -5px;
            width: auto;
            color: var(--ion-color-form-submit);
            border-radius: 25px;
            border: 1px solid #ddd;
            margin-right: 10px;
            margin-bottom: 10px;
        }
    }

    &-medium-popup:part(content) {
        position: absolute;
        overflow: hidden;
        border-radius: 10px;
        width: 93%;
        height: 30%;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        margin: auto;
        border: 0;
        box-shadow: 0 16px 20px rgba(0, 0, 0, 0.4);
        background-color: #fafafa;
    }

    &-choose-recipients {
        border: 1px solid #e2e2e2;
        border-radius: 0px;
        color: #4c555b;
        float: left;
        margin-bottom: 20px !important;
        padding: 0;
        margin-left: 0px;
        background: #ffffff;
        width: 100%;

        .display-text-wrap {
            background: #fff url(/assets/icon/chat/select-plus-icon.png) no-repeat right center;
            width: 100%;
            height: 44px;
            padding-right: 80px;
            border: 1px solid #e6e6e6;
            padding-left: 8px;
            text-align: left;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;

            .display-text-label {
                line-height: 44px;
                font-size: 14px;
                height: 44px;
            }

            #broadcastForm span {
                font-size: 15px !important;
                color: #444 !important;
            }
        }
    }

    &-search-box {
        display: flex;

        .search-container {
            flex: 1;

            .search-box {
                height: 35px;
                border: 1px solid #e2e2e2;
                color: #555555;
                background: #fff;
                width: 100%;
                border-radius: 5px;
                padding: 0px 10px;
            }
        }

        .action-buttons {
            .search-icon {
                color: var(--ion-color-skin-secondary-bright);
                font-size: 30px;
                margin: 0 10px;
            }

            .refresh-icon {
                color: var(--ion-color-skin-secondary-bright);
                font-size: 24px;
                padding: 5px;
                margin: 0 10px;
            }

            .add-patient-icon {
                color: var(--ion-color-skin-secondary-bright);
                font-size: 26px;
                padding: 5px;
                margin: 0 10px;
            }
        }
    }

    &-error-message {
        color: red;
        font-size: 15px;
        text-align: center;
    }

    &-no-items {
        color: var(--ion-color-skin-secondary-bright);
        font-size: 15px;
        text-align: center;
    }

    &-hide-file-input {
        position: absolute;
        width: 0;
        height: 0;
        opacity: 0;
        pointer-events: none;
    }

    &-capitalize-text {
        text-transform: capitalize;
    }

    &-start-padding .sc-ion-input-ios-h {
        --padding-start: 8px;
    }
}

.justify-center {
    display: flex;
    justify-content: center;
}

.ion-input-style {
    color: var(--ion-color-black);
}

.send-form-via-modal {
    .alert-message {
        text-align: center;
        font-size: 18px;
        font-weight: 700;
    }

    // Click on out side of the alert remove highlight button bg color when backDropDismiss false
    .alert-button.ion-focused.sc-ion-alert-ios,
    .alert-button.ion-focused.sc-ion-alert-md {
        background: transparent;
    }

    .alert-button-inner {
        color: var(--ion-color-blumine);
        text-transform: none;
    }
}

.show-button-center {
    .alert-button-group.sc-ion-alert-md {
        justify-content: center;
    }
}

.wrap-ellipsis {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.set-position::part(content) {
    top: calc(100% - 225px);
}

.set-ios-video-call-bg {
    background: transparent;
    --background: none;
}

.ion-caret-down {
    color: var(--ion-color-skin-secondary-bright);
}

.hide {
    display: none;
}

.hide-div {
    height: 0;
    width: 0;
    opacity: 0;
}

.disable-filter {
    pointer-events: none;
    opacity: 0.4;
}

.time-zone-footer {
    margin: 10px;
    font-weight: bold;
    font-size: 12px;
}

.common-tab-segment {
    ion {
        &-segment {
            --background: #ffffff;
            padding: 10px;
            border-bottom: 1px solid #e2e2e2;
        }

        &-segment-button {
            --background: #ffffff;
            --color: var(--ion-color-skin-secondary-bright);
            --background-checked: var(--ion-color-skin-secondary-bright);
            --color-checked: #ffffff;
            --background-hover: var(--ion-color-skin-secondary-bright);
            --ion-background-color: var(--ion-color-skin-secondary-bright);
        }

        &-label {
            white-space: normal;
            text-transform: initial !important;
            font-weight: 600 !important;
        }
    }
}

.set-border-bg-color {
    border: 1px solid #e2e2e2;
    background: var(--ion-color-white);
    --padding-start: 8px;
}

.filing-center-header {
    .header-ion-title {
        margin-left: 16px;
    }
}

ion-button[color="de-york"] {
    &.ion-activated {
        --ion-color-shade: var(--ion-color-de-york);
    }
}
.view-forms-page .common-document-head-title,
.common-document-header .common-document-head-title {
    margin-top: unset !important;
}

.mail-inbox-head #show-filter {
    right: 3px;
}

app-sites .site-header {
    background-color: white;
}

.ios.remove-display-flex {
    display: unset;
}

.item-3_5rem {
    --min-height: 3.5rem !important;
    --max-height: 3.5rem !important;
}
.file-thumbnail {
    width: 50px;
}
ion-textarea {
    --highlight-color: unset !important;
}
ion-input {
    --highlight-color-focused: var(--ion-color-danger, #eb445a) !important;
    --highlight-color-invalid: var(--ion-color-danger, #eb445a) !important;
    --highlight-color-valid: var(--ion-color-de-york) !important;
}
.show-icon-center {
    display: inline-block;
    margin: 6px auto 0px auto;
    text-align: center;
}
.show-download-options {
    font-size: 28px;
    margin-left: 16px;
    vertical-align: super;
    cursor: pointer;
}
.image-download-icon-position {
    position: relative;
    top: 55%;
}

.image-download-max-width {
    max-width: 65%;
}
.hide-download-message-info{
    .show-download-options {
        display: none;
    }
}
.stacked-input-padding {
    --padding-top: 16px;
    ion-row {
        padding-bottom: 5px;
        padding-left: 16px;
        padding-right: 16px;
    }
}
.readonly-input {
    opacity: 0.7 !important;
    pointer-events: none;
}
.document-auto-sign {
    .alert-button-group{
        padding: 0;
        .alert-cancel{
            border-right: 1px solid #e2e2e2 !important;
        }
    }
    .warn-btn {
        padding: 5px 0 !important;
    }
}
.f-italic {
    font-style: italic;
}

img.emojioneemoji {
    width: 20px;
    height: 20px;
    vertical-align: middle;
    margin: -0.2ex 0.15em 0.2ex;
}