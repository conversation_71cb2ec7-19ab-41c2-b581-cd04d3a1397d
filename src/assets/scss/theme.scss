$themes: (
    citus-health: (logo: '#{$org-path}/citus-health/images/ch-logo.png',
        topAdjust: 90px,
        headerBackgroundColor: $citus-header-color,
        headerBackgroundImage:'#{$org-path}/citus-health/images/ch-header-bottom-bg.png'),
    wellsky: (logo: '#{$org-path}/wellsky/images/ws-logo.png',
        topAdjust: 70px,
        headerBackgroundColor: $citus-header-color,
        headerBackgroundImage: '#{$org-path}/wellsky/images/ws-header-bottom-bg.png'),
    myardon: (logo: '#{$org-path}/myardon/images/ch-logo.png',
        headerBackgroundImage: '#{$org-path}/myardon/images/ch-header-bottom-bg.png'),
    matrixcare: (logo: '#{$org-path}/matrixcare/images/ch-logo.png',
        headerBackgroundImage: '#{$org-path}/matrixcare/images/ch-header-bottom-bg.png'),
    vitalcare: (logo: '#{$org-path}/vitalcare/images/ch-logo.png',
        headerBackgroundImage: '#{$org-path}/vitalcare/images/ch-header-bottom-bg.png'),
    soleoconnect: (logo: '#{$org-path}/soleoconnect/images/ch-logo.png',
        headerBackgroundImage: '#{$org-path}/soleoconnect/images/ch-header-bottom-bg.png'),
    virtisconnect: (logo: '#{$org-path}/virtisconnect/images/ch-logo.png',
        headerBackgroundImage: '#{$org-path}/virtisconnect/images/ch-header-bottom-bg.png'),
    trustport: (logo: '#{$org-path}/trustport/images/ch-logo.png',
        headerBackgroundImage: '#{$org-path}/trustport/images/ch-header-bottom-bg.png'),
    kabafusion: (logo: '#{$org-path}/kabafusion/images/ch-logo.png',
        headerBackgroundImage: '#{$org-path}/kabafusion/images/ch-header-bottom-bg.png'),
    myInhouseconnect: (logo: '#{$org-path}/myInhouseconnect/images/ch-logo.png',
        headerBackgroundImage: '#{$org-path}/myInhouseconnect/images/ch-header-bottom-bg.png'),
    intermountain: (logo: '#{$org-path}/intermountain/images/ch-logo.png',
        headerBackgroundImage: '#{$org-path}/intermountain/images/ch-header-bottom-bg.png'),
    rxconnect: (logo: '#{$org-path}/rxconnect/images/ch-logo.png',
        headerBackgroundImage: '#{$org-path}/rxconnect/images/ch-header-bottom-bg.png'),
    ameripharma: (logo: '#{$org-path}/ameripharma/images/ch-logo.png',
        headerBackgroundImage: '#{$org-path}/ameripharma/images/ch-header-bottom-bg.png'),
    kennebecpharmacy: (logo: '#{$org-path}/kennebecpharmacy/images/ch-logo.png',
        headerBackgroundImage: '#{$org-path}/kennebecpharmacy/images/ch-header-bottom-bg.png'),
    realocares: (logo: '#{$org-path}/realocares/images/ch-logo.png',
        headerBackgroundImage: '#{$org-path}/realocares/images/ch-header-bottom-bg.png'),
    orsinipharmacy: (logo: '#{$org-path}/orsinipharmacy/images/ch-logo.png',
        headerBackgroundImage: '#{$org-path}/orsinipharmacy/images/ch-header-bottom-bg.png'),
    premierinfusion: (logo: '#{$org-path}/premierinfusion/images/ch-logo.png',
        headerBackgroundImage: '#{$org-path}/premierinfusion/images/ch-header-bottom-bg.png'),          
    myanovo: (logo: '#{$org-path}/myanovo/images/ch-logo.png',
        headerBackgroundImage: '#{$org-path}/myanovo/images/ch-header-bottom-bg.png'),          
    nutrishare: (logo: '#{$org-path}/nutrishare/images/ch-logo.png',
        headerBackgroundImage: '#{$org-path}/nutrishare/images/ch-header-bottom-bg.png')          
);

@mixin themify($themes) {

    @each $theme,
    $map in $themes {
        .theme-#{$theme} & {
            $theme-map: () !global;

            @each $key,
            $submap in $map {
                $value: map-get(map-get($themes, $theme), '#{$key}');
                $theme-map: map-merge($theme-map, ($key: $value)) !global;
            }

            @content;
            $theme-map: null !global;
        }
    }
}

@function themed($key) {
    @return map-get($theme-map, $key);
}