@import "variables.scss";
@import "theme.scss";
@import "icons.scss";
@import url("../src/theme/vitalcare/variables.scss");
@import url("../src/theme/matrixcare/variables.scss");
@import url("../src/theme/soleoconnect/variables.scss");
@import url("../src/theme/virtisconnect/variables.scss");
@import url("../src/theme/trustport/variables.scss");
@import url("../src/theme/myardon/variables.scss");
@import url("../src/theme/myInhouseconnect/variables.scss");
@import url("../src/theme/kabafusion/variables.scss");
@import url("../src/theme/intermountain/variables.scss");
@import url("../src/theme/rxconnect/variables.scss");
@import url("../src/theme/ameripharma/variables.scss");
@import url("../src/theme/kennebecpharmacy/variables.scss");
@import url("../src/theme/realocares/variables.scss");
@import url("../src/theme/orsinipharmacy/variables.scss");
@import url("../src/theme/premierinfusion/variables.scss");
@import url("../src/theme/myanovo/variables.scss");
@import url("../src/theme/nutrishare/variables.scss");

.login-top-logo {
    @include themify($themes) {
        top: themed("topAdjust");
        background-image: url(themed("logo"));
    }
}

.common-header {
    background-color: var(--ion-color-skin-main);
}

.common-header:after {
    @include themify($themes) {
        background: url(themed("headerBackgroundImage")) no-repeat top left;
    }
}

.action-sheet-button-inner {
    display: flex;
    justify-content: center;
    color: var(--ion-color-action-sheet-button);
    font-size: 16px;
    text-align: center;
}

.action-sheet-title {
    display: flex;
    justify-content: center;
    color: var(--ion-color-skin-main) !important;
    font-size: 18px !important;
    background: #f4f4f4;
}

.action-sheet-group {
    margin-bottom: 8px;
    border-radius: 6px;
    overflow: hidden;
}

.action-sheet-group .action-sheet-button {
    border-color: #b5dbdb;
    color: #349b99;
    border-bottom: solid 1px #b5dbdb;
}
.delete-message {
    --ion-color-action-sheet-button: #eb445a;
}
.delete-message-disabled {
    --ion-color-action-sheet-button: gray;
    opacity: 0.5;
}
.event-popover::part(content) {
    min-width: fit-content;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
}

.event-details-popover::part(content) {
    justify-content: center;

    @media (min-width: 320px) {
        min-width: 85% !important;
    }

    // Medium devices (tablets, 768px and up)
    @media (min-width: 768px) {
        min-width: 40% !important;
    }

    // Large devices (desktops, 992px and up)
    @media (min-width: 992px) {
        min-width: 40% !important;
    }

    // Extra large devices (large desktops, 1200px and up)
    @media (min-width: 1200px) {
        min-width: 40% !important;
    }
}

.event-popover-custom::part(content) {
    min-width: 500px !important;

    @media (max-width: 500px) {
        left: 0px !important;
        min-width: 100% !important;
    }
}

.setAccordionIconColor {
    .ion-accordion-toggle-icon {
        color: #ffffff;
        font-size: 1.2rem;
        margin-right: 2px;
    }
}

.custom-modal {
    --height: 80%;
    --width: 85%;
}

.custom-alert .alert-button-group.sc-ion-alert-ios {
    flex-wrap: nowrap;
    flex-direction: row;
}

.availability-alert .alert-button-group.sc-ion-alert-ios {
    flex-wrap: nowrap;
    flex-direction: row;
}

.availability-alert {
    button.alert-button:nth-child(1) {
        color: #ffffff;
        background-color: rgba(214, 130, 47, 1);
    }

    button.alert-button:nth-child(2) {
        color: #ffffff;
        background-color: #0190fe;
    }

    button.alert-button:nth-child(3) {
        color: #ffffff;
        background-color: #acb7bf;
    }
}

.availability-alert .alert-button-group.sc-ion-alert-ios {
    width: 67%;
}

.custom-alert .alert-button-group.sc-ion-alert-ios {
    width: 67%;
}

.custom-alert {
    button.alert-button:nth-child(1) {
        color: #35abdd;
        font-weight: bold;
    }

    button.alert-button:nth-child(2) {
        color: #999999;
    }

    button.alert-button:nth-child(3) {
        color: #999999;
        font-weight: normal;
    }
}

.visit-alert {
    button.alert-button:nth-child(1) {
        color: #999999;
    }

    button.alert-button:nth-child(2) {
        color: #35abdd;
    }
}

.delete-alert {
    button.alert-button:nth-child(2) {
        color: #999999;
        font-weight: normal;
    }

    button.alert-button:nth-child(1) {
        color: #35abdd;
        font-weight: bold;
    }
}

.delete-modal {
    .cancel {
        --color: #999999;
        font-weight: normal;
    }

    .ok {
        --color: #35abdd;
        font-weight: bold;
    }
}

.deleted-visit-alert {
    font-size: 14px;
    font-weight: bold;

    button.alert-button.sc-ion-alert-ios {
        margin-top: 10px;
    }

    .alert-message.sc-ion-alert-ios {
        margin-top: 12px;
    }
}

.session-timeout-popup {
    // --width: 300px;
    // Small devices (landscape phones, 576px and up)
    @media (min-width: 320px) {
        --width: 300px;
    }

    // Medium devices (tablets, 768px and up)
    @media (min-width: 768px) {
        --width: 500px;
    }

    // Large devices (desktops, 992px and up)
    @media (min-width: 992px) {
        --width: 600px;
    }

    // Extra large devices (large desktops, 1200px and up)
    @media (min-width: 1200px) {
        --width: 800px;
    }
}

#video-render {
    width: 100%;
    height: 100%;
}

.vidyo-rendering-container {
    .media-grid.picture-in-picture {
        .video-container.remote-track {
            top: 0px;
            left: 0px;
        }

        .video-container.local-track {
            bottom: 100% !important;
        }
    }

    .media-grid.video-grid {
        .video-container.displayCropped:not(.video-muted) {
            top: unset;
            left: 0px;
        }
    }
}

.read-blue-popover {
    --background: var(--ion-color-skin-main);

    &::part(content) {
        min-width: fit-content;
    }
}

.text-center {
    text-align: center;
}

.no-items {
    color: var(--ion-color-text-light);
    margin: 20px;
}

.ios.ios-video-call-set-background {
    background: transparent;
    --background: none;
}

.select-option-alert {
    .alert-title {
        text-align: center;
    }

    [aria-checked="true"].sc-ion-alert-md .alert-radio-icon.sc-ion-alert-md {
        border-color: var(--ion-color-skin-main);

        .alert-radio-inner.sc-ion-alert-md {
            background-color: var(--ion-color-skin-main)
        }
    }

    .alert-button-group {
        justify-content: space-evenly;

        .alert-button.sc-ion-alert-md {
            color: var(--ion-color-skin-main);
        }
    }
}

.validation-error-border {
    border: 1px solid var(--ion-color-validation-error);
}

.validation-error-text {
    font-size: 12px;
    color: var(--ion-color-validation-error);
    margin: 3px;
}

.country-card-popover {
    --height: 300px;
    --width: 250px;
}

ion-datetime {
    color: black;
}

.recipient-list {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 0;
    border-bottom: solid 1px #e2e2e2;
    color: #555555;

    .recipient-parent {
        display: flex;
        flex-direction: row;
        align-content: stretch;
        justify-content: space-between;
        align-items: center;
        border-bottom: none;
        flex-wrap: wrap;
    }

    .recipient-label-wrap {
        white-space: normal;
        word-break: break-word;
    }

    .recipient-child {
        display: flex;
        flex-direction: row;
        align-content: stretch;
        justify-content: space-between;
        align-items: center;
        border-bottom: none;
        flex-wrap: wrap;
        --background: #f2efefcf;

        ion-label {
            margin-left: 27px;
        }
    }
}

.more-palette-button {
    &::part(content) {
        position: absolute;
        bottom: 100px;
        right: 15px;
        left: unset;
        background-color: initial;
        border-radius: unset;
        top: unset;
    }
}
.break-spaces {
    white-space: break-spaces;
}

.dark-text {
    color: var(--ion-color-text-dark);
    font-size: 15px;
}
.users-list {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    color: #555555;
    border-color: #e2e2e2;
    text-align: left;
    font-size: 14px;
}

.disabled-user {
    color: #b8beca !important;
}
.disabled-user-checkbox {
    padding-left: 34px;
}
.chat-with-content span {
    display: flex;
    justify-content: center;
    align-items: center;
}

.grid-icon .grid-icon-background {
    filter: brightness(0) saturate(100%) invert(70%) sepia(49%) saturate(524%) hue-rotate(146deg) brightness(86%)
        contrast(94%);
}

.tag-label {
    display: inline-block;
    width: auto;
    height: 20px;
    background-color: rgb(136, 155, 160);
    -webkit-border-radius: 3px 4px 4px 3px;
    -moz-border-radius: 3px 4px 4px 3px;
    border-radius: 3px 4px 4px 3px;
    border-left: 1px solid rgb(136, 155, 160);
    border-right-color: rgb(136, 155, 160);
    margin-left: 10px;
    position: relative;
    color: white;
    font-weight: 300;
    font-size: 12px;
    line-height: 20px;
    padding: 0 10px 0 10px;
    margin-top: 5px;
    span.shorter {
        max-width: 70px;
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

/* Makes the triangle */
.tag-label:before {
    right: 100%;
    top: 0;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
    border-right-color: inherit;
    border-width: 10px;
    margin-top: 0;
}
.alert-danger-custom {
    .alert-message {
        color: #eb445a;
    }
}
.alert-outline.danger,
.alert-fill.danger,
.priority-bg.ion-color-danger,
.at-fill.danger,
.at-outline.danger {
    background-color: #eb445a;
}

.arrowdown-outline.primary,
.arrowdown-fill.primary,
.priority-bg.ion-color-primary {
    background-color: #3880ff;
}

.chat-outline.primary{
    background-color: #ffffff;
}

.hide-important {
    display: none !important;
}

.priority-text.ion-color-danger {
    color: #eb445a;
}

.priority-text.ion-color-primary {
    color: #3880ff;
}

.mail-inbox-head {
    .alert-fill.bg-default,
    .alert-fill.danger,
    .arrowdown-fill.primary {
        margin-right: 1.3rem;
        margin-left: 0.2rem;
    }

    [class*="ion-flag"] {
        margin-right: 1rem;
        margin-left: 0.2rem;
    }
}
.custom-calendar-class {
    ion-toolbar {
        --background: var(--ion-color-skin-main) !important;
        ul {
            color: #ffffff !important;
        }
    }
    .ion-color-light {
        --ion-color-base: var(--ion-color-skin-main) !important;
        --ion-color-contrast: #ffffff !important;
    }
    .days {
        .today {
            p {
                color: var(--ion-color-skin-main) !important;
            }
        }
        .on-selected {
            background-color: var(--ion-color-skin-main) !important;
            p {
                color: #ffffff !important;
            }
        }
    }
    .between,
    .endSelection {
        .days-btn {
            p {
                color: #ffffff !important;
            }
        }
    }
    .startSelection:before,
    .between .days-btn,
    .endSelection:before,
    .endSelection .days-btn {
        background-color: var(--ion-color-skin-main) !important;
    }
}
.custom-class-date-filter {
    --width: 250px;
}
.site-header-padding {
    padding-top: 16px;
}
// https://github.com/ionic-team/ionic-framework/blob/6ffbdbb3b2b69290cf25753d535bc7483bd7c6e8/BREAKING.md#hidden-attribute
[hidden] {
    display: none !important;
}
.flag-image {
    width: 2em;
    line-height: 2em;
}
.inner-icon {
    font-size: 24px;
}
.flag-spacing {
    margin-top: 1px;
    margin-right: 5px;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-end;
    align-items: center;
}
.icon-margin-right {
    margin-right: 5px;
}
.remove-start-padding-for-mail-mobile {
    padding-inline-start: 0;
    .info-icon-mail-mobile {
        font-size: 26px;
        vertical-align: middle;
    }
}
  .barlow-regular {
    font-family: "Barlow", sans-serif;
    font-weight: 400;
    font-style: normal;
  }

  .red-badge {
    margin-left: -26px;
    font-size: 21px;
  }
  .profile-page-badge {
    position: absolute;
    bottom: 6px;
    margin: unset;
    right: 15px;
    font-size: 27px;
  }
  .chat-list-red-badge {
    margin-left: -21px;
    font-size: 19px;
    position: absolute;
    bottom: 21px;
  }

  .chatroom-circle-badge {
    font-size: 20px !important;
    padding-left: 3px;
    position: absolute;
    bottom: 10px;
    left: 48px;
  }
  .circle-badge-big .chatroom-circle-badge{
    bottom: 27px;
    left: 73px;
    font-size: 25px !important;
  }
  .cancel-reason-alert .alert-wrapper .alert-head, .cancel-reason-alert .alert-wrapper .alert-message {
  text-align: center;
  }
 .chatroom-list-circle-badge {
    left: 26px;
    font-size: 15px;
    position: absolute;
    top: 30px;
 } 
 .chatroom-list-right-circle-badge {
    font-size: 15px;
    position: absolute;
    bottom: 0;
    right: 16px;
 } 
 .schdule-center-avatar-position{
    left: 30px;
    font-size: 15px;
    position: absolute;
    bottom: 0;
 }