import { theme } from 'src/theme/theme';

export class ConfigValues {
  // TODO: Need to remove unwanted configs.
  static appName = 'CitusHealth';
  static tenantName = 'Homeinfusion';
  static defaultRedirectURI = 'citushealth://sso';
  static bundleIdentifier = theme.bundleIdentifier;
  static tenantId = 1;
  static appVersion = '5.88.0-20250726001'; // Format: Major.Minor.Patch-YYYYMMDDXXX (XXX - Build count released on the same date)
  static platformDependentValue = {
    android: {
      cordovaStoragePath: 'externalRootDirectory',
      filePath: 'callbell/document/'
    },
    ios: {
      cordovaStoragePath: 'documentsDirectory'
    }
  };
  static telemetryAgentApiKey = 'e2ea01a6-71c3-3664-96b2-08e60be64e26';
  static messages = {
    consolo: {
      autoLinkRecipientError: 'Recipient details not Given',
      autoLinkAssociateError: 'Associate Pateint details not Given',
      autoLinkSelegateError: 'Delegate Staff details not Given',
      autoLinkErrorMessage: 'Some Datas missing please choose',
      idpserverInvalidUser: 'Invalid user token',
      fileDetailsError: 'File details missing.....',
      consoloParamsMissing: 'Barnch params missing',
      idpServerError: 'Server have some issues.',
      cancelconsoloDocumnetSendMessage: 'Please confirm whether you want to cancel the signature request ?',
      backToinboxFromSignatureMessage: 'Are you sure want to cancel process ?',
      consoloLoginCheckingMessage: 'Your request has been processing.. Please wait..'
    },
    configurations: {
      clinicianSignupIdentifier: 'staff-',
      webPushAutoCloseAfterSecconds: 10,
      stashboardSSLKey: 'https',
      allowedFileFormat: [
        'pdf',
        'PDF',
        'DOC',
        'doc',
        'DOCX',
        'docx',
        'word',
        'WORD',
        'xl',
        'XL',
        'xls',
        'XLS',
        'xlsx',
        'XLSX',
        'odt',
        'jpg',
        'JPG',
        'jpeg',
        'JPEG',
        'png',
        'PNG'
      ],
      allowedFileSizeForSignatureRequest: 10,
      fileFormatNotsupported: 'Document is not supported',
      fileSizeError: 'Documnet size is high',
      defaultFileExtension: '.png',
      virtualPatientSaveError: "We can't save your data, Please try again",
      virtualPatientPopupTitle: 'Virtual Patient',
      associatePatientPopup: true,
      virtualPatientAllreadyExist: 'Patient already exists. Do you want to associate with it?',
      confirmationForChatFileRemove: true,
      checkboxGroupPalatteLimitFromModal: 10,
      videoCallRingWaitingTime: 60000, // 1 min.
      videoToken: '',
      videoHost: '12ba5e.vidyo.io',
      pushstaticient: {
        dev: {
          android: {
            senderID: '546677004766',
            staticiantID: '7de301ec-8cbe-4915-a24c-11d6d5e82ce6',
            staticiantSecret: 'b8915eb5-8f49-49ea-9090-7408af0c1016'
          },
          ios: {
            staticiantID: 'd7395a62-fe62-4de8-a6ea-d24e6b9b9ad4',
            staticiantSecret: '991e3de6-0b90-4bce-a84f-56fb1e65c830'
          }
        },
        prod: {
          android: {
            senderID: '546677004766',
            staticiantID: '592974be-41e6-416a-97e5-63745501a504',
            staticiantSecret: '125e0423-9ca8-490d-802e-81697c9439dd'
          },
          ios: {
            staticiantID: '0980e48d-b78c-4a9a-968d-2c43b63c9384',
            staticiantSecret: '7116a1ef-ffdc-4ee7-a82e-ad46ac79ef8c'
          }
        }
      },
      signatureDocumentPalette: {
        width: 100,
        height: 30,
        signwidth: 150,
        signheight: 30,
        minWidth: 30,
        minHeight: 8,
        maxWidthForText: 600,
        maxHeightForText: 150,
        checkboxHeight: 45,
        checkBoxLabelWithRedeuse: 31,
        checkBoxLabelHeightRedeuse: 5,
        leftAlignmentAfterFillData: 25,
        setFitHeightWidthForCheckbox: 72.7,
        resizeMaxHeightForText: 350
      }
    },
    virtualPatientAllreadyExistEmail: 'Email Id is already exists for another user',
    authenticationFailed: 'Invalid Email/Password',
    reroutingMessage: 'Forwarding message...',
    messageRerouted: 'Message forwarded successfully',
    messageReroutedExist: 'Member already exists in this chat session',
    serverError: "Can't connect to server",
    caseClosed: 'Case closed successfully',
    sessionOut: 'Your session has expired due to inactivity',
    sessionDuplicated: 'Your session has expired due to duplicate login in another device',
    passwordIsShort: 'Password should contain atleast 8 characters',
    blankFieldExists: 'Please fill all the fields',
    blankRequiredFieldExists: 'Please fill all required fields',
    passwordMismatch: 'Password mismatch',
    registrationSuccess: 'Registration successful.',
    registrationSuccessProceed: ' Please login to your account.',
    registrationApprovalPending: ' Please wait until the administrator approves your account.',
    blankUsername: 'Please enter a username',
    blankPassword: 'Please enter password',
    pendingAccountActivation: 'Please contact administrator to activate this account.',
    connectionLost: 'Internet connection lost!',
    deletedMessageGroup: 'Message Group deleted successfully',
    deletedDiscussionGroup: 'Discussion Group deleted successfully',
    groupMemberExists: 'Member already exists',
    groupNameIsShort: 'Group Name should contain atleast 4 characters',
    messageGroupSaved: 'Message Group saved successfully',
    discussionGroupSaved: 'Discussion Group saved successfully',
    deletedUser: 'User deleted successfully',
    userDeletionFailed: 'User deletion failed!',
    tenantRoleFieldBlank: 'Please choose a role',
    tenantRoleIdExists: 'Role already exists',
    emailNotCorrect: 'Incorrect Email',
    phoneNotCorrect: 'Incorrect Phone number',
    emptyEmailId: 'Please enter Email',
    usernameNotCorrect: 'Incorrect Username',
    tenantUpdateSuccess: 'Tenant details saved successfully',
    imageUploadError: "We're sorry you're having trouble uploading an image!",
    emailNotexists: 'This email does not exist in our system. Please check your email and try again.',
    acceptTermsAndConditions: 'Please read and accept the terms and conditions of ' + ConfigValues.appName + ' Inc',
    scheduleSuccess: 'Clinician schedule saved successfully',
    scheduleError: 'Please choose schedule timings',
    nurseUnAvailabilityBioScripInfusion:
      "No clinicians are scheduled at this time. Please contact {{tenantName}} Infusion Services office at <a href='tel:**************'>**************</a> for assistance.",
    nurseUnAvailabilityBioScrip:
      "Please contact the {{tenantName}} Infusion services office at <a href='tel:**************'>**************</a> for assistance",
    nurseUnAvailability: 'No clinicians are scheduled at this time. Please contact {{tenantName}} Infusion Services.',
    cameraUploadFailed: "Couldn't sent the captured picture Please contact administrator",
    feedbackSubmittedSuccess:
      'Thank you for the feedback, your suggestion has been sent to ' +
      ConfigValues.appName +
      "'s Customer Success team. We'll review your input and get back to you with questions or further clarifications.",
    tenantNameExist: 'Tenant name already exists',
    userRoleMissing: 'Your account role was deleted. Please contact Administrator to assign a new role.',
    noCliniciansAvailable: 'No clinicians available',
    touchAgainToExit: 'Touch again to exit',
    unauthorizedRequest: 'Unauthorized request!',
    successSendSignatureDocument: 'Successfully sent the document for digital signature',
    successSendSignedDocument: 'Successfully sent the signed document',
    pleaseConfirmSignature: 'Please confirm your signature',
    accountActivationMessage: '{{statusAction}}  account activation of {{statusChangedUserName}}',
    successPasswordReset: 'Password reset successfull',
    successRxRegistration: 'Password reset successfull',
    failedPasswordReset: 'Password reset failed',
    successRoleRemoved: 'Role removed Successfully',
    successRoleSaved: 'Role saved Successfully ',
    successPrivilegeSettingsSaved: 'Privilege settings successfully saved',
    successPrivilegeSettingsRemoved: 'Privileges removed Successfully',
    successInvitedToChatsession: '{{chatroomUserDisplayname}} has been invited to this chat session',
    alreadyInvitedToChatsession: '{{chatroomUserDisplayname}} already added to this chat session',
    successRegistrationIdValidation: 'Registration ID validated successfully',
    successRegistrationIdValidationClinician: 'Choose appropriate role and signup',
    failedRegistrationIdValidation: "Couldn't validate the registration ID. Please contact Administrator",
    blankRegistrationId: 'Please enter the registration ID provided by the Administrator',
    completedSOCFollowUp: 'Successfully Completed SOC Follow Up',
    completedIVIGAssessment: 'Successfully Completed IVIG Quarterly Assessment',
    invalidDOB: 'Please enter valid DOB',
    invalidInvenory: 'Please enter valid {{manageType}} name',
    invenoryNameAlreadyExists: '{{manageType}} name already exists',
    inventoryDeletedSuccessfully: '{{manageType}} deleted successfully',
    inventoryupdatedSuccessfully: '{{manageType}} updated successfully',
    inventorySavedSuccessfully: '{{manageType}} saved successfully',
    invalidZip: 'Please enter valid Zip code',
    infusionSupportRerouteToInbox: 'Our on call staff has received your message and will get back to you shortly.',
    infusionSupportRerouteToInboxWhenWillMyNurseArrive:
      'We are locating your nurse and will contact you shortly with their arrival time.',
    infusionSupportRerouteToInboxWhereIsMyDelivery:
      'We are locating your delivery and will contact you shortly with the estimated arrival time.',
    infusionSupportWhenWillMyNurseArrive:
      'Send a message to a staff member to confirm when your nurse is scheduled to arrive?',
    infusionSupportWhereIsMyDelivery:
      'Send a message to a staff member to confirm when your delivery is scheduled to arrive?',
    autoTranslateFailed:
      'Translation services are temporarily unavailable. This issue has been reported to the ' +
      ConfigValues.appName +
      ' support team. We apologize for the inconvenience.',
    chatServerUnavailable: 'Chat server is temporarily unavailable. Please try after some time.',
    successBroadcastSent: 'Successfully sent the broadcast message',
    emptyBroadcastType: 'Please choose the recipients',
    emptyBroadcastMessage: 'Please enter a message',
    sendBroadcastAttachmentOnly:
      'Sending an attachment without an associated message is not a good idea. Would you like to send the attachment without a message that accompanies it?',
    avatarDeletedSuccessfully: 'Avatar deleted successfully',
    patientInboxConfirmMessageFirstPart: ' may be off duty at this time. Your message may not be read by ',
    patientInboxConfirmMessageSecondPart: ' until the next business day.',
    patientInboxConfirmMessage:
      '{{clinicianName}} may be off duty at this time. Your message may not be read by {{clinicianName}} until the next business day.',
    avatarFileSizeValidation: 'Please upload a file which has size {{fileSize}} MB or less',
    noInventoryCategory: 'No Inventories available in this category',
    unSignedDoc: 'This document is not yet signed',
    oldSignatureDoc: 'Cannot download old documents',
    dischargedUser: 'User has been discharged',
    enabledWebPush: 'Web push notification enabled successfully',
    inventoryDataEmpty: 'Inventory cannot be blank',
    cannotViewOldSignatureDoc: 'Error in retrieving document as it is outdated',
    patientInboxConfirmationButtonOk: 'Next business day is OK',
    patientInboxConfirmationButtonCancel: 'Get immediate assistance',
    chatLogMessage: 'No logs available in this chat room',
    cmisServerIssue: 'Operation failed due to server issue.',
    cmisFileNotSupport: 'File not supported.',
    messageGroupUserNotConfiguredCmis: 'None of the user has configured Call Bell drive',
    userNotConfiguredCmis: 'User has not yet configured Call Bell drive',
    registerUserInCmisFailed: 'Configuring Call Bell drive for first time use failed',
    registerTenantInCmisFailed: 'Registering Tenant in Call Bell drive Failed',
    uploadFileFromSentFolderToChatroomFail: 'File upload Failed.Please Try again.',
    tagNameAlreadyExists: '{{tagNameExists}} name already exists',
    invalidTag: 'Please enter valid Tag name and select a Tag type',
    taggedSuccessMessage: 'Your message has been tagged and sent to {{approver}} for approval',
    yourMessageIsGettingReviewed: 'Your message is getting reviewed',
    taggedAutoApproveSuccessMessage: 'Your message has been tagged and auto approved',
    pushNotificationForTagApprover: 'You have a new message for approval',
    pushNotificationAfterApprove: 'Message Approved by {{filledBy}}',
    virtualPatientconfirmMessage:
      'We found this patient in our records. Please confirm whether we can link that patient to this discussion group?',
    saveVirtualPatientFirst: 'Please Save Virtual Patient First',
    virtualPatientSavedSuccess: 'Patient details saved successfully',
    virtualPatientSavedFailed: 'Patient details saving is failed, Please contact Administrator!',
    virtualPatientUpdatedSuccess: 'Patient details updated successfully',
    virtualPatientDeletedSuccess: 'Patient details deleted successfully',
    patientDetailsAlreadyExists: 'We found this patient exists already in our records',
    inventoryHelpMessageOnHand: "'On Hand' is the number of items currently available",
    inventoryHelpMessageMore:
      'Now you can enter how many more items are needed in the "Need More?" column. You can enter a number or enter ' +
      "'Yes' or 'No'",
    inventoryHelpMessageOnHandPatientFace: "'On Hand' is the number of inventory items that you have",
    inventoryHelpMessageMorePatientFace:
      'Now you can enter how many more items are needed in the "Need More?" column. You can enter a number or select ' +
      "'Yes' or 'No'",
    noUnreadMessages: 'No unread messages',
    approvedSuccess: '{{name}} approved successfully',
    patientFacingSuccess: 'Thank you for submitting your supply count',
    supplyRequest: 'One supply count is awaiting your response', // Please click here to review it.
    archivedInboxItems: 'Archieved successfully',
    patientInventoryCreation: 'One supply count is pending for submission', // Please click here to submit it.
    inventoryFinalSubmitMessage: 'When inventory submit {{userlist}} will be notified',
    archievedAdminMessage:
      '{{userDetails.displayName}} has archived the message and is no longer part of this conversation.',
    archievedAdminMessageForClinicianOnly:
      " If you need to keep  {{userDetails.displayName}} in this conversation, please choose 'Invite'.",
    scheduleTimeRangeSelection: 'Please choose a valid Time range',
    tagDeletedSuccessfully: 'Tag deleted successfully',
    tagUpdatedSuccessfully: 'Tag updated successfully',
    tagSavedSuccessfully: 'Tag saved successfully',
    scheduleHintTextForNonRecurrence:
      "How would you like the conflict(s) resolved for future 'Non recurring schedules (Day schedules)' ?",
    scheduleHintTextForRecurrence: "How would you like the conflict(s) resolved for 'Recurring' schedules?",
    setEndDateForRecurrence: 'Schedule End Date updated successfully',
    setEndDateForNonRecurrence: 'Day Schedule removed successfully',
    fillablePdfShareUserNotSelect: 'Select one Recipient',
    broadcastLargeFile: 'Sorry for the inconvenience! {{filename}} file size more than 20 mb not supported.',
    broadcastUnsupportedFiles: 'Sorry for the inconvenience! {{filename}} files types are not supported.',
    broadcastExeedFileCount: "Sorry for the inconvenience! More than 10 files can't be uploaded at a time.",
    uploadImageLimitExceed: 'Exceed the maximum limit.',
    icloudFolderSelectionError: 'Folder selection not supported.',
    cancelDocumnetSendMessage: 'Please confirm whether you want to cancel the signature request ?',
    backToinboxFromSignatureMessage: 'Are you sure want to cancel process ?',
    removeCameraImagesMessage: 'Do you want to remove selected file ?',
    icloudRemoveItemNotFoundMessage: 'Item not Found',
    icloudFolderandFileSelectionMessage: 'Folders not support, So removed it',
    documnetpickerPluginResponceError: 'Some internal Issues, Please try again',
    icloudFileLengthErrorMessage: 'Allowed only {{fileLimitCount}} Files.',
    maskedMessageSuccess: 'Masked message sent successfully',
    noActiveTopicOverlayMessage: 'If you need to contact a staff member please press the chat button',
    signatureRequestStartSignModalPatientTittle: 'Please hand the device to patient or representative',
    signatureRequestStartSignModalAssociateTittle:
      'Please hand the device to the person who needs to sign the document',
    confirmSignatureRequestPatient:
      'Thank you for confirming your signature. Please hand the device back to the representative.',
    confirmSignatureRequestAssociate:
      'Thank you for confirming your signature. Please hand the device back to {{userName}}.',
    checkboxMandatoryMessage: 'Do you want to make this checkbox as mandatory?',
    multipleCheckBoxNotAllowed: 'Only one checkbox allowed in a doc',
    removeSelectedChatFileTitle: '',
    removeSelectedChatFileMessage: 'Are you sure you want to remove this attachment?',
    successMasterTenantSaved: 'Master Tenant saved Successfully ',
    masterTenantIdExists: 'Master Tenant already exists',
    successmasterTenantRemoved: 'Master Tenant removed Successfully',
    signatureAreMandatoryMessage: 'Do you want to make this sign field as mandatory?',
    textBoxAreMandatoryMessage: 'Do you want to make this text field as mandatory?',
    multipleTagMessageRemoveConfirmation: 'You are going to remove selected messages from tag',
    inviteConfirmation: 'You are going to invite selected user(s) to this chat session',
    forwardConfirmation: 'You are going to forward this message thread to {{forwardName}}',
    autoLinkRecipientError: 'Recipient details not Given',
    autoLinkAssociateError: 'Associate Pateint details not Given',
    autoLinkSelegateError: 'Delegate Staff details not Given',
    autoLinkErrorMessage: 'Some Datas missing please choose'
  };
  static notificationSoundTune = 'aurora';
  static termsAndConditionsUrl = 'https://www.citushealth.com/hipaa-release-and-terms-of-service';
  static configurableFaqBaseUrl = 'https://devl.cms.citushealth.com/';
  static citusHealthGraphQl = 'http://devl.middleware-gqdss.citushealth.com/graphql';
  static citusHealthSynGraphQl = 'http://devl.middleware-gqdss.citushealth.com/graphiql';
  static citusHealthMiddlewareGraphQl = 'http://devl-gqdss.citushealth.com/graphql';
  static apiFeedbackUrl = 'https://api.citushealth.com/hook/web/generic/v2/notify';
  static assetsUrl = 'http://assets.citushealth.com/c/';
  static pushServerBaseUrl = 'https://test.push-notifier.citushealth.com/';
  static pushServerAPIUrl = 'https://test.api.push-notifier.citushealth.com/';
  static pushDeepLinkCategories = {
    chat: '10',
    inventory: '11',
    userApproval: '12',
    forms: '13'
  };

  static config = {
    appName: ConfigValues.appName,
    tenantName: ConfigValues.tenantName,
    bundleIdentifier: ConfigValues.bundleIdentifier,
    tenantId: ConfigValues.tenantId,
    inboxMessageRefreshInterval: 3, // seconds.
    sessionTimeoutMinutes: 10,
    sessionTimeoutWarningSeconds: 30,
    messages: ConfigValues.messages,
    usernameRegex: '/^[a-zA-Z0-9_@.]*$/',
    version: ConfigValues.appVersion,
    PSPDFKitKey:
      'T+5Z1Y7CLyIHxA9lNxOBmgvEV5Ro9iaCSpb+C7lNgE56Kec65jWEGmpjEbNBrnUjqPOytbM5W2aevLMuuKtpcoipLopvClACqYBGGQAwSnDTJAJy/sdkGv7JEHlo5EKiC+cVK/mmQF1XmUS1k9Qfy32b8pN3KgQux+8vWaHFYNqzOlRG1Q9Y0Ze8ZOFzC6PbQ/gbuubCXQeKErjQ9vL3VpNeYJdBlN7yRTRD0+jJHVsCU7DRu0rYdS+nrneRvw4HY+L9K0CvDg0gEaEE02UyP1H+n6h9EVq6dpqZwKcvR2/1SN0vTqb++aJai8koBScztqao1thf5Hl+7y9SqOEWkVx7xbklqUuvMSt+isVjOGgYCWp9VHHxb98CLEitl6l+a8/OyM4dnszqiN5WJcjhVjrf4vOaU+22BoxPMKKgdvrORhcLdJ2eaeGAGLs2TKyR',
    platform: 'web', // 'android,ios,web'
    platformDependentValue: ConfigValues.platformDependentValue,
    notificationSoundTune: ConfigValues.notificationSoundTune,
    pushDeepLinkCategories: ConfigValues.pushDeepLinkCategories,
    developerMode: {
      isEnabled: false,
      testCase: null
    },
    apiVersion: 'v4',
    scheduleInterval: 30,
    scheduleStartTime: '00:00',
    scheduleEndTime: '24:00',
    scheduleTimeFormat: 12,
    termsAndConditionsUrl: ConfigValues.termsAndConditionsUrl,
    telemetryAgentApiKey: ConfigValues.telemetryAgentApiKey,
    timeZone: 5.5,
    apiFeedbackUrl: ConfigValues.apiFeedbackUrl,
    configurableFaqBaseUrl: ConfigValues.configurableFaqBaseUrl,
    citusHealthGraphQl: ConfigValues.citusHealthGraphQl,
    citusHealthSynGraphQl: ConfigValues.citusHealthSynGraphQl,
    citusHealthMiddlewareGraphQl: ConfigValues.citusHealthMiddlewareGraphQl,
    assetsUrl: ConfigValues.assetsUrl,
    pushServerBaseUrl: ConfigValues.pushServerBaseUrl,
    pushServerAPIUrl: ConfigValues.pushServerAPIUrl
  };
}

export const appThemeConfig = {
  signUpSectionCheck: [
    'vitalcare',
    'matrixcare',
    'soleoconnect',
    'virtisconnect',
    'myInhouseconnect',
    'myardon',
    'intermountain',
    'rxconnect',
    'ameripharma',
    'kennebecpharmacy',
    'realocares',
    'premierinfusion',
    'myanovo',
    'nutrishare'
  ],
  hereToHelpSectionDisplay: [
    'vitalcare',
    'myInhouseconnect',
    'myardon',
    'rxconnect',
    'ameripharma',
    'kennebecpharmacy',
    'premierinfusion',
    'myanovo',
    'nutrishare'
  ],
  medicalEmergencyDisplay: ['soleoconnect'],
  technicalAndPharmacySupport: ['ameripharma'],
  hereToHelpTechnicalSupport: ['myanovo'],
  showEmailUsAt: ['premierinfusion']
};
export const oktaEnabledConfig = {
  enabledApps: ['citus-health', 'matrixcare']
};
