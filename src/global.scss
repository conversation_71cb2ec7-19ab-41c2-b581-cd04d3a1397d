/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "~@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "~@ionic/angular/css/normalize.css";
@import "~@ionic/angular/css/structure.css";
@import "~@ionic/angular/css/typography.css";
@import '~@ionic/angular/css/display.css';

/* Optional CSS utils that can be commented out */
@import "~@ionic/angular/css/padding.css";
@import "~@ionic/angular/css/float-elements.css";
@import "~@ionic/angular/css/text-alignment.css";
@import "~@ionic/angular/css/text-transformation.css";
@import "~@ionic/angular/css/flex-utils.css";

/* Project syles */
@import "assets/scss/common.scss";
/* Tenant based theme*/
@import "assets/scss/custom.scss";

@import "theme/vitalcare/custom.scss";
@import "theme/matrixcare/custom.scss";
@import "theme/soleoconnect/custom.scss";
@import "theme/virtisconnect/custom.scss";
@import "theme/trustport/custom.scss";
@import "theme/myardon/custom.scss";
@import "theme/kabafusion/custom.scss";
@import "theme/myInhouseconnect/custom.scss";
@import "theme/intermountain/custom.scss";
@import "theme/rxconnect/custom.scss";
@import "theme/ameripharma/custom.scss";
@import "theme/kennebecpharmacy/custom.scss";
@import "theme/realocares/custom.scss";
@import "theme/orsinipharmacy/custom.scss";
@import "theme/premierinfusion/custom.scss";
@import "theme/myanovo/custom.scss";
@import "theme/nutrishare/custom.scss";
@import "theme/citushealth/custom.scss";