// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 60;
	objects = {

/* Begin PBXBuildFile section */
		142D17D7682287115D7E553A /* Pods_App.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 42A14D3BA95DA286AE71DCD3 /* Pods_App.framework */; };
		2FAD9763203C412B000D30F8 /* config.xml in Resources */ = {isa = PBXBuildFile; fileRef = 2FAD9762203C412B000D30F8 /* config.xml */; };
		41B735BE290FB7E800EE39CE /* AVKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 41B735BD290FB7E800EE39CE /* AVKit.framework */; };
		41B735C0290FB7FD00EE39CE /* Photos.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 41B735BF290FB7FD00EE39CE /* Photos.framework */; };
		41B735CA290FB86F00EE39CE /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 41B735C9290FB86F00EE39CE /* UIKit.framework */; };
		41B735CC290FB88100EE39CE /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 41B735CB290FB88100EE39CE /* AudioToolbox.framework */; };
		41B735CE290FB88700EE39CE /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 41B735CD290FB88700EE39CE /* AVFoundation.framework */; };
		41B735D0290FB89200EE39CE /* CoreLocation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 41B735CF290FB89200EE39CE /* CoreLocation.framework */; };
		41B735D2290FB89800EE39CE /* CoreMedia.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 41B735D1290FB89800EE39CE /* CoreMedia.framework */; };
		41B735D4290FB8A200EE39CE /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 41B735D3290FB8A200EE39CE /* SystemConfiguration.framework */; };
		41B735DC290FB8C500EE39CE /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 41B735D6290FB8C500EE39CE /* GoogleService-Info.plist */; };
		41B735DD290FB8C500EE39CE /* VidyoPlatformPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = 41B735D8290FB8C500EE39CE /* VidyoPlatformPlugin.swift */; };
		41B735DE290FB8C500EE39CE /* IPluginEventHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 41B735D9290FB8C500EE39CE /* IPluginEventHandler.swift */; };
		41B735DF290FB8C500EE39CE /* VidyoClientWrapper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 41B735DA290FB8C500EE39CE /* VidyoClientWrapper.swift */; };
		41B735E0290FB8C500EE39CE /* VidyoPlatfromPlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = 41B735DB290FB8C500EE39CE /* VidyoPlatfromPlugin.m */; };
		41C4287D295AE7F300734363 /* busy.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 41C42872295AE7F300734363 /* busy.mp3 */; };
		41C4287E295AE7F300734363 /* default.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 41C42873295AE7F300734363 /* default.mp3 */; };
		41C4287F295AE7F300734363 /* calltone.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 41C42874295AE7F300734363 /* calltone.mp3 */; };
		41C42881295AE7F300734363 /* nouser.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 41C42876295AE7F300734363 /* nouser.mp3 */; };
		41C42885295AE7F300734363 /* videocall.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 41C4287A295AE7F300734363 /* videocall.mp3 */; };
		50379B232058CBB4000EE86E /* capacitor.config.json in Resources */ = {isa = PBXBuildFile; fileRef = 50379B222058CBB4000EE86E /* capacitor.config.json */; };
		504EC3081FED79650016851F /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 504EC3071FED79650016851F /* AppDelegate.swift */; };
		504EC30D1FED79650016851F /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 504EC30B1FED79650016851F /* Main.storyboard */; };
		504EC30F1FED79650016851F /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 504EC30E1FED79650016851F /* Assets.xcassets */; };
		504EC3121FED79650016851F /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 504EC3101FED79650016851F /* LaunchScreen.storyboard */; };
		50B271D11FEDC1A000F3C39B /* public in Resources */ = {isa = PBXBuildFile; fileRef = 50B271D01FEDC1A000F3C39B /* public */; };
		6099C71B23A747CDBD88CD8D /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = EF24D9522E1F4D808DB9712C /* PrivacyInfo.xcprivacy */; };
		C6E3F4032ABC0C5300F4504A /* tritone.caf in Resources */ = {isa = PBXBuildFile; fileRef = C6E3F3FD2ABC0C5200F4504A /* tritone.caf */; };
		C6E3F4042ABC0C5300F4504A /* glass.caf in Resources */ = {isa = PBXBuildFile; fileRef = C6E3F3FE2ABC0C5200F4504A /* glass.caf */; };
		C6E3F4052ABC0C5300F4504A /* chime.caf in Resources */ = {isa = PBXBuildFile; fileRef = C6E3F3FF2ABC0C5300F4504A /* chime.caf */; };
		C6E3F4062ABC0C5300F4504A /* ring.caf in Resources */ = {isa = PBXBuildFile; fileRef = C6E3F4002ABC0C5300F4504A /* ring.caf */; };
		C6E3F4072ABC0C5300F4504A /* aurora.caf in Resources */ = {isa = PBXBuildFile; fileRef = C6E3F4012ABC0C5300F4504A /* aurora.caf */; };
		C6E3F4082ABC0C5300F4504A /* bell.caf in Resources */ = {isa = PBXBuildFile; fileRef = C6E3F4022ABC0C5300F4504A /* bell.caf */; };
		CB50D6F12CF0960A003E6998 /* VidyoClientIOS.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 41B735C5290FB86300EE39CE /* VidyoClientIOS.xcframework */; };
		CB50D6F62CF09631003E6998 /* VidyoClientIOS.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 41B735C1290FB85900EE39CE /* VidyoClientIOS.xcframework */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		2CC0DDFE5E38B4DBB4F70C66 /* Pods-App.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-App.debug.xcconfig"; path = "Target Support Files/Pods-App/Pods-App.debug.xcconfig"; sourceTree = "<group>"; };
		2FAD9762203C412B000D30F8 /* config.xml */ = {isa = PBXFileReference; lastKnownFileType = text.xml; path = config.xml; sourceTree = "<group>"; };
		3398852C0038A832975A555C /* Pods-App.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-App.release.xcconfig"; path = "Target Support Files/Pods-App/Pods-App.release.xcconfig"; sourceTree = "<group>"; };
		41B735BD290FB7E800EE39CE /* AVKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVKit.framework; path = System/Library/Frameworks/AVKit.framework; sourceTree = SDKROOT; };
		41B735BF290FB7FD00EE39CE /* Photos.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Photos.framework; path = System/Library/Frameworks/Photos.framework; sourceTree = SDKROOT; };
		41B735C1290FB85900EE39CE /* VidyoClientIOS.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = VidyoClientIOS.xcframework; sourceTree = "<group>"; };
		41B735C5290FB86300EE39CE /* VidyoClientIOS.xcframework */ = {isa = PBXFileReference; expectedSignature = "AppleDeveloperProgram:6888NB32SR:Vidyo, Inc."; lastKnownFileType = wrapper.xcframework; path = VidyoClientIOS.xcframework; sourceTree = "<group>"; };
		41B735C9290FB86F00EE39CE /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = System/Library/Frameworks/UIKit.framework; sourceTree = SDKROOT; };
		41B735CB290FB88100EE39CE /* AudioToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioToolbox.framework; path = System/Library/Frameworks/AudioToolbox.framework; sourceTree = SDKROOT; };
		41B735CD290FB88700EE39CE /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = System/Library/Frameworks/AVFoundation.framework; sourceTree = SDKROOT; };
		41B735CF290FB89200EE39CE /* CoreLocation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreLocation.framework; path = System/Library/Frameworks/CoreLocation.framework; sourceTree = SDKROOT; };
		41B735D1290FB89800EE39CE /* CoreMedia.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMedia.framework; path = System/Library/Frameworks/CoreMedia.framework; sourceTree = SDKROOT; };
		41B735D3290FB8A200EE39CE /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = System/Library/Frameworks/SystemConfiguration.framework; sourceTree = SDKROOT; };
		41B735D5290FB8C400EE39CE /* App-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "App-Bridging-Header.h"; sourceTree = "<group>"; };
		41B735D6290FB8C500EE39CE /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		41B735D7290FB8C500EE39CE /* App-Bridging-Header.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "App-Bridging-Header.h"; sourceTree = "<group>"; };
		41B735D8290FB8C500EE39CE /* VidyoPlatformPlugin.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = VidyoPlatformPlugin.swift; sourceTree = "<group>"; };
		41B735D9290FB8C500EE39CE /* IPluginEventHandler.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = IPluginEventHandler.swift; sourceTree = "<group>"; };
		41B735DA290FB8C500EE39CE /* VidyoClientWrapper.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = VidyoClientWrapper.swift; sourceTree = "<group>"; };
		41B735DB290FB8C500EE39CE /* VidyoPlatfromPlugin.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VidyoPlatfromPlugin.m; sourceTree = "<group>"; };
		41C42872295AE7F300734363 /* busy.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = busy.mp3; sourceTree = "<group>"; };
		41C42873295AE7F300734363 /* default.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = default.mp3; sourceTree = "<group>"; };
		41C42874295AE7F300734363 /* calltone.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = calltone.mp3; sourceTree = "<group>"; };
		41C42876295AE7F300734363 /* nouser.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = nouser.mp3; sourceTree = "<group>"; };
		41C4287A295AE7F300734363 /* videocall.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = videocall.mp3; sourceTree = "<group>"; };
		41C80BE4290FD66300FE6DA0 /* App.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = App.entitlements; sourceTree = "<group>"; };
		42A14D3BA95DA286AE71DCD3 /* Pods_App.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_App.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		50379B222058CBB4000EE86E /* capacitor.config.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = capacitor.config.json; sourceTree = "<group>"; };
		504EC3041FED79650016851F /* App.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = App.app; sourceTree = BUILT_PRODUCTS_DIR; };
		504EC3071FED79650016851F /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		504EC30C1FED79650016851F /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		504EC30E1FED79650016851F /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		504EC3111FED79650016851F /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		504EC3131FED79650016851F /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		50B271D01FEDC1A000F3C39B /* public */ = {isa = PBXFileReference; lastKnownFileType = folder; path = public; sourceTree = "<group>"; };
		C6E3F3FD2ABC0C5200F4504A /* tritone.caf */ = {isa = PBXFileReference; lastKnownFileType = file; path = tritone.caf; sourceTree = "<group>"; };
		C6E3F3FE2ABC0C5200F4504A /* glass.caf */ = {isa = PBXFileReference; lastKnownFileType = file; path = glass.caf; sourceTree = "<group>"; };
		C6E3F3FF2ABC0C5300F4504A /* chime.caf */ = {isa = PBXFileReference; lastKnownFileType = file; path = chime.caf; sourceTree = "<group>"; };
		C6E3F4002ABC0C5300F4504A /* ring.caf */ = {isa = PBXFileReference; lastKnownFileType = file; path = ring.caf; sourceTree = "<group>"; };
		C6E3F4012ABC0C5300F4504A /* aurora.caf */ = {isa = PBXFileReference; lastKnownFileType = file; path = aurora.caf; sourceTree = "<group>"; };
		C6E3F4022ABC0C5300F4504A /* bell.caf */ = {isa = PBXFileReference; lastKnownFileType = file; path = bell.caf; sourceTree = "<group>"; };
		EF24D9522E1F4D808DB9712C /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; path = PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		504EC3011FED79650016851F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				41B735CE290FB88700EE39CE /* AVFoundation.framework in Frameworks */,
				41B735CA290FB86F00EE39CE /* UIKit.framework in Frameworks */,
				41B735D0290FB89200EE39CE /* CoreLocation.framework in Frameworks */,
				41B735D2290FB89800EE39CE /* CoreMedia.framework in Frameworks */,
				41B735BE290FB7E800EE39CE /* AVKit.framework in Frameworks */,
				CB50D6F62CF09631003E6998 /* VidyoClientIOS.xcframework in Frameworks */,
				41B735D4290FB8A200EE39CE /* SystemConfiguration.framework in Frameworks */,
				CB50D6F12CF0960A003E6998 /* VidyoClientIOS.xcframework in Frameworks */,
				41B735CC290FB88100EE39CE /* AudioToolbox.framework in Frameworks */,
				41B735C0290FB7FD00EE39CE /* Photos.framework in Frameworks */,
				142D17D7682287115D7E553A /* Pods_App.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		27E2DDA53C4D2A4D1A88CE4A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				41B735D3290FB8A200EE39CE /* SystemConfiguration.framework */,
				41B735D1290FB89800EE39CE /* CoreMedia.framework */,
				41B735CF290FB89200EE39CE /* CoreLocation.framework */,
				41B735CD290FB88700EE39CE /* AVFoundation.framework */,
				41B735CB290FB88100EE39CE /* AudioToolbox.framework */,
				41B735C9290FB86F00EE39CE /* UIKit.framework */,
				41B735C5290FB86300EE39CE /* VidyoClientIOS.xcframework */,
				41B735C1290FB85900EE39CE /* VidyoClientIOS.xcframework */,
				41B735BF290FB7FD00EE39CE /* Photos.framework */,
				41B735BD290FB7E800EE39CE /* AVKit.framework */,
				42A14D3BA95DA286AE71DCD3 /* Pods_App.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		41C4286F295AE7F300734363 /* sounds */ = {
			isa = PBXGroup;
			children = (
				41C42872295AE7F300734363 /* busy.mp3 */,
				C6E3F4012ABC0C5300F4504A /* aurora.caf */,
				C6E3F4022ABC0C5300F4504A /* bell.caf */,
				C6E3F3FF2ABC0C5300F4504A /* chime.caf */,
				C6E3F3FE2ABC0C5200F4504A /* glass.caf */,
				C6E3F4002ABC0C5300F4504A /* ring.caf */,
				C6E3F3FD2ABC0C5200F4504A /* tritone.caf */,
				41C42873295AE7F300734363 /* default.mp3 */,
				41C42874295AE7F300734363 /* calltone.mp3 */,
				41C42876295AE7F300734363 /* nouser.mp3 */,
				41C4287A295AE7F300734363 /* videocall.mp3 */,
			);
			path = sounds;
			sourceTree = "<group>";
		};
		504EC2FB1FED79650016851F = {
			isa = PBXGroup;
			children = (
				504EC3061FED79650016851F /* App */,
				504EC3051FED79650016851F /* Products */,
				27E2DDA53C4D2A4D1A88CE4A /* Frameworks */,
				EF24D9522E1F4D808DB9712C /* PrivacyInfo.xcprivacy */,
				E5E8500E435041E82D752264 /* Pods */,
			);
			sourceTree = "<group>";
		};
		504EC3051FED79650016851F /* Products */ = {
			isa = PBXGroup;
			children = (
				504EC3041FED79650016851F /* App.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		504EC3061FED79650016851F /* App */ = {
			isa = PBXGroup;
			children = (
				41C4286F295AE7F300734363 /* sounds */,
				41C80BE4290FD66300FE6DA0 /* App.entitlements */,
				41B735D7290FB8C500EE39CE /* App-Bridging-Header.h */,
				41B735D6290FB8C500EE39CE /* GoogleService-Info.plist */,
				41B735D9290FB8C500EE39CE /* IPluginEventHandler.swift */,
				41B735DA290FB8C500EE39CE /* VidyoClientWrapper.swift */,
				41B735D8290FB8C500EE39CE /* VidyoPlatformPlugin.swift */,
				41B735DB290FB8C500EE39CE /* VidyoPlatfromPlugin.m */,
				50379B222058CBB4000EE86E /* capacitor.config.json */,
				504EC3071FED79650016851F /* AppDelegate.swift */,
				504EC30B1FED79650016851F /* Main.storyboard */,
				504EC30E1FED79650016851F /* Assets.xcassets */,
				504EC3101FED79650016851F /* LaunchScreen.storyboard */,
				504EC3131FED79650016851F /* Info.plist */,
				2FAD9762203C412B000D30F8 /* config.xml */,
				50B271D01FEDC1A000F3C39B /* public */,
				41B735D5290FB8C400EE39CE /* App-Bridging-Header.h */,
			);
			path = App;
			sourceTree = "<group>";
		};
		E5E8500E435041E82D752264 /* Pods */ = {
			isa = PBXGroup;
			children = (
				2CC0DDFE5E38B4DBB4F70C66 /* Pods-App.debug.xcconfig */,
				3398852C0038A832975A555C /* Pods-App.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		504EC3031FED79650016851F /* App */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 504EC3161FED79650016851F /* Build configuration list for PBXNativeTarget "App" */;
			buildPhases = (
				4759F785EFA3A73A72B57F48 /* [CP] Check Pods Manifest.lock */,
				504EC3001FED79650016851F /* Sources */,
				504EC3011FED79650016851F /* Frameworks */,
				504EC3021FED79650016851F /* Resources */,
				84726FBA9AA1D2420F7C1D4F /* [CP] Embed Pods Frameworks */,
				84BFD65EE35C00174F10C9B2 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = App;
			productName = App;
			productReference = 504EC3041FED79650016851F /* App.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		504EC2FC1FED79650016851F /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 920;
				LastUpgradeCheck = 920;
				TargetAttributes = {
					504EC3031FED79650016851F = {
						CreatedOnToolsVersion = 9.2;
						LastSwiftMigration = 1320;
					};
				};
			};
			buildConfigurationList = 504EC2FF1FED79650016851F /* Build configuration list for PBXProject "App" */;
			compatibilityVersion = "Xcode 8.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 504EC2FB1FED79650016851F;
			productRefGroup = 504EC3051FED79650016851F /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				504EC3031FED79650016851F /* App */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		504EC3021FED79650016851F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C6E3F4042ABC0C5300F4504A /* glass.caf in Resources */,
				41C4287D295AE7F300734363 /* busy.mp3 in Resources */,
				504EC3121FED79650016851F /* LaunchScreen.storyboard in Resources */,
				41C4287E295AE7F300734363 /* default.mp3 in Resources */,
				41C42885295AE7F300734363 /* videocall.mp3 in Resources */,
				50B271D11FEDC1A000F3C39B /* public in Resources */,
				504EC30F1FED79650016851F /* Assets.xcassets in Resources */,
				C6E3F4052ABC0C5300F4504A /* chime.caf in Resources */,
				C6E3F4072ABC0C5300F4504A /* aurora.caf in Resources */,
				C6E3F4082ABC0C5300F4504A /* bell.caf in Resources */,
				50379B232058CBB4000EE86E /* capacitor.config.json in Resources */,
				41C42881295AE7F300734363 /* nouser.mp3 in Resources */,
				41C4287F295AE7F300734363 /* calltone.mp3 in Resources */,
				C6E3F4032ABC0C5300F4504A /* tritone.caf in Resources */,
				504EC30D1FED79650016851F /* Main.storyboard in Resources */,
				2FAD9763203C412B000D30F8 /* config.xml in Resources */,
				41B735DC290FB8C500EE39CE /* GoogleService-Info.plist in Resources */,
				C6E3F4062ABC0C5300F4504A /* ring.caf in Resources */,
				6099C71B23A747CDBD88CD8D /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		4759F785EFA3A73A72B57F48 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-App-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		84726FBA9AA1D2420F7C1D4F /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-App/Pods-App-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		84BFD65EE35C00174F10C9B2 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-App/Pods-App-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		504EC3001FED79650016851F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				41B735DE290FB8C500EE39CE /* IPluginEventHandler.swift in Sources */,
				41B735E0290FB8C500EE39CE /* VidyoPlatfromPlugin.m in Sources */,
				504EC3081FED79650016851F /* AppDelegate.swift in Sources */,
				41B735DF290FB8C500EE39CE /* VidyoClientWrapper.swift in Sources */,
				41B735DD290FB8C500EE39CE /* VidyoPlatformPlugin.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		504EC30B1FED79650016851F /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				504EC30C1FED79650016851F /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		504EC3101FED79650016851F /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				504EC3111FED79650016851F /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		504EC3141FED79650016851F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		504EC3151FED79650016851F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		504EC3171FED79650016851F /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2CC0DDFE5E38B4DBB4F70C66 /* Pods-App.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = App/App.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = ********;
				DEVELOPMENT_TEAM = "";
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				INFOPLIST_FILE = App/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = CitusHealth;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 5.72;
				OTHER_SWIFT_FLAGS = "$(inherited) \"-D\" \"COCOAPODS\" \"-DDEBUG\"";
				PRODUCT_BUNDLE_IDENTIFIER = com.citushealth.cb;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OBJC_BRIDGING_HEADER = "App/App-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		504EC3181FED79650016851F /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3398852C0038A832975A555C /* Pods-App.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = App/App.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = ********;
				DEVELOPMENT_TEAM = ZS7Q94X3G3;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				INFOPLIST_FILE = App/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = CitusHealth;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 5.72;
				PRODUCT_BUNDLE_IDENTIFIER = com.citushealth.cb;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "";
				SWIFT_OBJC_BRIDGING_HEADER = "App/App-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		504EC2FF1FED79650016851F /* Build configuration list for PBXProject "App" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				504EC3141FED79650016851F /* Debug */,
				504EC3151FED79650016851F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		504EC3161FED79650016851F /* Build configuration list for PBXNativeTarget "App" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				504EC3171FED79650016851F /* Debug */,
				504EC3181FED79650016851F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 504EC2FC1FED79650016851F /* Project object */;
}
